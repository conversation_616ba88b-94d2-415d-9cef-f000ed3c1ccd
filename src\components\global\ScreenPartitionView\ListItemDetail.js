import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue, useResetRecoilState, useRecoilState } from 'recoil';

// TODO: rename CancelButton inside @components/global/Buttons
import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import Tabs from '@components/global/Tabs/Tabs';
import { activeTabIndexAtom } from '@recoil/app';
import {
  adminInfoHasChangedState,
  adminInfoChangesState,
  refreshAdminListState,
} from '@recoil/admin';

import { useInvalidateSelector } from '@recoil/hooks';

import { ButtonContainer } from '@components/global/Form';

import Header from './ListItemDetailHeader';
import Body from './ListItemDetailBody';

const DetailContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const Footer = styled.div`
  display: flex;
  justify-content: space-between;
  flex: 0 1 60px;
  padding: 6px 12px;
  background-color: ${({ theme }) => theme.secondary[200]};
`;

const ListItemDetail = ({
  children,
  tabs,
  listItems,
  selectedItem,
  setSelectedItem,
  onSaveButtonClick,
  onDeleteButtonClick,
  headerText,
  detailText,
  itemId = 'oid', // Defaults to using 'oid' as the identifier for agents and users.
  showFooter,
}) => {
  const refreshList = useInvalidateSelector(refreshAdminListState);
  const [activeTab, setActiveTab] = useRecoilState(activeTabIndexAtom(['tabs']));
  const adminInfoChanged = useRecoilValue(adminInfoHasChangedState);
  const resetChanges = useResetRecoilState(adminInfoChangesState);

  const isCreate = !selectedItem?.[itemId];

  const discardChanges = () => {
    resetChanges();
    // If this is not a create, the previously selected item will be found in the .find
    const newSelectedItem = listItems.find(
      (listItem) => listItem[itemId] === selectedItem?.[itemId],
    );
    // If this is a create, then Discard Changes, return first item in the list
    const selectedItemValue = newSelectedItem || listItems[0];
    setSelectedItem(selectedItemValue);
  };

  const handleSaveButtonClick = async () => {
    const savedSuccessfully = await onSaveButtonClick();
    if (!savedSuccessfully) return;

    resetChanges();
    refreshList();
  };

  const handleDeleteButtonClick = async () => {
    const deletedSuccessfully = await onDeleteButtonClick();
    if (!deletedSuccessfully) return;

    resetChanges();
    setActiveTab(0);
    refreshList();
  };

  return (
    <DetailContainer>
      <Header headerText={headerText} detailText={detailText} />
      <Body>
        {tabs ? (
          <>
            <Tabs tabs={tabs} />
            {tabs[activeTab].component}
          </>
        ) : (
          children
        )}
      </Body>
      {showFooter && (
        <Footer>
          <CancelButton onClick={handleDeleteButtonClick} disabled={isCreate}>
            Delete
          </CancelButton>

          <ButtonContainer>
            <PrimaryButton disabled={!adminInfoChanged} onClick={handleSaveButtonClick}>
              {isCreate ? 'Create' : 'Save'}
            </PrimaryButton>
            <CancelButton disabled={!adminInfoChanged} onClick={discardChanges}>
              Discard Changes
            </CancelButton>
          </ButtonContainer>
        </Footer>
      )}
    </DetailContainer>
  );
};

ListItemDetail.propTypes = {
  children: PropTypes.node,
  tabs: PropTypes.arrayOf(PropTypes.shape({ component: PropTypes.node })),
  listItems: PropTypes.arrayOf(PropTypes.shape({})),
  selectedItem: PropTypes.shape({ oid: PropTypes.string }),
  setSelectedItem: PropTypes.func,
  onSaveButtonClick: PropTypes.func,
  onDeleteButtonClick: PropTypes.func,
  headerText: PropTypes.string,
  detailText: PropTypes.string,
  itemId: PropTypes.string,
  showFooter: PropTypes.bool,
};

ListItemDetail.defaultProps = {
  children: null,
  tabs: null,
  listItems: [],
  selectedItem: {},
  setSelectedItem: (selectedItem) => {
    console.log('No setSelectedItem configured. Tried to set:');
    console.log(selectedItem);
  },
  onSaveButtonClick: () => {
    console.log('No save button click configured');
  },
  onDeleteButtonClick: () => {
    console.log('No delete button click configured');
  },
  headerText: '',
  detailText: '',
  itemId: undefined,
  showFooter: true,
};

export default ListItemDetail;
