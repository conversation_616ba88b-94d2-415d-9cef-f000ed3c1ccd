import React, { useState, useEffect, useRef } from 'react';
import { useRecoilValue, useRecoilState, useResetRecoilState } from 'recoil';
import styled from 'styled-components';
import moment from 'moment';

import { authorizedDepartmentsSelector } from '@recoil/app';
import { agentsSelectorFamily } from '@recoil/agents';
import { selectedPatternState } from '@recoil/eventPatterns';

import {
  FormInput,
  FormSelect,
  FormMultiselect,
  FormDatePicker,
  FormDateTimePicker,
  handleFormFieldChange,
  GoogleAddressInput,
  ButtonContainer,
} from '@components/global/Form';
import { PrimaryButton, CancelButton } from '@components/global/Buttons';
import { PatternsManager } from '@utils/APIManager';
import { useInvalidateSelector } from '@recoil/hooks';
import refreshPatternsState from '@recoil/eventPatterns/refreshPatternsState';

import { parseGoogleAutocomplete } from '@utils/functions';

const PatternsFormContainer = styled.div`
  padding: 16px;
`;

const ActionButtonsContainer = styled(ButtonContainer)`
  height: 50px;
`;

const EventPatternsForm = () => {
  const [selectedPattern, setSelectedPattern] = useRecoilState(selectedPatternState);

  const {
    patternId,
    patternName,
    departmentId,
    oids,
    repeatInterval,
    startDate,
    endDate,
    startTime,
    endTime,
    eventType,
    address,
  } = selectedPattern;

  const prevState = useRef();

  useEffect(() => {
    prevState.selectedPattern = selectedPattern;
  }, [selectedPattern]);

  const [addressInvalid, setAddressInvalid] = useState(false);
  const departmentOptions = useRecoilValue(authorizedDepartmentsSelector);
  const resetSelectedPattern = useResetRecoilState(selectedPatternState);
  const refreshPatterns = useInvalidateSelector(refreshPatternsState);

  // AgentsSelectorFamily expects department name, not id
  // departmentOptions has the ID as the value, so we need to find the key instead
  const { key: departmentName } = departmentOptions.find(
    ({ value: optionId }) => optionId === parseInt(departmentId, 10),
  );

  const agents = useRecoilValue(agentsSelectorFamily({ MA: [departmentName] }));

  const isCreate = !patternId;

  const agentOptions = agents.map(({ displayName, oid }) => {
    return { key: displayName, value: oid };
  });

  // TODO: what should these be?
  const eventTypeOptions = [
    { key: 'Custom Block', value: '999999' },
    // { key: 'Regional Meeting', value: '999997' },
    // { key: 'One on One', value: '999996' },
    // { key: 'Other', value: null }, // TODO: is this necessary?
  ];

  const repeatIntervalOptions = [
    { key: 'Weekly', value: '7 days' },
    { key: 'BiWeekly', value: '14 days' },
    { key: 'Every 4 Weeks', value: '28 days' },
    { key: 'Monthly', value: '1 month' },
  ];

  const getNewEndDate = (startDateMoment = startDate, intervalString = repeatInterval) => {
    const [intervalNumber, intervalType] = [
      parseInt(intervalString, 10), // ie: 2
      `${intervalString}`.replace(/[^a-zA-Z]/g, ''), // ie: weeks
    ];

    return moment(startDateMoment).add(intervalNumber, intervalType);
  };

  const handleRepeatIntervalChange = (e) => {
    const { value: newInterval } = e.target;

    const newEndDate = getNewEndDate(undefined, newInterval);

    return handleFieldChange(e, { ...selectedPattern, endDate: newEndDate });
  };

  const handleStartDateChange = (e) => {
    const { value: newStartDate } = e.target;

    const newEndDate = getNewEndDate(newStartDate);
    return handleFieldChange(e, { ...selectedPattern, endDate: newEndDate });
  };

  const handleStartTimeChange = (e) => {
    const { value: newStartTime } = e.target;
    const newEndTime = moment(newStartTime, 'HH:mm:ss')
      .add(30, 'minutes')
      .format('HH:mm:ss');
    return handleFieldChange(e, { ...selectedPattern, endTime: newEndTime });
  };

  const handleFieldChange = (e, updatedPattern = prevState.selectedPattern) => {
    return handleFormFieldChange(e, updatedPattern, setSelectedPattern);
  };

  // TODO: the following two functions are used by the custom block form as well
  // Eventually, this logic should probably be handled by the googleAutocomplete component
  // as there's some overhead here that needs to happen every time
  // Only used for address change for google auto complete
  const handleAddressChange = (e) => {
    handleFieldChange(e);

    const { value: address } = e.target;
    // Sets address invalid if the value exists. This would mean theyve changed the address without selecting it from the autocomplete
    // Sets address valid if value does not exist. This would mean they removed the address from the event.
    setAddressInvalid(!!address);
  };

  // Used when you click an address in the google auto complete
  const handleGoogleLocationInput = (autocomplete) => {
    const updateObject = parseGoogleAutocomplete(autocomplete);

    handleFieldChange({ target: { name: 'address', value: updateObject } });
    setAddressInvalid(false);
  };

  const createPattern = async () => {
    await PatternsManager.createPattern(selectedPattern);
    refreshPatterns();
  };

  const savePattern = async () => {
    await PatternsManager.updatePattern(selectedPattern);
    refreshPatterns();
  };

  const deletePattern = async () => {
    await PatternsManager.deletePattern(selectedPattern);
    resetSelectedPattern();
    refreshPatterns();
  };

  return (
    <PatternsFormContainer>
      <FormInput
        title="Pattern Name"
        name="patternName"
        value={patternName}
        onChange={handleFieldChange}
        required
      />
      <FormSelect
        title="Department"
        name="departmentId"
        value={departmentId}
        options={departmentOptions}
        onChange={handleFieldChange}
        required
      />
      <FormMultiselect
        title="HES/HCS"
        name="oids"
        value={oids}
        options={agentOptions}
        onChange={handleFieldChange}
        required
      />
      <FormSelect
        title="Repeat Interval"
        name="repeatInterval"
        value={repeatInterval}
        options={repeatIntervalOptions}
        onChange={handleRepeatIntervalChange}
        required
      />
      <FormDatePicker
        title="Start Date"
        name="startDate"
        value={startDate}
        onChange={handleStartDateChange}
        required
        useSyntheticEvent
      />
      <FormDatePicker
        title="End Date"
        name="endDate"
        value={endDate}
        minDate={startDate}
        onChange={handleFieldChange}
        required
        useSyntheticEvent
      />
      <FormDateTimePicker
        title="Start Time"
        name="startTime"
        value={moment(startTime, 'HH:mm:ss')}
        timeFormat="HH:mm:ss"
        dateFormat="h:mm a"
        displayTimeFormat="h:mm a"
        onChange={handleStartTimeChange}
        required
        useSyntheticEvent
      />
      <FormDateTimePicker
        title="End Time"
        name="endTime"
        value={moment(endTime, 'HH:mm:ss')}
        timeFormat="HH:mm:ss"
        dateFormat="h:mm a"
        displayTimeFormat="h:mm a"
        minTime={moment(startTime, 'HH:mm:ss')}
        onChange={handleFieldChange}
        required
        useSyntheticEvent
      />
      <GoogleAddressInput
        title="Address"
        name="address"
        value={address}
        onPlaceChange={(autocomplete) => handleGoogleLocationInput(autocomplete)}
        onChange={handleAddressChange}
        isInvalid={addressInvalid}
        uniqueId="address"
      />
      <FormSelect
        title="Type"
        name="eventType"
        value={eventType}
        options={eventTypeOptions}
        onChange={handleFieldChange}
        required
      />

      {isCreate ? (
        <ActionButtonsContainer marginDirections={['right', 'bottom']}>
          <PrimaryButton onClick={() => createPattern()}>Create Pattern</PrimaryButton>
        </ActionButtonsContainer>
      ) : (
        <ActionButtonsContainer marginDirections={['right', 'bottom']}>
          <PrimaryButton onClick={() => savePattern()}>Save Pattern</PrimaryButton>
          <CancelButton onClick={() => deletePattern()}>Delete Pattern</CancelButton>
        </ActionButtonsContainer>
      )}
    </PatternsFormContainer>
  );
};

EventPatternsForm.propTypes = {};
EventPatternsForm.defaultProps = {};

export default EventPatternsForm;
