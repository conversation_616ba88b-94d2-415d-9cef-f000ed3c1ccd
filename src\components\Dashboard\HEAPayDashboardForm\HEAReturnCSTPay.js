import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAReturnCSTPay = ({ record }) => {
  const { dealId, returnVisitCstIncentive, operationsName } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="operationsName"
            value={operationsName}
            title="Operation Name"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="returnVisitCstIncentive"
            value={returnVisitCstIncentive}
            title="Return Visit CST Incentive"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HEAReturnCSTPay.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    returnVisitCstIncentive: PropTypes.string,
    operationsName: PropTypes.string,
  }),
};

HEAReturnCSTPay.defaultProps = {
  record: {},
};

export default HEAReturnCSTPay;
