import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import FormFieldLabel from './FormFieldLabel';

const FormInfoFieldContainer = styled.div`
  margin-bottom: 5px;
`;

const FormInfoFieldBody = styled.h4`
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
`;

const FormInfoField = ({ title, body }) => {
  if (!title) return null;
  return (
    <FormInfoFieldContainer>
      <FormFieldLabel>{title}</FormFieldLabel>
      <FormInfoFieldBody>{body}</FormInfoFieldBody>
    </FormInfoFieldContainer>
  );
};

FormInfoField.propTypes = {
  title: PropTypes.string,
  body: PropTypes.string,
};

FormInfoField.defaultProps = {
  title: null,
  body: null,
};

export default FormInfoField;
