import React, { useState } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { Broadcast } from '@styled-icons/bootstrap/Broadcast';

import Tooltip from '../Tooltip/Tooltip';

const IconStyled = styled(Broadcast)`
  height: 30px;
`;

const ScheduledOnlineContainer = styled.div`
  position: relative;
  height: 100%;
`;

const ScheduledOnline = ({ hoverText }) => {
  const [isHovering, setIsHovering] = useState(false);
  return (
    <ScheduledOnlineContainer
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <IconStyled /> {hoverText && isHovering && <Tooltip text={hoverText} />}
    </ScheduledOnlineContainer>
  );
};

ScheduledOnline.propTypes = {
  hoverText: PropTypes.string,
};

ScheduledOnline.defaultProps = {
  hoverText: null,
};

export default ScheduledOnline;
