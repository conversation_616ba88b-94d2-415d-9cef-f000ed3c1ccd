import React, { useState } from 'react';
import { useRecoilValue } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { CaretRightFill } from '@styled-icons/bootstrap/CaretRightFill';
import { HorizontalLine } from '@components/global';
import { formRecoilState, formSettingsState } from '@recoil/dataIntakeForm';
import DataIntakeFormFields from './DataIntakeFormFields';

const SectionContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const SectionHeader = styled.div`
  font-weight: 700;
  font-size: 15px;
  margin-bottom: 8px;
  margin-top: 10px;
  flex: ${(props) => props.sectionHeader || 'unset'};
`;

const CollapseContainer = styled.div`
  display: flex;
  align-items: center;
`;

const CollapseFieldsContainer = styled.div``;

const CollapseArrowIcon = styled(CaretRightFill)`
  height: 12px;
  margin-right: 2px;
  /* With Styled Component React does not recognize prop as boolean on DOM Element */
  transform: ${({ collapsed }) => (collapsed === 'true' ? 'none' : 'rotate(0.25turn)')};
  transition: transform 0.2s;
`;

const DataIntakeFormSection = (props) => {
  const { numUnit } = useRecoilValue(formSettingsState);
  const formValues = useRecoilValue(formRecoilState);
  const {
    text,
    fields,
    perUnit,
    collapse,
    defaultCollapsed,
    conditional,
    unitNum,
    readOnly,
  } = props;
  const { style = {} } = fields?.[0];
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  if (conditional && !conditional(formValues)) return null;

  // Add the unit number to repeated fields in perUnit section
  // This way, the DataIntakeFormFields component knows that it doesn't need to repeat the field to get the unit number
  // if (unitNum)
  //   fields = fields.map((field) => {
  //     return { ...field, unitNum };
  //   });

  // If it is per unit, and we don't have a unit number yet, render an instance of this component for each unit.
  // We will have a unit number once we do this, so we can just render as normal below
  if (perUnit && !unitNum) {
    return (
      <SectionContainer key={text} style={style}>
        <SectionHeader sectionHeader={style?.sectionHeader}>{text}</SectionHeader>
        {[...Array(numUnit)].map((_, index) => {
          const unitNum = index + 1;
          return (
            <DataIntakeFormSection
              {...props}
              key={`${text}${unitNum}`}
              text={`Unit ${unitNum}`}
              unitNum={unitNum}
              readOnlyForReview={readOnly}
            />
          );
        })}
      </SectionContainer>
    );
  }
  if (collapse)
    return (
      <>
        <HorizontalLine />
        <SectionContainer key={text}>
          <CollapseContainer onClick={() => setIsCollapsed(!isCollapsed)}>
            <CollapseArrowIcon collapsed={isCollapsed.toString()} />
            <SectionHeader>{text}</SectionHeader>
          </CollapseContainer>
          {!isCollapsed && (
            <CollapseFieldsContainer>
              <DataIntakeFormFields
                isCollapsed={isCollapsed}
                fields={fields}
                unitNum={unitNum}
                readOnlyForReview={readOnly}
              />
            </CollapseFieldsContainer>
          )}
        </SectionContainer>
      </>
    );

  return (
    <SectionContainer key={text}>
      <div>
        <SectionHeader>{text}</SectionHeader>
        <DataIntakeFormFields fields={fields} unitNum={unitNum} readOnlyForReview={readOnly} />
      </div>
    </SectionContainer>
  );
};

DataIntakeFormSection.propTypes = {
  text: PropTypes.string,
  fields: PropTypes.arrayOf(PropTypes.shape({})),
  sectionType: PropTypes.string,
  perUnit: PropTypes.bool,
  unitNum: PropTypes.number,
  collapse: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  conditional: PropTypes.func,
  numUnit: PropTypes.number,
  readOnly: PropTypes.bool,
};

DataIntakeFormSection.defaultProps = {
  text: '',
  fields: [],
  sectionType: '',
  perUnit: false,
  unitNum: null,
  collapse: false,
  defaultCollapsed: true,
  conditional: null,
  numUnit: 1,
  readOnly: false,
};

export default DataIntakeFormSection;
