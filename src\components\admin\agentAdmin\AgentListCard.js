import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useParams } from 'react-router-dom';
import { useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';

import { ListCard } from '@components/global/ScreenPartitionView';
import { selectedAgentAtom } from '@recoil/admin/agents';
import { adminInfoChangesState } from '@recoil/admin';
import { activeTabIndexAtom } from '@recoil/app';

const getNameInitials = (firstname, lastname) => {
  const firstnameInitial = firstname?.[0]?.toUpperCase() || '';
  const lastnameInitial = lastname?.[0]?.toUpperCase() || '';
  return `${firstnameInitial}${lastnameInitial}`;
};

const DepartmentText = styled.div`
  color: ${({ theme }) => theme.secondary[500]};
`;

const RegionText = styled.span`
  font-weight: 500;
  margin-right: 5px;
`;

const RegCompContainer = styled.p`
  color: ${({ theme }) => theme.secondary[500]};
  margin-bottom: 0rem;
`;

const AgentListCard = (props) => {
  const { agent, showDepartment, showCompany } = props;

  const {
    oid,
    displayName,
    firstname,
    lastname,
    department,
    departmentName,
    companyName,
    regionAbbreviation,
    state,
  } = agent;

  const {
    oid: selectedAgentOid,
    department: selectedAgentDepartment,
    state: selectedAgentState,
  } = useRecoilValue(selectedAgentAtom);
  const { oid: urlOid } = useParams();
  const setActiveTab = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const setSelectedCrew = useSetRecoilState(selectedAgentAtom);
  const resetAgentInfoChanges = useResetRecoilState(adminInfoChangesState);

  const handleSelectCrew = () => {
    setSelectedCrew(agent);
    setActiveTab(0);
    resetAgentInfoChanges();
  };

  if (urlOid === oid) handleSelectCrew();

  const initials = getNameInitials(firstname, lastname);

  const isActive =
    oid === selectedAgentOid &&
    department === selectedAgentDepartment &&
    state === selectedAgentState;

  const DetailsNode = (
    <>
      <RegCompContainer>
        <RegionText>{regionAbbreviation ? `${regionAbbreviation} |` : '- |'}</RegionText>
        {showCompany && companyName}
      </RegCompContainer>
      {showDepartment && <DepartmentText>{departmentName}</DepartmentText>}
    </>
  );

  return (
    <ListCard
      onClick={handleSelectCrew}
      active={isActive}
      iconText={initials}
      title={displayName}
      detailsNode={DetailsNode}
    />
  );
};

AgentListCard.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
  agent: PropTypes.shape({
    oid: PropTypes.string.isRequired,
    firstname: PropTypes.string,
    lastname: PropTypes.string,
    displayName: PropTypes.string.isRequired,
    regionAbbreviation: PropTypes.string,
    departmentName: PropTypes.string,
    department: PropTypes.number,
    companyName: PropTypes.string,
    state: PropTypes.string,
  }).isRequired,

  showCompany: PropTypes.bool,
  showDepartment: PropTypes.bool,
};

AgentListCard.defaultProps = {
  showCompany: false,
  showDepartment: false,
  location: { search: null },
};

export default AgentListCard;
