/**
 *
 * @param {string} eventType Event type string
 * Example: '005000'
 */
const getEventColorForEventType = (eventType, theme) => {
  const colorDict = {
    '00': {
      '00': theme.colors.eventA,
      '01': theme.colors.eventC,
      '02': theme.colors.eventB,
      '03': theme.colors.eventF,
      '04': theme.colors.eventJ,
      '05': theme.colors.eventJ,
      '06': theme.colors.eventN,
    },
    '05': {
      '00': theme.colors.eventA,
      '01': theme.colors.eventC,
      '02': theme.colors.eventC,
      '03': theme.colors.eventB,
      '04': theme.colors.eventA,
      '05': theme.colors.eventB,
      '06': theme.colors.eventB,
      '07': theme.colors.eventM,
    },
    '99': {
      '99': theme.colors.customBlock,
    },
  };
  if (eventType?.length !== 6) return theme.colors.eventGreen;
  const business = eventType.slice(2, 4);
  const businessEvent = eventType.slice(4, 6);
  return colorDict[business] && colorDict[business][businessEvent]
    ? colorDict[business][businessEvent]
    : theme.colors.eventGreen;
};

export default getEventColorForEventType;
