import { atom, selector, DefaultValue } from 'recoil';
import { calendarTypeAtom } from '@recoil/app';

const defaultValues = {
  id: null,
  type: '',
  numUnit: 1,
  status: 'Sent for Inspection',
  oids: [],
  oid: '',
  sfIds: { accountId: '', siteId: null },
  paperWorkStatus: '',
  remediationEstimation: '',
  notes: { hweNotes: '', partnerNotes: '' },
  inspectionScheduledDate: null,
  remediationStartDate: null,
  remediationEndDate: null,
  scheduledStartDate: null,
  scheduledEndDate: null,
  contact_dates: {
    dateContact: null,
    dateContact2: null,
    dateContact3: null,
    dateContact4: null,
    dateContact5: null,
    deferredCallBackDate: null,
  },
  scheduledBy: '',
  scheduledDate: null,
  lastModified: null,
  customerName: '',
  email: '',
  phoneNumber: '',
  secondaryPhoneNumber: '',
  address: null,
  archive: false,
  barrierTypes: [],
  isCap: false,
  accountName: '',
  halted: null,
};

const selectedPartnerEventFieldStates = {};

// Create a separate atom for each field on the selectedPartnerEvent.
// This way, changing the customerName doesn't need to rerender the siteId
// We can just update the customerName atom without effecting the siteId atom
Object.keys(defaultValues).forEach((fieldName) => {
  selectedPartnerEventFieldStates[fieldName] = atom({
    key: `selectedPartnerEventEvent-${fieldName}Atom`,
    default: defaultValues[fieldName],
  });
});

const selectedPartnerEventSelector = selector({
  key: 'selectedPartnerEventSelector',
  get: ({ get }) => {
    const selectedPartnerEvent = {};
    const propertyNames = Object.keys(selectedPartnerEventFieldStates);

    // Get value of each atom, then return together in an object
    propertyNames.forEach((propertyName) => {
      selectedPartnerEvent[propertyName] = get(selectedPartnerEventFieldStates[propertyName]);
    });

    return selectedPartnerEvent;
  },
  set: ({ get, set, reset }, newValue) => {
    // Handle Resetting selected event
    if (newValue instanceof DefaultValue) {
      const calendarType = get(calendarTypeAtom);
      const propertyNames = Object.keys(selectedPartnerEventFieldStates);
      propertyNames.forEach((propertyName) => {
        if (propertyName === 'type')
          set(selectedPartnerEventFieldStates[propertyName], calendarType);
        else reset(selectedPartnerEventFieldStates[propertyName]);
      });
      return;
    }

    // New values we are trying to update.
    // Don't need to pass the { ...selectedPartnerEvent, [updateField]: changedValue }
    // Since we only update the properties that are present in the newValue object
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      // We can only update atoms that we have created above. Each property needs a default value if it should be updated
      // Might also need one just to access the property.
      // Previously, when selecting an event on the calendar, it put the whole object from the backend into state.
      // This means that all properties on that object were automatically accounted for, and set to state even if they were undefined to start.
      // Now, we have no way of setting an unknown property, since there are individual atoms for each.
      // If necessary, we could potentially create a new atom and add it to the selectedPartnerEventFieldStates here
      // But I think that it might be better just to enforce that all properties are created to start with
      if (!selectedPartnerEventFieldStates[propertyName]) return;
      set(selectedPartnerEventFieldStates[propertyName], newValue[propertyName]);
    });
  },
});

export default selectedPartnerEventSelector;
