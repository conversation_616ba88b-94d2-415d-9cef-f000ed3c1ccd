// eslint-disable-next-line no-unused-vars
import React from 'react';
import moment from 'moment';
import Swal from 'sweetalert2/dist/sweetalert2';
import ReactDOMServer from 'react-dom/server';
import { throwError } from '@utils/EventEmitter';
import styled from 'styled-components';
import Cookies from 'js-cookie';
import { isEmpty } from 'lodash';

import { CalendarCheck } from '@styled-icons/bootstrap/CalendarCheck';
import { CalendarX } from '@styled-icons/bootstrap/CalendarX';
import { CalendarWeek } from '@styled-icons/bootstrap/CalendarWeek';
import { DollarCircle } from '@styled-icons/boxicons-regular/DollarCircle';

import { salesforceUrls } from '@utils/constants';

const CalendarWeekIcon = styled(CalendarWeek)`
  color: ${({ theme }) => theme.colors.eventC};
`;
const CalendarXIcon = styled(CalendarX)`
  color: ${({ theme }) => theme.colors.eventJ};
`;
const CalendarCheckIcon = styled(CalendarCheck)`
  color: ${({ theme }) => theme.colors.actionButton};
`;
const CapIncSchIcon = styled(DollarCircle)`
  color: ${({ theme }) => theme.colors.customBlock};
`;
const CapIncNotCompIcon = styled(DollarCircle)`
  color: ${({ theme }) => theme.colors.eventJ};
`;
const CapIncCompIcon = styled(DollarCircle)`
  color: ${({ theme }) => theme.colors.eventM};
`;

const isArray = (a) => {
  return Array.isArray(a);
};

const isObject = (o) => {
  return o === Object(o) && !isArray(o) && typeof o !== 'function' && Object.values(o).length;
};

const isNumeric = (n) => {
  return typeof n === 'number' && !Number.isNaN(n);
};

const getNestedFieldValueWithPath = (object, path) => {
  const fieldPathArray = typeof path === 'string' ? path.split('.') : path;

  const returnValue = fieldPathArray.reduce((acc, pathSegment) => {
    const nextStepInPath = acc[pathSegment];
    if (nextStepInPath === undefined) throw Error(`Couldnt find any value for path ${path}`);
    return nextStepInPath;
  }, object);

  return returnValue;
};

const updateNestedObjectFieldWithPath = (object, pathToUpdate, newValue, mutateOriginal) => {
  const fieldPathArray = typeof pathToUpdate === 'string' ? pathToUpdate.split('.') : pathToUpdate;
  const [nextPathSegment] = fieldPathArray;
  const returnObject = mutateOriginal ? object : { ...object };
  fieldPathArray.shift();

  returnObject[nextPathSegment] = fieldPathArray.length
    ? updateNestedObjectFieldWithPath(returnObject[nextPathSegment], fieldPathArray, newValue)
    : newValue;
  return returnObject;
};

// TODO: This is to deep clone arrays. Couldn't use json stringify/parse because
// my array had moment objects in it. This is not bulletproof, we will likely need
// to add to it to handle other use cases. I unfortunately don't have time to spend
// on it now. I think lodash has some deep clone functionality, it might be something
// to look into.
const deepClone = (items) =>
  items.map((item) => {
    if (Array.isArray(item)) return deepClone(item);
    if (item && typeof item === 'object') return { ...item };
    return item;
  });

const formatCurrency = (amount) => {
  // Checks for falsy values like "null" null "undefined" undefined
  const parsedAmount = Number(amount) || 0;
  // Double parse float since '.toFixed' and '.toLocaleString' both return strings
  return parseFloat(parseFloat(parsedAmount).toFixed(2)).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  });
};

const getCityAndZipFromAddress = (address) => {
  if (!address) return address;
  const [zip] = address.match(/[0-9]{5}/) || [null];
  let [, formattedAddress] = address.split(',');
  if (!formattedAddress) return address;

  formattedAddress = formattedAddress.replace(/[0-9]{5}/, '').replace(/\s[A-Z]{2}\s/, ''); // Remove zip and state abbreviation
  formattedAddress = `${formattedAddress}${zip && `, ${zip}`}`;
  return formattedAddress;
};

const renderStartAndEndTimes = (event) => {
  const { startTime, endTime } = event;
  const formatTime = (timeString) => moment(timeString, 'HH:mm:ss a').format('h:mm a');
  const [formattedStartTime, formattedEndTime] = [formatTime(startTime), formatTime(endTime)];
  return `${formattedStartTime} - ${formattedEndTime}`;
};

// We don't currently support events across weeks.
const verifySingleWeek = (startDate, jobLength) => {
  // Get job start and end times
  const startMoment = moment(startDate, 'MM/DD/YYYY');
  // jobLength - 1 since a 1 day event doesn't need to worry about the next day
  const endMoment = moment(startDate, 'MM/DD/YYYY').add(jobLength - 1, 'days');

  return startMoment.isSame(endMoment, 'week');
};

const chopString = (str, length) => {
  let chopped = str;
  if (str.length > length) chopped = `${str.slice(0, length - 2)}...`;
  return chopped;
};

const toggleSidebar = () => {
  const [sideBarNode] = document.getElementsByClassName('side-bar');
  const [clickOutsideNode] = document.getElementsByClassName('click-outside');
  if (sideBarNode.classList.contains('closed')) {
    sideBarNode.classList.remove('closed');
    clickOutsideNode.classList.remove('closed');
  } else {
    sideBarNode.classList.add('closed');
    clickOutsideNode.classList.add('closed');
  }
};

const getSalesforceUrl = (state = 'MA') => {
  const devSalesforceUrl = state === 'CT' ? salesforceUrls.dev2 : salesforceUrls.dev;
  const prodSalesforceUrl = state === 'CT' ? salesforceUrls.prod2 : salesforceUrls.prod;

  const host = window.location.origin;

  const salesforceUrl =
    host === 'https://sch.homeworksenergy.com' ? prodSalesforceUrl : devSalesforceUrl;

  return salesforceUrl;
};

const preventParentElementClick = (event) => {
  event.stopPropagation();
  // eslint-disable-next-line no-param-reassign
  event.cancelBubble = true;
};

const openNewTabWithUrl = (url) => {
  window.open(url, '_blank');
};

const openEmailClient = (email) => {
  window.open(`mailto:${email}`);
};
const openPhoneCall = (phoneNumber) => {
  document.location.href = `tel:+1${phoneNumber}`;
};

const displayPhoneNumber = (phoneNumber) => {
  return phoneNumber
    ? `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`
    : '';
};

const getGoogleMapsDirectionsUrl = (address) =>
  `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(address)}`;

const getEventDuration = (startTime, endTime) =>
  moment.duration(moment(endTime, 'hh:mm:ss').diff(moment(startTime, 'hh:mm:ss'))).asHours();

const removeSpecialCharacters = (str) => {
  return str.replace(/[^a-zA-Z0-9]/g, '');
};

const formatZipCode = (zipCode) => {
  return zipCode ? removeSpecialCharacters(zipCode).slice(0, 5) : '-';
};

const capitalizeFirstLetterOfString = (string) =>
  `${string.charAt(0).toUpperCase()}${string.slice(1)}`;

const urlParamsToJson = (params) => {
  const urlParams = new URLSearchParams(params);
  const result = {};
  urlParams.forEach((value, key) => {
    if (value === 'true') result[key] = true;
    if (value === 'false') result[key] = false;
    else result[key] = value;
  });
  return result;
};

const getEventColor = (eventTypeName, wxVisitResult, classification = null, theme) => {
  const classificationColors = {
    'Income Eligible': {
      Complete: theme.colors.eventE,
      '': theme.colors.eventE,
      Walk: theme.colors.red,
      Reschedule: theme.colors.eventD,
      'Building Inspector No Show': theme.colors.red,
      'Customer No Show': theme.colors.red,
    },
    Partner: theme.colors.eventL,
  };
  const eventColors = {
    'Insulation Install': {
      Walk: theme.colors.red,
      Complete: theme.colors.eventD,
      Reschedule: theme.colors.eventD,
      Partner: theme.colors.eventL,
      'Building Inspector No Show': theme.colors.red,
      'Customer No Show': theme.colors.red,
      default: theme.colors.eventA,
    },
    'Insulation Finish': { default: theme.colors.eventF },
    'Insulation Internal Callback': { default: theme.colors.eventC },
    'Insulation Callback': { default: theme.colors.eventC },
    'Insulation Manager Visit': {
      Walk: theme.colors.red,
      Complete: theme.colors.eventD,
      Reschedule: theme.colors.eventD,
      'Building Inspector No Show': theme.colors.red,
      'Customer No Show': theme.colors.red,
      default: theme.colors.eventB,
    },
    'Insulation Manager QC Visit': { default: theme.colors.eventB },
    'Insulation Truck Service': { default: theme.colors.eventB },
    'Insulation Manager Permit Inspection': { default: theme.colors.eventM },
    default: theme.colors.eventA,
  };
  if (classification)
    return (
      classificationColors?.[classification]?.[wxVisitResult] ||
      classificationColors?.[classification]
    );

  if (wxVisitResult)
    return eventColors?.[eventTypeName]?.[wxVisitResult] || eventColors?.[eventTypeName]?.default;

  return eventColors?.[eventTypeName]?.default || eventColors?.default;
};

const parseGoogleAutocomplete = (autocomplete) => {
  const { geometry, address_components: addressComponents } = autocomplete.getPlace();

  // eslint-disable-next-line one-var
  let streetNum, streetName, city, state, postalCode, country;
  for (let k = 0; k < addressComponents.length; k++) {
    const { types, long_name: longName, short_name: shortName } = addressComponents[k];
    if (types.includes('street_number')) streetNum = longName;
    if (types.includes('route')) streetName = shortName;

    // Cities can come in a couple of different ways.
    // For example, 'Charlestown' doesn't come through as a locality, only as ['political', 'neighborhood']
    // A location in 'Dorchester' however, comes through with ['political', 'neighborhood'] as 'Dorchester', but ['political', 'locality'] as 'Boston'
    // This way the city will be captured as the more specific of the two options
    if (types.includes('locality')) city = longName;
    if (types.includes('neighborhood')) city = longName;

    if (types.includes('administrative_area_level_1')) state = shortName;
    if (types.includes('postal_code')) postalCode = longName;
    if (types.includes('country')) country = shortName;
  }
  const latitude = geometry.location.lat();
  const longitude = geometry.location.lng();

  const street = `${streetNum} ${streetName}`;

  const updateObject = {
    addressKey: `${street} ${city} ${state} ${postalCode}`.toLowerCase(),
    displayAddress: `${street}, ${city}, ${state} ${postalCode}, ${country}`,
    street,
    city,
    state,
    postalCode,
    latitude,
    longitude,
  };

  return updateObject;
};

const getAlphanumericString = (string) => {
  return string
    .replace(/[^a-zA-Z0-9 ]/g, '')
    .trim()
    .replace(/\s+/g, ' ');
};

// https://stackoverflow.com/questions/4346186/how-to-determine-if-a-function-is-empty
const isFunctionEmpty = (func) => {
  return /^function[^{]+\{\s*\}/m.test(func.toString());
};

const copyDealId = (dealId = '') => {
  const dealIdLength = dealId.length;
  if (dealIdLength >= 15) {
    copyTextToClipboard(dealId);
  } else throwError('Invalid Deal Id\n Deal Id not copied.');
};

const getZipcodeFromAddress = (address) => (address ? `${formatZipCode(address.postalCode)}` : '');

const getCityAndZipcodeFromAddress = (address) =>
  address ? `${address.city}, ${formatZipCode(address.postalCode)}` : '';

const copyTextToClipboard = (text, showSuccess = false) => {
  navigator.clipboard.writeText(text);
  if (showSuccess) {
    return Swal.fire({
      title: 'Successfully Copied',
    });
  }
  return true;
};

/**
 * Redirect To Schedule Weatherization Visit
 * @param {string} operationsId SalesForce Operations Id string
 * Example: 'a0o4X00000JbdOkQAJ'
 */
// eslint-disable-next-line consistent-return
const redirectScheduleWx = (operationsId) => {
  if (!operationsId || ![15, 18].includes(operationsId.length)) {
    return Swal.fire({
      type: 'error',
      title: 'Invalid Operations Id',
      text: `This visit has a invalid Operations Id. <NAME_EMAIL> with the Operations Id and Event Information you are having an issue with. Operations ID: ${operationsId}`,
      footer: 'Send a <NAME_EMAIL>',
    });
  }
  window.location.href = `${window.location.origin}/view-insulation-install-schedule/find-slots/${operationsId}`;
};

const partnerVisitStatusIcon = (e) => {
  switch (e) {
    case 'Inspection Scheduled':
      return <CalendarWeekIcon />;
    case 'Inspection Not Completed':
    case 'Customer Not Responsive':
    case 'Closed Lost':
      return <CalendarXIcon />;
    case 'Inspection Completed':
      return <CalendarCheckIcon />;
    case 'CAP - Inspection Scheduled':
      return <CapIncSchIcon />;
    case 'CAP - Inspection Not Completed':
    case 'CAP - Customer Not Responsive':
    case 'CAP - Closed Lost':
      return <CapIncNotCompIcon />;
    case 'CAP - Inspection Completed':
      return <CapIncCompIcon />;
    default:
      return null;
  }
};

const setCookie = (name, value, expirationHours) => {
  const expires = new Date();
  expires.setTime(expires.getTime() + expirationHours * 60 * 60 * 1000); // Convert hours to milliseconds for cookies
  Cookies.set(name, value, { expires });
};

const getFormattedCustomerInfoFromLead = (lead, formValues = {}, overrider = false) => {
  /* eslint-disable camelcase */
  const {
    Phone,
    FirstName,
    LastName,
    MobilePhone,
    Address,
    LeadSource,
    Lead_Source__c,
    Email__c,
    Day_Phone__c,
    Home_Phone__c,
    Customer_Email__c,
    Day_Phone_Type__c,
    Evening_Phone_Type__c,
    Referred_by_Auditor_Emp__c,
    Marketing_Referral_Code__c,
    Site_ID__c,
    Unit_Number__c,
    Electric_Account_Number__c,
    Electric_Provider__c,
    Electric_Project_Number__c,
    Gas_Project_Number__c,
    Gas_Account_Number__c,
    Gas_Provider__c,
    Heating_Fuel_Type__c,
    Year_Built__c,
    Square_Footage__c,
    Lead_Id__c,
    Email,
  } = lead;
  const customerEmail =
    overrider && formValues?.customerEmail
      ? formValues.customerEmail
      : Customer_Email__c || Email__c || Email || '';
  return {
    customerFirstName: FirstName,
    customerLastName: LastName,
    customerPrimaryPhoneNumber: Day_Phone__c || Phone || MobilePhone || '',
    customerPrimaryPhoneNumberType: Day_Phone_Type__c,
    customerSecondaryPhoneNumber: Home_Phone__c,
    customerSecondaryPhoneNumberType: Evening_Phone_Type__c,
    customerEmail,
    customerAddress: {
      ...Address,
      displayAddress: `${Address?.street}, ${Address?.city}, ${Address?.state} ${Address?.postalCode}`,
    },
    referredByAuditor: Referred_by_Auditor_Emp__c,
    leadSource: LeadSource || Lead_Source__c,
    isAuditorValueOnSf: !isEmpty(Referred_by_Auditor_Emp__c),
    referralCode: Marketing_Referral_Code__c,
    siteId: [Site_ID__c],
    unitNumber: [Unit_Number__c],
    houseBuilt: Year_Built__c,
    squareFeet: [Square_Footage__c],
    electricProviderCT: [Electric_Provider__c || ''],
    electricAccountNumber: [Electric_Account_Number__c || ''],
    gasProviderCT: [Gas_Provider__c || ''],
    gasAccountNumber: [Gas_Account_Number__c || ''],
    heatingFuelCT: [Heating_Fuel_Type__c],
    leadSourceCT: LeadSource,
    leadId: Lead_Id__c,
    electricProjectNumber: Electric_Project_Number__c,
    gasProjectNumber: Gas_Project_Number__c,
  };
};
/* eslint-enable camelcase */
const formatPhoneNumber = (phoneNumber) => {
  // formatPhoneNumber is directly called on render and on change
  // so on render we cannot return false, it should be '' so if phone
  // is undefined it should show empty field instead of showing false in render field
  if (!phoneNumber) return '';
  const removeAllSymbols = /^\+1|[ \-()+]/g;
  const numericOnly = phoneNumber.replace(removeAllSymbols, '');
  let formattedNumber = '';
  if (numericOnly.length > 0) {
    formattedNumber = `(${numericOnly.substring(0, 3)}`;
    if (numericOnly.length > 3) {
      formattedNumber += `) ${numericOnly.substring(3, 6)}`;
    }
    if (numericOnly.length > 6) {
      formattedNumber += ` - ${numericOnly.substring(6, 10)}`;
    }
  }

  return formattedNumber ? `+1 ${formattedNumber}` : '';
};

const csvDownload = (header, data, fileName = 'download') => {
  const stringData = data
    .map((a) => {
      return a.join(',');
    })
    .join('\n');
  const contentData = `${header.join(',')}\n${stringData}`;
  const csvContent = `data:text/csv;charset=utf-8;,${contentData}`;

  const link = document.createElement('a');
  link.download = fileName;
  link.href = csvContent;
  link.click();
};

const calculateVentMathRound = (calc) => {
  let result = Math.round(calc * 100) / 100;
  if (!Number.isFinite(result)) result = 0;
  return result;
};

const getTimeInDayEveningFormat = (inputTime) => {
  if (!inputTime) return '-';
  // Parser to get time in 8:00 am / 4:30 pm
  return moment(inputTime, 'HH:mm:ss').format('h:mm a');
};

const getArrivalWindows = ({ isHVACSales, type }) => {
  if (isHVACSales) {
    return {
      '9:00:00 AM': '8 AM - 9 AM',
      '12:00:00 PM': '11 AM - 12 PM',
      '3:00:00 PM': '2 PM - 3 PM',
    };
  }

  // CT
  if (type && type.slice(0, 2) === '01') {
    // Sealing Service
    if (type === '010005') {
      return {
        '8:00:00 AM': '8 AM - 12 PM',
        '9:00:00 AM': '8 AM - 12 PM',
        '10:30:00 AM': '8 AM - 12 PM',
        '12:00:00 PM': '10 AM - 2 PM',
        '1:30:00 PM': '10 AM - 2 PM',
      };
    }
    // HEA + Sealing Service
    if (type === '010006') {
      return {
        '8:00:00 AM': '8 AM - 10 AM',
        '1:00:00 PM': '12 PM - 2 PM',
      };
    }

    // All other CT
    return {
      '8:00:00 AM': '8 AM - 9 AM',
      '9:00:00 AM': '9 AM - 10 AM',
      '10:30:00 AM': '10 AM - 12 PM',
      '11:00:00 AM': '10 AM - 12 PM',
      '12:00:00 PM': '11 AM - 1 PM',
      '1:30:00 PM': '1 PM - 3 PM',
      '2:00:00 PM': '1 PM - 2 PM',
    };
  }

  return {
    '8:00:00 AM': '8 AM - 9 AM',
    '9:00:00 AM': '9 AM - 10 AM',
    '10:00:00 AM': '10 AM - 11 AM',
    '10:30:00 AM': '10 AM - 11 AM',
    '11:00:00 AM': '10 AM - 12 PM',
    '11:30:00 AM': '10:30 AM - 12:30 PM',
    '12:00:00 PM': '11 AM - 12 PM',
    '12:30:00 PM': '12 PM - 1 PM',
    '1:00:00 PM': '12:30 - 1:30 PM',
    '2:00:00 PM': '1 PM - 2 PM',
    '3:00:00 PM': '2 PM - 3 PM',
    '3:30:00 PM': '3 PM - 4 PM',
    '4:00:00 PM': '4 PM - 5 PM',
  };
};

const getFormattedArrivalWindow = ({ dateTimeMoment, type, isHVACSales = false }) => {
  const formattedDateTime = new Date(dateTimeMoment).toLocaleTimeString('en-US', { hour12: true });
  const arrivalWindows = getArrivalWindows({ type, isHVACSales });
  return arrivalWindows[formattedDateTime];
};

const parseJSXContentForSwalPopup = (content) => {
  return ReactDOMServer.renderToString(content);
};

const formatCsvData = (string) => {
  return string
    .replaceAll(/,/g, '')
    .replaceAll(/#/g, '')
    .replace(/[\r\n\u0085\u2028\u2029]+/g, '');
};

const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return false;
  const cleanedNumber = phoneNumber?.replace(/\D/g, '');

  if (cleanedNumber?.length !== 10) {
    return false;
  }

  if (!/^\d+$/.test(cleanedNumber)) {
    return false;
  }
  return true;
};

const validateEmail = (email) => {
  if (!email) return false;
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailPattern.test(email)) {
    return false;
  }

  return email;
};

// TODO: this could maybe be added to the utility service and we can use on the backend as well
const getSalesforceObjectTypeFromId = (salesforceId) => {
  if (!salesforceId) return null;
  const idPrefix = salesforceId.slice(0, 3);
  switch (idPrefix) {
    case '001':
      return 'account';
    case '00Q':
      return 'lead';
    case '006':
      return 'opportunity';
    case '800':
      return 'contract';
    case 'a05': // UAT 2.0
    case 'a1Z': // Prod 2.0
      return 'workOrder';
    case 'a06': // UAT 2.0
    case 'a1a': // Prod 2.0
      return 'workVisit';
    default:
      return null;
  }
};

export {
  isArray,
  isObject,
  getNestedFieldValueWithPath,
  isNumeric,
  updateNestedObjectFieldWithPath,
  deepClone,
  formatCurrency,
  getCityAndZipFromAddress,
  renderStartAndEndTimes,
  verifySingleWeek,
  chopString,
  toggleSidebar,
  getSalesforceUrl,
  preventParentElementClick,
  openNewTabWithUrl,
  openEmailClient,
  openPhoneCall,
  displayPhoneNumber,
  getGoogleMapsDirectionsUrl,
  getEventDuration,
  removeSpecialCharacters,
  formatZipCode,
  urlParamsToJson,
  capitalizeFirstLetterOfString,
  getEventColor,
  parseGoogleAutocomplete,
  getAlphanumericString,
  isFunctionEmpty,
  getZipcodeFromAddress,
  getCityAndZipcodeFromAddress,
  copyDealId,
  copyTextToClipboard,
  redirectScheduleWx,
  partnerVisitStatusIcon,
  setCookie,
  getFormattedCustomerInfoFromLead,
  formatPhoneNumber,
  csvDownload,
  calculateVentMathRound,
  getTimeInDayEveningFormat,
  getArrivalWindows,
  getFormattedArrivalWindow,
  parseJSXContentForSwalPopup,
  formatCsvData,
  validatePhoneNumber,
  validateEmail,
  getSalesforceObjectTypeFromId,
};
