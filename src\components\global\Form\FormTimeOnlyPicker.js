import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

import FormDateTimePicker from './FormDateTimePicker';

const FormTimeOnlyPicker = (props) => {
  const { onChange, timeIntervals = 15 } = props;

  const onTimeChange = (e) => {
    const { value } = e.target;

    // Handle time manipulation here so that the formdatetimepicker doesnt have to care about it
    return onChange({ target: { ...e.target, value: moment(value).format('HH:mm:ss') } });
  };

  return (
    <FormDateTimePicker
      timeIntervals={timeIntervals}
      {...props}
      onChange={onTimeChange}
      timeFormat="HH:mm:ss"
      dateFormat="h:mm a"
      timeCaption="Time"
      showTimeSelect
      showTimeSelectOnly
      useSyntheticEvent
      timeOnly
    />
  );
};

FormTimeOnlyPicker.propTypes = {
  onChange: PropTypes.func.isRequired,
  timeIntervals: PropTypes.number,
};

FormTimeOnlyPicker.defaultProps = {
  timeIntervals: 15,
};

export default FormTimeOnlyPicker;
