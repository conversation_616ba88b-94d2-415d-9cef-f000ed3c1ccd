import React, { useEffect } from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';
import {
  useRecoilState,
  useRecoilValue,
  // eslint-disable-next-line camelcase
  useRecoilBridgeAcrossReactRoots_UNSTABLE,
  useRecoilCallback,
} from 'recoil';
import { useLocation } from 'react-router-dom';

import { decodeEventType } from '@homeworksenergy/utility-service';

import { createSwalWithTheme } from '@config/swalConfig';

import { SalesforceManager, UtilityManager } from '@utils/APIManager';
import { getSalesforceObjectTypeFromId, capitalizeFirstLetterOfString } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

import { selectedEventState } from '@recoil/eventSidebar';
import { allEventsAtom } from '@recoil/event';
import { calendarTypeAtom } from '@recoil/app';

import { FormInput } from '@components/global/Form';
import { fireRemoveUnitConfirmation } from '@components/confirmations/RemoveUnitConfirmation';
import { fireCancelConfirmation } from '@components/confirmations/CancelEventConfirmation';
import { fireRescheduleConfirmation } from '@components/confirmations/RescheduleEventConfirmation';

// This sfTypeOptions is used to avoid typos
// ie: comparing 'operation' instead of 'operations', 'Deal' instead of 'deal' etc.
// Using reduce to enforce consistency between key and value
const sfTypeOptions = [
  'deal',
  'account',
  'operations',
  'event',
  'workVisit',
  'workOrder',
  'site',
  'opportunity',
  'salesVisit',
  'project',
].reduce((acc, type) => {
  acc[type] = type;
  return acc;
}, {});

const sfTypeDisplayNameMap = {
  [sfTypeOptions.workOrder]: 'Work Order',
  [sfTypeOptions.workVisit]: 'Work Visit',
  [sfTypeOptions.salesVisit]: 'Sales Visit',
};

const SfIdsContainer = styled.div`
  position: relative;
`;

const SfIdInputs = ({
  sfObjectType: sfObjectTypeFromProps,
  readOnly,
  recoilState,
  allowMultiUnit,
  allowEdit,
}) => {
  const theme = useTheme();
  const swal = createSwalWithTheme(theme);

  // Handle "find-slots" URL functionality, autopopulate IDs and open find-slots sidebar
  const urlParams = useLocation().pathname.split('/find-slots/')[1] || '';
  const [salesforceIdFromUrl] = urlParams.split('/');

  const sfObjectType = salesforceIdFromUrl
    ? getSalesforceObjectTypeFromId(salesforceIdFromUrl)
    : sfObjectTypeFromProps;

  const sfObjectKey = `${sfObjectType}Id`;

  // RecoilBridge allows the cancelEventConfirmation to share the same recoil state as the rest of our application
  // This is necessary since sweetalert2 creates its own ReactRoot for each modal
  // https://recoiljs.org/docs/api-reference/core/useRecoilBridgeAcrossReactRoots
  const RecoilBridge = useRecoilBridgeAcrossReactRoots_UNSTABLE();

  const [selectedEvent, setSelectedEvent] = useRecoilState(recoilState);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const allEvents = useRecoilValue(allEventsAtom);
  const {
    id,
    numUnit,
    sfIds: {
      [`${sfObjectKey}`]: sfId,
      [`${sfObjectKey}2`]: sfId2,
      [`${sfObjectKey}3`]: sfId3,
      [`${sfObjectKey}4`]: sfId4,
    },
  } = selectedEvent;

  const isCreate = !id;
  const type = selectedEvent.type || calendarType;

  const fetchSalesforceInfo = async (sfId) => {
    let salesforceInfo = {};

    // CT HEA
    if (state === 'CT' && department === 'HEA') {
      // Skip salesforce fetch for sealing service and revisits?
      salesforceInfo = await SalesforceManager.getCTHEAEventInfo({
        objectName: sfObjectType,
        sfId,
      });
    }
    // For MA Deal/Account inputs
    if (
      state === 'MA' &&
      ['HEA', 'HVAC Sales'].includes(department) &&
      [sfTypeOptions.deal, sfTypeOptions.account].includes(sfObjectType)
    ) {
      // Get other sfIds (account, event etc) and other info (fuel type, provider etc)
      salesforceInfo =
        department === 'HEA'
          ? await SalesforceManager.getHEAEventInfoWithDealIds([sfId])
          : await SalesforceManager.getHVACSalesInfoWithAccountId(sfId);
    }

    // SUBHUB PARTNER CALENDAR SUPPORT
    if (['Partners', 'Insulation Partners'].includes(department)) {
      salesforceInfo = await SalesforceManager.getPartnersEventInfoWithAccountIds([sfId]);
      // return setSelectedEvent({ ...selectedEvent, ...salesforceInfo });
    }

    return salesforceInfo;
  };

  const handleSfIdChange = async (e) => {
    const { value: sfId, name } = e.target;
    const sfIdKey = name.split('.').pop();

    // Pull out just the unit number from the key
    // If unit 1, there is no number at the end
    const unitNum = parseInt(sfIdKey.slice(-1), 10) || '';

    // Allow freeform edit. Used for siteIds
    if (allowEdit) {
      const sfIds = { ...selectedEvent.sfIds, [`${sfObjectKey}${unitNum}`]: sfId };
      return setSelectedEvent({ ...selectedEvent, sfIds });
    }

    // If not the correct length, we can't be sure whether the id is valid or not. require the correct sfId
    if (![15, 18].includes(sfId.length)) {
      return throwError('Please input either the 15 or 18 character Salesforce ID');
    }

    if (numUnit && Number(numUnit) > 1) {
      // For multifamilies, make sure we're not putting the same ID in multiple times
      // Filter out blank IDs for this check (CAPE jobs are scheduled before they have Site Ids)
      const filteredIds = unitSfIdsArr.filter((id) => id);
      const noDups = new Set([sfId, ...filteredIds]);

      if (filteredIds.length + 1 !== noDups.size) return throwError('Duplicate ID inserted.');
    }

    const skipExistingEventCheck = [
      '000003',
      '000004',
      '000005',
      '000007',
      '000100',
      '010000',
      '008800',
      '008801',
      '999999',
    ].includes(type);

    // Prevent multiple HEAs from being scheduled
    if (!skipExistingEventCheck) {
      const dealExists = await UtilityManager.getEventsForDealIdInDb(sfId);
      if (dealExists) return swal.fire({ title: `Event already exists for this Deal Id: ${sfId}` });
    }
    // Prevent multiple HCSs from being scheduled
    if (type === '000100') {
      const accountExists = await UtilityManager.checkEventsForAccountId(sfId);
      if (accountExists)
        return swal.fire({ title: `Event already exists for this Account Id: ${sfId}` });
    }

    const isCap = ['000006'].includes(type);

    const salesforceInfo = await fetchSalesforceInfo(sfId);
    if (!salesforceInfo) return throwError('There was an error fetching Salesforce info');

    // CAP VALIDATIONS
    if (department === 'HEA') {
      const { leadVendor } = salesforceInfo;

      // Prevent scheduling a CAP job as Market Rate
      // Allow return visits, custom blocks etc
      if (leadVendor === 'CAP' && !isCap && !skipExistingEventCheck)
        return swal.fire({
          title: 'Hold Up!',
          text:
            'You are trying to schedule a CAP customer in a market rate time slot. The lead vendor on Salesforce must match the event type you are scheduling.',
          icon: 'warning',
          confirmButtonText: 'Cancel',
        });

      // Prevent scheduling market rate jobs as CAP
      // Allow them to override for Mixed income
      if (leadVendor !== 'CAP' && isCap) {
        const { value: isMixedIncome } = await swal.fire({
          title: 'Hold Up!',
          text:
            'You are trying to schedule a market rate customer for a CAP time slot. The lead vendor on Salesforce must match the event type you are scheduling.',
          icon: 'warning',
          confirmButtonText: 'This is a mixed income property',
          showCancelButton: true,
          cancelButtonText: 'Cancel',
        });
        if (!isMixedIncome) return false;
      }
    }

    // HVAC SALES ZIPCODE VALIDATION
    const zipcode = salesforceInfo.address?.postalCode;

    const supportedZipcode = await UtilityManager.validateZipCode(zipcode, type);
    if (!supportedZipcode) return false;

    // MULTIFAMILY SUPPORT
    // Update keys of the sfIds returned from salesforce to match the correct unit number
    // ie: accountId -> accountId3, dealId -> dealId3 etc
    Object.keys(salesforceInfo.sfIds).forEach((sfIdType) => {
      // Keys are already correct for unit 1
      if (!unitNum) return;
      const sfId = salesforceInfo.sfIds[sfIdType];
      salesforceInfo.sfIds[`${sfIdType}${unitNum}`] = sfId;
      delete salesforceInfo.sfIds[sfIdType];
    });

    const sfIds = {
      ...selectedEvent.sfIds,
      ...(!!salesforceInfo.sfIds && salesforceInfo.sfIds), // Funky syntax, but just only spreading sfids if they exist in the sf response
      [`${sfObjectKey}${unitNum}`]: sfId,
    };

    return setSelectedEvent({
      ...selectedEvent,
      ...salesforceInfo,
      sfIds,
    });
  };

  useEffect(() => {
    // Only fetch SF info when creating a new event.
    if (!isCreate) return;

    // Fetch for sfId in url params
    if (salesforceIdFromUrl) {
      handleSfIdChange({
        target: { value: salesforceIdFromUrl, name: `sfIds.${sfObjectType}` },
      });
    }

    // If sfIds[sfObjectType] is present, such as for scheduling Sealing Services from an existing event.
    if (sfId) {
      fetchSalesforceInfo(sfId);
    }
    // Leaving out salesforceId and handleSfIdChange here so that it doesn't when inputting salesforceId manually, not from the URL
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const originalEvent = allEvents[id];

  const removeUnit = useRecoilCallback(
    ({ snapshot }) => async (removalUnitNum, isNewUnit, cancelOrReschedule) => {
      const selectedEvent = await snapshot.getPromise(selectedEventState);

      const {
        sfIds,
        numUnit,
        notes: { cancelNotes, rescheduleNotes },
        cancelReason,
        rescheduleReason,
      } = selectedEvent;
      let { removedUnits = [] } = selectedEvent;

      // Used to keep track of units that have been removed
      let newRemovedUnits = null;

      // If they haven't saved the unit in the db yet, we don't need to cancel/reschedule
      if (!isNewUnit) {
        newRemovedUnits = {};

        // Map to the correct notes and reason fields depending on reschedule or cancel
        const [removalNotes, removalReason] = {
          cancel: [cancelNotes, cancelReason],
          reschedule: [rescheduleNotes, rescheduleReason],
        }[cancelOrReschedule];

        newRemovedUnits.cancelOrReschedule = cancelOrReschedule;
        // Set as cancel notes or reschedule notes
        newRemovedUnits[`${cancelOrReschedule}Notes`] = removalNotes;
        newRemovedUnits[`${cancelOrReschedule}Reason`] = removalReason;
      }

      const newSfIds = unitSfIdsArr.reduce((acc, curr, index) => {
        // Need to place the deal ids back in order, even if we take one out in the middle
        // Reduce unit number by 1 if it's after the removal index
        // ex: if we have a 4 unit and are removing unit 2:
        // unit 1 and 2 stay the same (unit 2 is removed below)
        // unit 3 and 4 become units 2 and 3 respectively
        const oldUnitNum = index + 1;
        const newUnitNum = oldUnitNum <= removalUnitNum ? oldUnitNum : oldUnitNum - 1;

        // Traverse all possible sfIds and remove them for that unit.
        // If we're removing unit 2's deal id, need to remove operations, account etc for unit 2 as well.
        Object.keys(sfTypeOptions).forEach((type) => {
          const oldSfIdKey = createSfIdKey(oldUnitNum, type);
          const newSfIdKey = createSfIdKey(newUnitNum, type);

          // If there is no id for that type/unit in the sfIds object, move on
          // ie: no workVisit saved for that unit
          if (!sfIds[oldSfIdKey]) return;

          // If it is the unit we're trying to remove, don't add to newSfIds
          // Also add it to the list of removed units to be canceled on salesforce on the backend
          if (oldUnitNum === removalUnitNum) {
            // If they add the unit and then remove it, but haven't saved yet, we don't need to add to the removed unit array
            // since the unit is not yet synced with Salesforce/the DB
            if (!isNewUnit) newRemovedUnits[`${type}Id`] = sfIds[oldSfIdKey];
            return;
          }

          // Rebuild sfIds object with only the objects that are relevant
          acc[newSfIdKey] = sfIds[oldSfIdKey];
        });

        return acc;
      }, {});

      removedUnits = newRemovedUnits ? [...removedUnits, newRemovedUnits] : removedUnits;

      // Decrement numUnit since we removed a unit
      setSelectedEvent({
        ...selectedEvent,
        sfIds: newSfIds,
        numUnit: numUnit - 1,
        removedUnits,
      });
    },
  );

  const handleRemoveUnitConfirmations = async (k, isNewUnit) => {
    // No need to fire confirmations if they haven't saved the unit to the database.
    // There would be nothing to cancel/reschedule on salesforce
    if (isNewUnit) return removeUnit(k, isNewUnit);

    // Find out whether the unit should be fully canceled or rescheduled later
    const cancelOrReschedule = await fireRemoveUnitConfirmation(theme);
    // If they close out of the cancel/reschedule question without choosing anything, don't remove the unit
    if (!cancelOrReschedule) return false;

    let confirmed = false;
    if (cancelOrReschedule === 'cancel')
      ({ value: confirmed } = await fireCancelConfirmation(RecoilBridge, false, true, theme));
    if (cancelOrReschedule === 'reschedule')
      ({ value: confirmed } = await fireRescheduleConfirmation(RecoilBridge, theme));
    // If they don't fill out the cancel/reschedule reasons, don't remove the unit
    if (!confirmed) return false;

    return removeUnit(k, isNewUnit, cancelOrReschedule);
  };

  if (!type) return null;
  if (!numUnit || Number(numUnit) < 1) return null;

  const multiUnit = numUnit && type.slice(0, 4) !== '0088' && Number(numUnit) > 1;

  const { state, business: department } = decodeEventType(type);

  // If there is an empty sfId, require them to fill it out before removing another unit
  const isAdding = [sfId, sfId2, sfId3, sfId4].slice(0, numUnit).filter((sfId) => !sfId).length > 0;

  // If there is not a value for that unit (3 and 4 of a 2 unit mf for example) filter it out
  // In case of site ids (or maybe others), there could be an undefined id in the middle of the array.
  const unitSfIdsArr = [sfId, sfId2, sfId3, sfId4].slice(0, numUnit).map((id) => {
    return id || '';
  });

  const createSfIdKey = (unitNum = 1, sfObjectType = sfObjectType) => {
    // Unit 1 doesn't have suffix (ie: dealId vs dealId2)
    const objectKeySuffix = unitNum === 1 ? '' : unitNum;
    return `${sfObjectType}Id${objectKeySuffix}`;
  };

  // If creating a return visit, don't attempt to open the deal page
  const onClickSfId = (sfId, isNewUnit) => {
    if (!isCreate && !isNewUnit && sfId)
      if (state === 'CT')
        UtilityManager.openSf2Page(sfId, `${capitalizeFirstLetterOfString(sfObjectType)}`);
      else UtilityManager.openSfPage(sfId, `${capitalizeFirstLetterOfString(sfObjectType)}__c`);
  };

  // TODO: need one function to increase the num unit, and another to actually handle the salesforce add once they plug in the deal id
  const addUnit = async () => {
    // If it's a single family and we're adding a unit, change the type to multifamily.
    const newType = type === '000000' ? '000001' : type;
    setSelectedEvent({ ...selectedEvent, numUnit: numUnit + 1, type: newType });
  };

  const sfIdInputs = [];

  // For each sfId
  for (let k = 1; k < Number(numUnit) + 1; k++) {
    const value = unitSfIdsArr[k - 1];

    // Allow editing of a unit if it has not yet been added to the database
    const isNewUnit = !Object.values(originalEvent?.sfIds || {}).includes(value);

    // Don't allow removing sfId if there is only one there
    // On create, the sfIds are set by the numUnit radio button so don't show there either
    // If there is an empty sfId, require them to fill it out before removing a different unit
    const titleAction =
      (!isCreate && numUnit > 1 && !isAdding) || (!value && !isCreate && isAdding && numUnit > 1)
        ? { label: 'Remove', action: () => handleRemoveUnitConfirmations(k, isNewUnit) }
        : null;

    // Only show 'add a unit' button on bottom unit and don't allow adding more than 4
    const bottomAction =
      allowMultiUnit && !isCreate && k === numUnit && numUnit < 4
        ? { label: 'Add a Unit', action: () => addUnit() }
        : null;

    // Add space for object names with multiple words
    const sfTypeDisplayName = sfTypeDisplayNameMap[sfObjectType] || sfObjectType;

    sfIdInputs.push(
      <FormInput
        key={`sfIds.${sfObjectKey}${k}`}
        required={isCreate}
        title={multiUnit ? `${sfTypeDisplayName} id (${k})` : `${sfTypeDisplayName} id`}
        titleAction={!readOnly && titleAction}
        bottomAction={!readOnly && bottomAction}
        placeholder="Enter Salesforce ID"
        readOnly={allowEdit ? !allowEdit : readOnly || (!isCreate && !isNewUnit)}
        name={`sfIds.${createSfIdKey(k)}`}
        value={value}
        onClick={!allowEdit ? () => onClickSfId(value, isNewUnit) : () => {}}
        onChange={handleSfIdChange}
        allowCopy={!isCreate}
        copyTextValue={value}
        maxLength={18}
      />,
    );
    if (type.slice(0, 4) === '0088') break;
  }

  return <SfIdsContainer>{sfIdInputs}</SfIdsContainer>;
};

SfIdInputs.propTypes = {
  sfObjectType: PropTypes.oneOf(Object.values(sfTypeOptions)).isRequired,
  readOnly: PropTypes.bool,
  recoilState: PropTypes.shape({}),
  allowMultiUnit: PropTypes.bool,
  allowEdit: PropTypes.bool,
};

SfIdInputs.defaultProps = {
  readOnly: false,
  recoilState: selectedEventState,
  allowMultiUnit: true,
  allowEdit: false,
};

export default SfIdInputs;
