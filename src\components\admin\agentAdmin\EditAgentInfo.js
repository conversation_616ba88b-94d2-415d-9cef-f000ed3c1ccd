import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';
import { Plus } from '@styled-icons/boxicons-regular/Plus';

import { PrimaryButton, SecondaryButton } from '@components/global/Buttons';
import { PageContainer, PageHeader } from '@pages/Components';
import ScreenPartitionView from '@components/global/ScreenPartitionView/ScreenPartitionView';
import { Filters } from '@components/global/Filters';
import AgentList from '@components/admin/agentAdmin/AgentList';
import AgentListItemDetail from '@components/admin/agentAdmin/AgentListItemDetail';

import { urlParamsToJson } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

import { authorizedDepartmentsSelector, statesSelector } from '@recoil/app';
import { allAuthorizedAgentsForUserSelector, selectedAgentAtom } from '@recoil/admin/agents/';
import { selectedFiltersState, adminInfoChangesState } from '@recoil/admin';

import MultipleAgents from './MultipleAgents/MultipleAgents';

const PlusIcon = styled(Plus)`
  height: 22px;
`;

const EditAgentInfo = (props) => {
  const allAgents = useRecoilValue(allAuthorizedAgentsForUserSelector);
  const allStates = useRecoilValue(statesSelector);
  const [filteredAgents, setFilteredAgents] = useState(allAgents);
  const departments = useRecoilValue(authorizedDepartmentsSelector);
  const selectedFilters = useRecoilValue(selectedFiltersState);
  const setSelectedAgent = useSetRecoilState(selectedAgentAtom);
  const resetSelectedAgent = useResetRecoilState(selectedAgentAtom);
  const [editMultipleAgents, setEditMultipleAgents] = useState(false);
  const resetAdminInfoChanges = useResetRecoilState(adminInfoChangesState);

  const {
    location: { search: urlQueryParams },
  } = props;

  useEffect(() => {
    setAgentByUrlOid();
    resetAdminInfoChanges();
  }, [setAgentByUrlOid, resetAdminInfoChanges]);

  useEffect(() => {
    const newFilteredAgents = allAgents.filter((agent) => {
      let agentMatchesAllFilters = true;

      const filterNames = Object.keys(selectedFilters);

      filterNames.forEach((filterName) => {
        if (agent[filterName] !== selectedFilters[filterName].value) agentMatchesAllFilters = false;
      });

      return agentMatchesAllFilters;
    });

    setFilteredAgents(newFilteredAgents);
  }, [selectedFilters, allAgents]);

  const setAgentByUrlOid = useCallback(() => {
    // TODO: this will crash if there are no agents. We might want to handle this
    if (!urlQueryParams) return setSelectedAgent(allAgents[0]);

    const { oid } = urlParamsToJson(urlQueryParams);
    const newSelectedAgent = allAgents.find((agent) => (agent.oid === oid ? agent : false));

    if (!newSelectedAgent) {
      throwError('Agent not found. You may not have sufficient permissions to update this agent.');
      return setSelectedAgent(allAgents[0]);
    }

    return setSelectedAgent(newSelectedAgent);
  }, [allAgents, setSelectedAgent, urlQueryParams]);

  const handleCreateAgentClick = () => {
    resetSelectedAgent();
  };

  const filterOptions = [
    // { name: 'Company', options: companies },
    // { name: 'Region', options: regions },
    { displayName: 'State', name: 'state', options: allStates },
    { displayName: 'Department', name: 'department', options: departments },
  ];

  const HeaderButtons = [
    <SecondaryButton
      role="button"
      onClick={() => {
        setEditMultipleAgents(true);
      }}
      key="editMultipleButton"
    >
      Edit Multiple
    </SecondaryButton>,
    <PrimaryButton role="button" onClick={handleCreateAgentClick} key="createNewAgentButton">
      <PlusIcon />
      Create New Crew
    </PrimaryButton>,
  ];

  const AgentFilterComponent = <Filters filters={filterOptions} />;

  if (editMultipleAgents)
    return (
      <PageContainer>
        <PageHeader>Edit Multiple Agents</PageHeader>
        <MultipleAgents cancel={setEditMultipleAgents} />
      </PageContainer>
    );

  return (
    <PageContainer>
      <PageHeader buttons={HeaderButtons} filters={AgentFilterComponent}>
        Edit Crew Info
      </PageHeader>
      <ScreenPartitionView ratio={[3, 7]}>
        <AgentList agents={filteredAgents} />
        <AgentListItemDetail />
      </ScreenPartitionView>
    </PageContainer>
  );
};

EditAgentInfo.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
};

EditAgentInfo.defaultProps = {
  location: { search: null },
};

export default EditAgentInfo;
