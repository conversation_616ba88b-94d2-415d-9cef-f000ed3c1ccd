import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme, ThemeProvider } from 'styled-components';
import {
  useRecoilState,
  useRecoilValue,
  // eslint-disable-next-line camelcase
  useRecoilBridgeAcrossReactRoots_UNSTABLE,
  useResetRecoilState,
} from 'recoil';

import { decodeEventType } from '@homeworksenergy/utility-service';

import { selectedEventState } from '@recoil/eventSidebar';
import { paymentLinkAtom, paymentLinkSelector, customerInfoSelector } from '@recoil/payment';
import paymentValidation from '@utils/paymentValidation';
import { paymentTypeOptions, paymentTypeOptionsDict } from '@utils/constants';

import {
  handleFormFieldChange,
  Row,
  Col,
  FormSelect,
  FormInputSelect,
  FormInput,
} from '@components/global/Form';
import { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';

import { DollarCircle } from '@styled-icons/boxicons-regular/DollarCircle';
import { Stripe } from '@styled-icons/fa-brands/Stripe';
import { PrimaryButton, SecondaryButton } from '@components/global/Buttons';

import { PaymentManager } from '@utils/APIManager';
import { openNewTabWithUrl } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

const PaymentButton = styled(PrimaryButton)`
  padding: 8px;
  width: 100%;
  font-weight: 600;
  font-size: 12px;
  margin-top: 5px;
  color: ${({ theme }) => theme.colors.eventA};
  background-color: ${({ theme }) => theme.secondary[100]};
`;
const Container = styled.div``;
const DollarIcon = styled(DollarCircle)`
  height: 25px;
  width: 25px;
  margin-right: 5px;
`;

const StripeIcon = styled(Stripe)`
  height: 120px;
  width: 120px;
  margin-top: 30px;
  color: #635bff;
`;

const PaymentLinkButton = styled(PrimaryButton)`
  padding: 8px;
  width: 100%;
  font-weight: 600;
  font-size: 12px;
  margin-top: 5px;
  color: ${({ theme }) => theme.colors.eventA};
  background-color: ${({ theme }) => theme.primary[100]};
  & :hover {
    color: ${({ theme }) => theme.primary[100]};
    background-color: ${({ theme }) => theme.primary[300]};
  }
`;

const EmailPaymentLinkButton = styled(SecondaryButton)`
  padding: 8px;
  width: 100%;
  font-weight: 600;
  font-size: 12px;
  margin-top: 5px;

  color: ${({ theme }) => theme.colors.eventGreen};
  background-color: ${({ theme }) => theme.primary[100]};
  & :hover {
    color: ${({ theme }) => theme.primary[100]};
    background-color: ${({ theme }) => theme.colors.eventGreen};
  }
`;

const GeneratePaymentLinkButton = styled(SecondaryButton)`
  padding: 8px;
  width: 100%;
  font-weight: 600;
  font-size: 12px;
  margin-top: 5px;

  color: ${({ theme }) => theme.colors.eventGreen};
  background-color: ${({ theme }) => theme.primary[100]};
  & :hover {
    color: ${({ theme }) => theme.primary[100]};
    background-color: ${({ theme }) => theme.colors.eventGreen};
  }
`;

const FormContainer = styled.div`
  margin-top: 20px;
`;

const UnitNumberContainer = styled(Row)`
  display: grid;
  width: 100%;
  justify-content: center;
  margin-bottom: 15px;
`;

const PaymentForm = ({ selectedUnitNumber, multiUnit, homePageEvent, theme }) => {
  const [activeForm, setActiveForm] = useState('customerInfo');
  const [sfIds, setSfIds] = useState({});
  const [paymentLink, setPaymentLink] = useRecoilState(paymentLinkAtom);
  const [paymentState, setPaymentState] = useRecoilState(paymentLinkSelector);
  const [customerInfo, setCustomerInfo] = useRecoilState(customerInfoSelector);
  const resetPaymentState = useResetRecoilState(paymentLinkSelector);
  const event = useRecoilValue(selectedEventState);
  let paymentOptionsPicklist = paymentTypeOptions;
  const { type } = event;
  if (type) {
    const { business: department, state } = decodeEventType(type);
    paymentOptionsPicklist = paymentTypeOptionsDict[state][department] || paymentTypeOptions;
  }

  const { email, customerName, phoneNumber } = customerInfo;
  const { paymentType, amount, customAmount, location } = paymentState;

  const showActionButtons =
    paymentType.length > 0 && (amount.length > 0 || customAmount.length > 0) && location.length > 0;

  const isWifiTherm = paymentType === 'Wifi Therms';

  const wifiThermAmounts = [
    { key: '$235.00 (1)', value: '235' },
    { key: '$470.00 (2)', value: '470' },
    { key: '$705.00 (3)', value: '705' },
    { key: '$940.00 (4)', value: '940' },
    { key: '$1175.00 (5)', value: '1175' },
    { key: '$1410.00 (6)', value: '1410' },
  ];

  const amountOptionsDict = {
    'Wifi Therms': wifiThermAmounts,
    'HVAC Deposit': [{ key: '$500.00', value: '500' }],
    'Wx Deposit': [{ key: '$150.00', value: '150' }],
  };

  const amountOptions = amountOptionsDict[paymentType] || [];

  const locationOptions = [
    { key: 'MA - North Shore', value: 'MA - North Shore' },
    { key: 'MA - South Shore A', value: 'MA - South Shore A' },
    { key: 'MA - Metro West', value: 'MA - Metro West' },
    { key: 'MA - Western', value: 'MA - Western' },
    { key: 'MA - Cape Cod', value: 'MA - Cape Cod' },
    { key: 'Other', value: 'Other' },
  ];

  useEffect(() => {
    resetPaymentState();
    setPaymentLink('');
    let { email, customerName, phoneNumber } = event;
    const { address } = event;
    let displayAddress = address?.displayAddress;
    if (email === '' && customerName === '' && phoneNumber === '' && !displayAddress)
      ({
        email,
        customerName,
        phoneNumber,
        address: { displayAddress },
      } = homePageEvent);
    setCustomerInfo({ ...customerInfo, email, customerName, phoneNumber, address: displayAddress });
    retriveSfIds();
  }, []);

  useEffect(() => {
    if (activeForm === 'customerInfo' && paymentLink.length > 0) setPaymentLink('');
  }, [activeForm]);

  const getDefaultAmount = (paymentType) => {
    switch (paymentType) {
      case 'HVAC Deposit':
      case 'Wx Deposit':
        return '500';
      case 'Wifi Therms':
        return '235';
      default:
        return '';
    }
  };

  const handleFieldChange = (e) => {
    return handleFormFieldChange(e, paymentState, setPaymentState);
  };

  const handleCustomerInfoFieldChange = (e) => {
    return handleFormFieldChange(e, customerInfo, setCustomerInfo);
  };

  const generatePaymentLink = async () => {
    try {
      const { dealId, accountId } = sfIds;
      if (!dealId || !accountId || dealId.length === 0 || accountId.length === 0) {
        throw 'Salesforce Ids missing.'; // eslint-disable-line no-throw-literal
      }
      const params = {
        ...paymentState,
        sfIds: {
          dealId,
          accountId,
        },
        customerEmail: customerInfo.email,
        customerName: customerInfo.customerName,
        customerPhoneNumber: customerInfo.phoneNumber,
      };
      const isValid = paymentValidation.paymentLink(params);
      if (isValid) {
        const url = await PaymentManager.getPaymentLink(params);
        setPaymentLink(url);
      }
    } catch (error) {
      throwError({
        message: 'Error Generating Payment Link.\nPlease contact Software team.',
      });
    }
  };

  const handleEmailPaymentLinkButtonClick = async () => {
    await PaymentManager.emailPaymentLink({
      ...customerInfo,
      paymentLink,
      sfIds,
      unitNumber: multiUnit ? selectedUnitNumber + 1 : null,
    });
  };

  const handlePaymentTypeChange = (event) => {
    const { name, value } = event.target;
    const obj = { [name]: value, location: value === 'HVAC Deposit' ? 'MA - Woburn' : '' };
    const amount = getDefaultAmount(value);
    setPaymentState({ ...paymentLink, ...obj, amount, customAmount: '' });
    setPaymentLink('');
  };

  const retriveSfIds = () => {
    const { sfIds } = event;
    const formattedSfIdUnit = selectedUnitNumber === 0 ? '' : selectedUnitNumber + 1;
    const dealId =
      sfIds?.[`dealId${formattedSfIdUnit}`] && sfIds?.[`dealId${formattedSfIdUnit}`]?.length !== 0
        ? sfIds?.[`dealId${formattedSfIdUnit}`]
        : homePageEvent?.sfIds?.[`dealId${formattedSfIdUnit}`];
    const accountId =
      sfIds?.[`accountId${formattedSfIdUnit}`] &&
      sfIds?.[`accountId${formattedSfIdUnit}`]?.length !== 0
        ? sfIds?.[`accountId${formattedSfIdUnit}`]
        : homePageEvent?.sfIds?.[`accountId${formattedSfIdUnit}`];

    setSfIds({ dealId, accountId });
  };

  const renderPaymentLinkGenerationForm = () => {
    return (
      <>
        <Row>
          <Col size={1}>
            <FormSelect
              required
              title="Payment Type"
              placeholder="Select Payment Type"
              name="paymentType"
              value={paymentType}
              onChange={handlePaymentTypeChange}
              options={paymentOptionsPicklist}
            />
          </Col>
          <Col size={1}>
            <FormInputSelect
              title="Amount"
              readOnly={false}
              options={amountOptions}
              selectFieldName="amount"
              inputFieldName="customAmount"
              selectFieldValue={amount}
              inputFieldValue={customAmount}
              state={paymentState}
              setState={setPaymentState}
              errorMessage={
                paymentType === 'Wx Deposit' ? '*Amount needs to be $150 or less.' : null
              }
              customAmountMax={paymentType === 'Wx Deposit' ? 150 : null}
              showCustomValue={!isWifiTherm}
            />
          </Col>
        </Row>
        <Row>
          {paymentType !== 'HVAC Deposit' && (
            <Col size={1}>
              <FormSelect
                required
                title="Location"
                placeholder="Select Location"
                name="location"
                value={location}
                onChange={handleFieldChange}
                options={locationOptions}
              />
            </Col>
          )}
        </Row>
        <GeneratePaymentLinkButton onClick={() => setActiveForm('customerInfo')}>
          Back To Customer Info
        </GeneratePaymentLinkButton>
      </>
    );
  };

  const handleOnClickNextButton = () => {
    const isValid = paymentValidation.customerInfo(customerInfo);
    if (isValid) setActiveForm('paymentLinkGenerator');
    else
      throwError({
        message: 'Incorrent Email, Phone or Name.',
      });
  };

  const renderCustomerInfoForm = () => {
    return (
      <>
        <Row>
          <Col>
            <FormInput
              required
              title="Customer name"
              placeholder="Customer name"
              name="customerName"
              value={customerName}
              onChange={handleCustomerInfoFieldChange}
            />
          </Col>
          <Col>
            <FormInput
              required
              title="Email"
              placeholder="Enter Email"
              name="email"
              value={email}
              onChange={handleCustomerInfoFieldChange}
            />
          </Col>
        </Row>
        <Row>
          <Col>
            <FormInput
              required
              title="Phone"
              placeholder="Enter Phone"
              name="phoneNumber"
              value={phoneNumber}
              onChange={handleCustomerInfoFieldChange}
            />
          </Col>
        </Row>
        <GeneratePaymentLinkButton onClick={() => handleOnClickNextButton()}>
          Next To Generate Payment Link
        </GeneratePaymentLinkButton>
      </>
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <FormContainer>
        <UnitNumberContainer>
          {multiUnit && <HeaderLabel>Unit Number: {selectedUnitNumber + 1}</HeaderLabel>}
          <HeaderLabel>Address: {customerInfo?.address}</HeaderLabel>
        </UnitNumberContainer>
        {activeForm === 'customerInfo' && renderCustomerInfoForm()}
        {activeForm === 'paymentLinkGenerator' && renderPaymentLinkGenerationForm()}
        {showActionButtons && activeForm === 'paymentLinkGenerator' && paymentLink?.length === 0 && (
          <Row>
            <GeneratePaymentLinkButton onClick={() => generatePaymentLink()}>
              Generate Payment Link
            </GeneratePaymentLinkButton>
          </Row>
        )}
        {paymentLink?.length > 0 && (
          <Row>
            <Col>
              <EmailPaymentLinkButton onClick={() => handleEmailPaymentLinkButtonClick()}>
                <DollarIcon />
                Email Payment Link
              </EmailPaymentLinkButton>
            </Col>
            <Col>
              <PaymentLinkButton onClick={() => openNewTabWithUrl(paymentLink)}>
                <DollarIcon />
                Open Payment Link
              </PaymentLinkButton>
            </Col>
          </Row>
        )}
      </FormContainer>
    </ThemeProvider>
  );
};

const Payment = forwardRef((props, ref) => {
  const theme = useTheme();
  const RecoilBridge = useRecoilBridgeAcrossReactRoots_UNSTABLE();

  useImperativeHandle(
    ref,
    () => {
      return {
        handlePaymentButtonClick(unitNumber, multiUnit) {
          onPaymentButtonClick(unitNumber, multiUnit);
        },
      };
    },
    [],
  );

  const onPaymentButtonClick = async (unitNumber = 0, multiUnit = false) => {
    const { createSwalWithTheme } = await import('@config/swalConfig');
    const swal = createSwalWithTheme(theme);
    await swal.fire({
      html: (
        <>
          <Container>
            <StripeIcon />
          </Container>
          <RecoilBridge>
            <PaymentForm
              selectedUnitNumber={unitNumber}
              multiUnit={multiUnit}
              homePageEvent={props.event}
              theme={theme}
            />
          </RecoilBridge>
        </>
      ),
      width: '800px',
      height: '800px',
      showCloseButton: true,
      showConfirmButton: false,
    });
  };

  return props.showButton ? (
    <PaymentButton onClick={props.onClick}>
      <DollarIcon />
      Payment
    </PaymentButton>
  ) : null;
});

Payment.propTypes = {
  event: PropTypes.shape({
    sfIds: PropTypes.shape({
      dealId: PropTypes.string,
      accountId: PropTypes.string,
    }),
    email: PropTypes.string,
    customerName: PropTypes.string,
    phoneNumber: PropTypes.string,
  }),
  showButton: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
};

Payment.defaultProps = {
  showButton: true,
  event: {},
};

PaymentForm.propTypes = {
  selectedUnitNumber: PropTypes.number.isRequired,
  multiUnit: PropTypes.bool.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  theme: PropTypes.object.isRequired,
  homePageEvent: PropTypes.shape({
    sfIds: PropTypes.shape({
      dealId: PropTypes.string,
      accountId: PropTypes.string,
    }),
    email: PropTypes.string,
    customerName: PropTypes.string,
    phoneNumber: PropTypes.string,
  }),
};

PaymentForm.defaultProps = {
  homePageEvent: {},
};

export default Payment;
