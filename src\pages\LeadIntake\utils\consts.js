const maxNumberOfUnits = `
Unfortunately, the MassSave program does not allow HomeWorks to perform assessments at homes that
are part of a 5 or more unit multi-family. The good news is that you should still be able to get an
assessment through MassSave directly. Would you like the number for that? (You can reach them 
directly at ************)
`;

const renovationsRecently = ` MassSave is a retrofit program, that means we can only 
assess homes as they ARE and not what they MAY BE, if your home is being constructed or undergoing
major renovations, you can call a specific department at MassSave. Would you like the number? (You can reach
them at ************)`;

const homeAssesmentRecently =
  'Will need to wait until 6 years have passed and then customer may be eligible to have that done again.';

const houseBuiltAndArea = 'This job should be scheduled for a full day.';

const duplicateTableSteps = { accounts: 'DUPLICATE_ACCOUNTS', leads: 'DUPLICATE_LEADS' };

const leadIntakeTabs = [
  {
    name: 'preliminaryQuestions',
    title: 'Preliminary Questions',
  },
  {
    name: 'customerInfo',
    title: 'Customer Info',
  },
  {
    name: 'sourceInfo',
    title: 'Source Info',
  },
  {
    name: 'review',
    title: 'Review',
  },
  {
    name: 'scheduleEvent',
    title: 'Open Slots',
  },
  {
    name: 'wrapUp',
    title: 'Wrap-Up',
  },
];

const leadIntakeTabsCT = [
  {
    name: 'customerInfo',
    title: 'Customer Info',
  },
  {
    name: 'preliminaryQuestions',
    title: 'Preliminary Questions',
  },
  {
    name: 'sourceInfo',
    title: 'Source Info',
  },
  {
    name: 'review',
    title: 'Review',
  },
  {
    name: 'scheduleEvent',
    title: 'Open Slots',
  },
  {
    name: 'wrapUp',
    title: 'Wrap-Up',
  },
];

const electricProvidersOptions = ['National Grid', 'Eversource', 'Municipal', 'Other'];
const heatingFuelOptions = ['Gas', 'Electric', 'Oil', 'Propane'];
const gasProviderOptions = [
  'National Grid',
  'Eversource',
  'Municipal',
  'Columbia Gas',
  'Berkshire',
  'Liberty',
  'Blackstone',
  'Other',
];
const leadSourceOptionsMA = [
  'Auditor Referral',
  'Customer Referral',
  'Direct HVAC',
  'Direct Mail',
  'Email',
  'Employee Referral',
  'Field Marketing',
  'HEA Visit',
  'Home Energy Specialist',
  'Mass Save',
  'Multifamily',
  'Paid Search',
  'Partners',
  'Radio',
  'Repeat Customer',
  'Social',
  'TV',
  'Word of mouth',
  'Inside Sales',
  'Website',
  'Service Tech',
  'Wx Install',
  'CAP',
  'Green Collar',
];
const leadSourceOptionsCT = [
  'Direct Mail',
  'Field Marketing',
  'UI (United Illuminating)',
  'ES (Eversource)',
  'I Heart My Home',
  'Posigen',
  'Solar',
  'Referral',
  'Mailing',
  'Sync Renewables',
  "Citizen's Oil",
  'Dandelion',
  'Sealed',
  'Online/Social',
  'Heatsmart Branford/EH',
  'Heatsmart Fairfield',
  'Heatsmart RiverCOG',
  'Heatsmart Westport',
  'Heatsmart Woodbridge',
  'Heatsmart N Branford',
  'Heatsmart Hamden',
  'Other',
];

const leadTypeOptions = ['HEA', 'Competitive Quote', 'Posigen Sub-contractor', 'CMC Windows'];

const leadSourceDetailOptionsCT = {
  Solar: ['Momentum', 'Palmetto', 'Earthsmart', 'Earthlight', 'Everlast', 'Empire', 'Posigen'],
  Referral: ['Customer', 'Employee'],
  Mailing: ['HW'],
};

const capeLightZips = [
  '02532',
  '02561',
  '02559',
  '02534',
  '02556',
  '02540',
  '02543',
  '02536',
  '02542',
  '02644',
  '02563',
  '02537',
  '02649',
  '02635',
  '02648',
  '02668',
  '02655',
  '02632',
  '02601',
  '02630',
  '02673',
  '02675',
  '02664',
  '02670',
  '02639',
  '02660',
  '02638',
  '02637',
  '02641',
  '02671',
  '02646',
  '02645',
  '02631',
  '02659',
  '02633',
  '02650',
  '02653',
  '02642',
  '02667',
  '02666',
  '02652',
  '02657',
  '02663',
  '02562',
];

const eplusElectricZips = [
  '01373',
  '01376',
  '01237',
  '01349',
  '01088',
  '01260',
  '01224',
  '01347',
  '01066',
  '01242',
  '01201',
  '01002',
  '01301',
  '01003',
  '01226',
  '01238',
  '01035',
  '01375',
  '01225',
  '01038',
  '01351',
  '01342',
  '01093',
  '01240',
  '02790',
  '02791',
  '02571',
  '02576',
  '02538',
  '02558',
  '01073',
  '01360',
  '01223',
  '01050',
  '01337',
  '01235',
  '01034',
  '01054',
  '01341',
  '01340',
  '01354',
  '01330',
  '01071',
  '01098',
  '01008',
  '01026',
  '01254',
  '01270',
  '01255',
  '01253',
  '01256',
  '01070',
  '01012',
  '01243',
  '01264',
  '01338',
  '01011',
  '01344',
  '01370',
  '01029',
  '01084',
  '01097',
  '02360',
  '02740',
  '02050',
  '02745',
  '02021',
  '02067',
  '02719',
  '02332',
  '02746',
  '02364',
  '02330',
  '02744',
  '02347',
  '02743',
  '02739',
  '02770',
  '02738',
  '02367',
  '02571',
  '02066',
  '02359',
  '02790',
  '01030',
  '01151',
  '02717',
  '02702',
  '02366',
  '02747',
  '02748',
  '02346',
  '02047',
  '01109',
];

const eplusGasZips = [
  '01109',
  '01020',
  '01089',
  '01108',
  '01104',
  '01013',
  '01056',
  '01027',
  '01001',
  '01106',
  '01118',
  '01119',
  '01105',
  '01107',
  '01077',
  '01129',
  '01128',
  '01022',
  '01103',
  '01199',
  '01075',
  '01028',
  '01095',
  '01057',
  '01069',
  '01033',
  '01036',
  '02360',
  '02740',
  '02050',
  '02745',
  '02021',
  '02067',
  '02719',
  '02332',
  '02746',
  '02364',
  '02330',
  '02744',
  '02347',
  '02743',
  '02739',
  '02770',
  '02738',
  '02367',
  '02066',
  '02359',
  '02780',
  '02048',
  '02767',
  '02779',
  '02715',
  '02301',
  '02703',
  '02368',
  '02302',
  '02072',
  '02324',
  '02766',
  '02771',
  '02339',
  '02333',
  '02769',
  '02061',
  '02343',
  '02338',
  '02379',
  '02322',
  '01030',
  '01151',
  '02717',
  '02702',
  '02366',
  '02047',
  '02718',
  '02764',
  '02035',
  '02356',
  '02375',
  '01080',
  '02763',
  '02357',
  '01009',
  '01079',
  '02747',
  '02748',
  '02346',
  '02341',
];

const scheduleEventScript = {
  MA: {
    title:
      'There is a 1-2 hour arrival window for every appointment we offer. The Home Energy Specialist can arrive at the early end or late end of that arrival window. If they are arriving outside the arrival window, someone will call to let you know.',
    arrivalDetails: [
      {
        eventTypeHeader: 'For CAP HEAs:',
        appointmentTimingDetails: {
          header: 'Appointment Time',
          timings: ['8:00 AM', '11:30 AM'],
        },
        arrivalWindowDetails: {
          header: 'Arrival Window',
          timings: ['8:00 AM - 9:00 AM', '10:30 AM - 12:30 PM'],
        },
      },
      {
        eventTypeHeader: 'For Single Families:',
        appointmentTimingDetails: {
          header: 'Appointment Time',
          timings: ['8:00 AM', '11:00 AM', '2:00 PM'],
        },
        arrivalWindowDetails: {
          header: 'Arrival Window',
          timings: ['8:00 AM - 9:00 AM', '10:00 AM - 12:00 AM', '1:00 PM - 2:00 PM'],
        },
      },
      {
        eventTypeHeader: 'For Multi Families:',
        appointmentTimingDetails: {
          header: 'Appointment Time',
          timings: ['8:00 AM', '11:30 AM', '3:00 PM'],
        },
        arrivalWindowDetails: {
          header: 'Arrival Window',
          timings: ['8:00 AM - 9:00 AM', '10:30 AM - 12:30 PM', '2:00 PM - 3:00 PM'],
        },
      },
    ],
  },
  CT: { title: '', arrivalDetails: [] },
};

export {
  maxNumberOfUnits,
  renovationsRecently,
  duplicateTableSteps,
  leadIntakeTabs,
  leadIntakeTabsCT,
  electricProvidersOptions,
  heatingFuelOptions,
  gasProviderOptions,
  eplusElectricZips,
  eplusGasZips,
  capeLightZips,
  scheduleEventScript,
  homeAssesmentRecently,
  houseBuiltAndArea,
  leadSourceOptionsMA,
  leadSourceOptionsCT,
  leadSourceDetailOptionsCT,
  leadTypeOptions,
};
