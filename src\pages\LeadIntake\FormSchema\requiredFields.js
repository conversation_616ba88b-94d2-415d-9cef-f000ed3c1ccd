import {
  validateHeatingFuel,
  validateElectricProvider,
  validateGasProvider,
} from '@utils/leadIntake/leadintake';
import { validatePhoneNumber, validateEmail } from '@utils/functions';

const getLeadIntakeRequiredFields = (formValues = {}) => {
  const {
    atAnEvent,
    preferredLanguage,
    numUnitsSchedulingToday,
    singleOrMultiFamily,
    heaOrHvac,
    condoAssociation,
    electricProvider,
    heatingFuel,
    occupantType,
    customerFirstName,
    customerLastName,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerAddress,
    customerEmail,
    emailOptOut,
    campaignId,
    isCampaignIdValid = false,
    callSource,
    leadSource,
    gasProvider,
    howManyUnitsAreInAssociation,
    discountedRateCodeForCIA,
    unitNumber,
  } = formValues;

  const validateParams = {
    numUnitsSchedulingToday,
    heatingFuel,
    electricProvider,
    gasProvider,
    heaOrHvac,
  };

  const heatingFuelValidation = () => {
    const valid = validateHeatingFuel(numUnitsSchedulingToday, heatingFuel);
    return valid;
  };

  const electricProviderValidation = () => {
    const valid = validateElectricProvider(validateParams);
    return valid;
  };

  const gasProviderValidation = () => {
    const valid = validateGasProvider(validateParams);
    return valid;
  };

  const requiredFields = {
    preliminaryQuestions: {
      'Campaign ID': atAnEvent === 'Yes' ? campaignId : true,
      'Preferred Language': preferredLanguage,
      'Single Or Multi Family': singleOrMultiFamily,
      'Hea Or Hvac': heaOrHvac,
      'Condo Association': condoAssociation,
      'Electric Provider': electricProviderValidation(),
      'Gas Provider': gasProviderValidation(),
      'Heating Fuel': heatingFuelValidation(),
      'Occupant Type': occupantType?.[0],
      'Condo Units Association': !(
        condoAssociation === 'Yes' && howManyUnitsAreInAssociation === '5+'
      ),
    },
    customerInfo: {
      'First Name': customerFirstName,
      'Last Name': customerLastName,
      'Primary Phone Number': validatePhoneNumber(customerPrimaryPhoneNumber),
      'Phone Type': customerPrimaryPhoneNumberType,
      Address: customerAddress,
      Email: emailOptOut ? true : validateEmail(customerEmail),
    },
    sourceInfo: {
      'Call Source': callSource,
      'Lead Source': leadSource,
      'Discounted Rate': heaOrHvac === 'HEA' ? discountedRateCodeForCIA : true,
    },
    review: {},
  };

  if (Number(numUnitsSchedulingToday) > 1) {
    for (let itr = 0; itr < Number(numUnitsSchedulingToday); itr++) {
      requiredFields.preliminaryQuestions[`Unit Number For Unit ${itr + 1} `] = unitNumber?.[itr];
      requiredFields.preliminaryQuestions[`Occupant Type For Unit ${itr + 1} `] =
        occupantType?.[itr];
      requiredFields.preliminaryQuestions[`Heating Fuel For Unit ${itr + 1} `] = heatingFuel?.[itr];
      requiredFields.preliminaryQuestions[`Electric Provider For Unit ${itr + 1} `] =
        electricProvider?.[itr];
      if (heatingFuel?.[itr] === 'Gas') {
        requiredFields.preliminaryQuestions[`Gas Provider For Unit ${itr + 1} `] =
          gasProvider?.[itr];
      }
    }
  }

  const requiredFieldsForBAs = {
    preliminaryQuestions: requiredFields.preliminaryQuestions,
    customerInfo: requiredFields.customerInfo,
    review: {},
    openSlots: {},
    wrapUp: {},
  };
  return isCampaignIdValid && atAnEvent === 'Yes' ? requiredFieldsForBAs : requiredFields;
};

const getLeadIntakeRequiredFieldsCT = (formValues = {}) => {
  const {
    hadPreviousAssessmentCT,
    // howLongAgoEnergyAssesmentConducted,
    // receivedFinancialAssistance,
    // programEnrolled,
    // houseHoldSize,
    // houseHoldMaxAnnualIncome,
    // openConstructionRemodeling,
    // anyMoldAsbestosORVermiculitePresent,
    singleOrMultiFamily,
    customerFirstName,
    customerLastName,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerAddress,
    customerEmail,
    textMessageOptIn,
    emailOptOut,
    leadSourceCT,
    leadType,
    houseBuilt,
    occupantType,
    squareFeet,
    receivedFinancialAssistanceFromUtility,
    financialAssistanceFromState,
    openConstructionRemodeling,
    heatingFuelCT,
    electricProviderCT,
    leadSentToCompany,
    customerIEPreApproved,
  } = formValues;
  return {
    customerInfo: {
      'First Name': customerFirstName,
      'Last Name': customerLastName,
      Address: customerAddress,
      'Invalid Address': customerAddress?.displayAddress,
      Email: emailOptOut ? true : validateEmail(customerEmail),
      'Primary Phone Number': validatePhoneNumber(customerPrimaryPhoneNumber),
      'Phone Type': customerPrimaryPhoneNumberType,
      'Text Message Consent': textMessageOptIn,
      'Lead Sent to Company': leadSentToCompany,
    },
    preliminaryQuestions: {
      'Previous Energey Assesment Performed': hadPreviousAssessmentCT,
      'Single Multi Family': singleOrMultiFamily,
      'Year Built': houseBuilt,
      'Occupant Type': occupantType,
      'Square Feet': squareFeet,
      'Financial Assistance from Utility': customerIEPreApproved
        ? true
        : receivedFinancialAssistanceFromUtility,
      'Financial Assistance from State': customerIEPreApproved
        ? true
        : financialAssistanceFromState,
      'Open Construction/Remodeling': openConstructionRemodeling,
      'Heating Fuel': heatingFuelCT,
      'Electric Provider': electricProviderCT,
      // 'LAst energy Assessment Condicted': howLongAgoEnergyAssesmentConducted,
      // 'Financial Assistance': receivedFinancialAssistance,
      // 'Programmed Enrolled': programEnrolled,
      // 'Household Size': houseHoldSize,
      // 'Household Maximum Annual Income': houseHoldMaxAnnualIncome,
      // 'Open Construction Remodeling': openConstructionRemodeling,
      // 'Mold, Asbestos or Vermiclite': anyMoldAsbestosORVermiculitePresent,
    },
    sourceInfo: {
      'Lead Source': leadSourceCT,
      'Lead Type': leadType,
    },
    review: {},
  };
};

export { getLeadIntakeRequiredFields, getLeadIntakeRequiredFieldsCT };
