import React, { useEffect } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { selectedEventState, availableSlotsAtom, selectedSlotsState } from '@recoil/eventSidebar';

import { Header } from '@components/global';
import { PrimaryButton } from '@components/global/Buttons';

import ScrollingSlotsList from './ScrollingSlotsList';
import DaySlot from './DaySlot';
import TimeSlot from './TimeSlot';
import AgentSlot from './AgentSlot';
import SelectedSlots from './SelectedSlots';

const AvailableSlotsContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  width: 100%;
`;

const NoSlotsMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const AddMultiSlotButton = styled(PrimaryButton)``;

const AvailableSlots = ({ singleAgent, allowAgentSelect, allowMultiSlotSelect }) => {
  // TODO: create fallback for no slots available
  const [selectedSlot, setSelectedSlot] = useRecoilState(selectedEventState);
  const [selectedSlots, setSelectedSlots] = useRecoilState(selectedSlotsState);
  const availableSlots = useRecoilValue(availableSlotsAtom);

  const {
    date: selectedDate,
    startTime: selectedTime,
    oids: [oid],
    type,
    numUnit,
    notes: { officeNotes },
  } = selectedSlot;

  useEffect(() => {
    const updatedSlots = selectedSlots.map((slot) => {
      return { ...slot, notes: { officeNotes } };
    });
    setSelectedSlots(updatedSlots);
  }, [officeNotes, setSelectedSlots]); // Adding selectedSlots here would cause an infinite loop

  const resetSelectedSlot = () =>
    setSelectedSlot({
      ...selectedSlot,
      date: null,
      startTime: null,
      endTime: null,
      oid: null,
      displayName: null,
    });

  useEffect(() => {
    // Need to reset these the first time or they will be the same as the existing event
    resetSelectedSlot();
  }, []);

  if (!availableSlots) return null;

  const handleSelectForMultiSlot = () => {
    setSelectedSlots([...selectedSlots, selectedSlot]);
    // Resetting the selectedSlot (selectedEventState) works to allow them to select the next slot
    // However, it causes issues in BookSlotsButton (maybe elsewhere too?) when trying to book the slots after the last one is selected
    // So only reset if it is not the last slot
    // This doesn't have access to the updated selectedSlots yet, so add 1
    if (numUnit !== selectedSlots.length + 1) resetSelectedSlot();
  };

  const getAgentLabel = (type) => {
    if (!type) return 'Available Agents';
    switch (type.slice(0, 4)) {
      case '0100':
        return 'Available Agents';
      default:
        return 'Available Trucks';
    }
  };

  const renderTimeSlots = () => {
    const times = availableSlots[selectedDate]
      ? Object.keys(availableSlots[selectedDate])?.sort()
      : [];
    return (
      <ScrollingSlotsList numSlotsToDisplay={4}>
        {times.map((time) => (
          <TimeSlot
            key={`${selectedDate} ${time}`}
            slot={{
              date: selectedDate,
              startTime: time,
              openEnd: availableSlots[selectedDate]?.[time]?.[0]?.openEnd,
              agentName: allowAgentSelect
                ? null
                : availableSlots[selectedDate]?.[time]?.[0]?.displayName,
              oid: allowAgentSelect ? null : availableSlots[selectedDate]?.[time]?.[0]?.oid,
            }}
          />
        ))}
      </ScrollingSlotsList>
    );
  };

  const renderAgentSlots = (type) => {
    const slots = availableSlots[selectedDate]?.[selectedTime];
    if (!slots) return null;
    return (
      <>
        <Header h3>{getAgentLabel(type)}</Header>
        <ScrollingSlotsList numSlotsToDisplay={5}>
          {slots
            .map((slot) =>
              !selectedSlots.find((selectedSlot) => {
                // Don't display the agent if it is a multi slot select and it is already selected
                return (
                  selectedSlot.date === selectedDate &&
                  selectedSlot.startTime === selectedTime &&
                  selectedSlot.oids[0] === slot.oid
                );
              }) ? (
                <AgentSlot
                  key={`${slot.oid} ${selectedDate} ${selectedTime}`}
                  slot={{ ...slot, date: selectedDate, startTime: selectedTime }}
                  singleAgent={singleAgent}
                  allowMultiSlotSelect={allowMultiSlotSelect}
                />
              ) : null,
            )
            .filter((slot) => slot)}
        </ScrollingSlotsList>
      </>
    );
  };

  const dates = Object.keys(availableSlots);

  const showAvailableSlots =
    (dates.length > 0 && !allowMultiSlotSelect) ||
    (allowMultiSlotSelect && selectedSlots.length !== numUnit);

  return (
    <AvailableSlotsContainer>
      {showAvailableSlots && (
        <>
          <Header h3>Available Slots</Header>
          <ScrollingSlotsList numSlotsToDisplay={6}>
            {dates.map((date) => (
              <DaySlot key={date} date={date} />
            ))}
          </ScrollingSlotsList>
          {selectedDate && renderTimeSlots()}
          {selectedTime && allowAgentSelect && renderAgentSlots(type)}
          {allowMultiSlotSelect && selectedDate && selectedTime && oid && (
            <AddMultiSlotButton onClick={handleSelectForMultiSlot}>Select Slot</AddMultiSlotButton>
          )}
        </>
      )}

      {allowMultiSlotSelect && <SelectedSlots />}

      {dates.length === 0 && (
        <NoSlotsMessage>No slots available. Please reset your search query.</NoSlotsMessage>
      )}
    </AvailableSlotsContainer>
  );
};

AvailableSlots.propTypes = {
  singleAgent: PropTypes.bool,
  allowAgentSelect: PropTypes.bool,
  allowMultiSlotSelect: PropTypes.bool,
};

AvailableSlots.defaultProps = {
  singleAgent: false,
  allowAgentSelect: true,
  allowMultiSlotSelect: false,
};

export default AvailableSlots;
