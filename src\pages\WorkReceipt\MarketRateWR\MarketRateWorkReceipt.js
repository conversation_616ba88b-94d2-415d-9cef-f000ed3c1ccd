import React from 'react';
import PropTypes from 'prop-types';
import DataIntakeForm from '@components/DataIntakeForm';
import { useRecoilValue, useRecoilState } from 'recoil';
import { activeTabState, formValuesState, activeFormState } from '@recoil/dataIntakeForm';
import { workReceiptValuesState } from '@recoil/workreceipt';
import { useTabsAndFieldsForWR } from './useTabsAndFieldsForWR';

export const MarketRateWorkReceipt = ({ isDealLoaded }) => {
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const activeForm = useRecoilValue(activeFormState);

  const {
    resultingSingleOrMulti,
    hasReturnVisit = false,
    wasDhwQuoted = 'No',
    crawlSpaceSpec = '',
    atticSpec = '',
    kwSpec = '',
    miscVentSpec = '',
    wallSpec = '',
    anyPrewxSpec = '',
    areasOnWorkscope = [],
    docRepoStatus = 0,
  } = formValues[activeForm] || {};

  const isBadSpec = [
    crawlSpaceSpec,
    atticSpec,
    kwSpec,
    miscVentSpec,
    wallSpec,
    anyPrewxSpec,
  ].includes('Bad Spec');

  const activeTab = useRecoilValue(activeTabState);

  const updateFieldValues = (values, index = null) => {
    setFormValues({ ...values, index: index || activeForm });
  };

  const handleFieldChange = (e) => {
    updateFieldValues({
      [e.target.name]: e.target.type === 'number' ? parseFloat(e.target.value) : e.target.value,
      index: activeForm,
    });
  };

  const { workReceiptTabs, workReceiptMap, workReceiptFields } = useTabsAndFieldsForWR(
    handleFieldChange,
    {
      resultingSingleOrMulti,
      isDealLoaded,
      hasReturnVisit,
      wasDhwQuoted,
      isBadSpec,
      activeTab,
      areasOnWorkscope,
      docRepoStatus,
    },
  );

  return (
    <DataIntakeForm
      map={workReceiptMap}
      fields={workReceiptFields}
      valuesState={workReceiptValuesState}
      tabs={workReceiptTabs}
    />
  );
};

MarketRateWorkReceipt.defaultProps = {
  isDealLoaded: false,
};

MarketRateWorkReceipt.propTypes = {
  isDealLoaded: PropTypes.bool,
};
