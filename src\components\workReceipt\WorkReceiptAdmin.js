import React, { useCallback, useRef, memo } from 'react';
import styled from 'styled-components';
import { WorkReceiptManager } from '@utils/APIManager';
import Swal from 'sweetalert2';
import { FormSelect } from '@components/global/Form';
import { useRecoilValue } from 'recoil';
import { statesSelector } from '@recoil/app';
import { PageHeader } from '@pages/Components';
import { CloudUpload } from '@styled-icons/bootstrap/CloudUpload';
import { FileEarmarkPdf } from '@styled-icons/bootstrap/FileEarmarkPdf';
import { PrimaryButton, CancelButton as ResetButton } from '@components/global/Buttons';
import { Trash } from '@styled-icons/bootstrap/Trash';
import PropTypes from 'prop-types';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import { workReceiptModeOptions } from '@pages/WorkReceipt/consts';

const ButtonContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const UploadBox = styled.div`
  width: 350px;
  height: 300px;
  border: 2px dashed #aaa;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #007bff;
  }

  @media (max-width: 415px) {
    width: 265px;
  }
`;

const PaperBackground = styled.div`
  background-color: #fff;
  padding: 10px;
`;

const UploadingIcon = styled(CloudUpload)`
  height: 92px;
  width: 92px;
  color: ${({ theme }) => theme.colors.eventA};
`;

const HiddenInput = styled.input`
  display: none;
`;

const FileDisplayContainer = styled.div`
  position: relative;
  width: 100%;
  justify-content: space-between;
  max-width: 350px;
  padding: 8px;
  background-color: #ffffff;
  border: 1px solid #dfe1e6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-bottom: 1.5em;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #007bff;
  }
`;

const FileIcon = styled(FileEarmarkPdf)`
  width: 24px;
  color: ${({ theme }) => theme.colors.eventI};
  margin-right: 10px;
`;

const DeleteIcon = styled(Trash)`
  width: 24px;
  color: ${({ theme }) => theme.colors.eventI};
  margin-right: 10px;
`;

const FileName = styled.span`
  font-size: 12px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const WorkReceiptAdminContainer = styled.div`
  display: flex;
  gap: 32px;
  width: inherit;
  flex-wrap: wrap;
`;

const BoxContainer = styled.div``;

const UploadBoxIconContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const IconText = styled.span``;
const List = styled.li``;
const ListContainer = styled.ul``;
const FormItemContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  width: 350px;
`;

const FileItem = memo(({ fileName, removeFile }) => {
  const [showDeleteIcon, setShowDeleteIcon] = React.useState(false);
  return (
    <FileDisplayContainer
      onMouseEnter={() => setShowDeleteIcon(true)}
      onMouseLeave={() => setShowDeleteIcon(false)}
    >
      <BoxContainer>
        <FileIcon />
        <FileName>{fileName}</FileName>
      </BoxContainer>
      {showDeleteIcon && <DeleteIcon onClick={removeFile} />}
    </FileDisplayContainer>
  );
});

const WorkReceiptAdmin = () => {
  const fileInputRef = useRef(null);
  const [state, setState] = React.useState('');
  const [type, setType] = React.useState('');
  const [file, setFile] = React.useState('');
  const [fileName, setFileName] = React.useState('');
  const statesOptions = useRecoilValue(statesSelector);

  const handleUploadWorkReceipt = useCallback(async (event) => {
    const { files } = event.target;

    if (files.length > 1) {
      return Swal.fire({
        title: 'File Upload Error',
        text: 'You can only upload one file at a time.',
        icon: 'error',
        confirmButtonText: 'OK',
      });
    }

    const file = files[0];
    const fileExtension = file.name
      .split('.')
      .pop()
      .toLowerCase();
    if (fileExtension !== 'xlsm') {
      return Swal.fire({
        title: 'File Type Error',
        text: 'Only .xlsm files are allowed.',
        icon: 'error',
        confirmButtonText: 'OK',
      });
    }
    const data = new FormData();
    data.append('file', files[0]);
    setFileName(files[0].name);
    return setFile(data);
  }, []);

  const handleSubmit = useCallback(() => {
    if (!state || !type || !file) {
      return Swal.fire({
        title: 'Form Submission Error: Required Fields Missing',
        html: parseJSXContentForSwalPopup(
          <>
            The following fields are mandatory and must be filled in to proceed:
            <ListContainer>
              <List>
                <strong>State:</strong> Please select the appropriate state for this entry.
              </List>
              <List>
                <strong>Work Receipt Type:</strong> Specify the type of work receipt to ensure
                proper processing.
              </List>
              <List>
                <strong>File Upload:</strong> Attach the necessary file(s) for this submission.
              </List>
            </ListContainer>
            Without completing these fields, you will not be able to continue.
          </>,
        ),
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
    }

    return WorkReceiptManager.uploadWorkReceiptTemplate({
      file,
      state,
      type,
    });
  }, [file, state, type]);

  const handleClick = useCallback(() => {
    fileInputRef.current.click();
  }, []);

  const handleReset = useCallback(() => {
    setState('');
    setType('');
    setFile('');
    setFileName('');
  }, []);

  const removeFile = useCallback(() => {
    setFile('');
    setFileName('');
  }, []);

  return (
    <>
      <PageHeader>Work Receipt Template Uploader</PageHeader>
      <PaperBackground>
        <WorkReceiptAdminContainer>
          <BoxContainer>
            <UploadBox onClick={handleClick}>
              <UploadBoxIconContainer>
                <UploadingIcon />
                <IconText>Click to select</IconText>
              </UploadBoxIconContainer>
            </UploadBox>
            <HiddenInput type="file" ref={fileInputRef} onChange={handleUploadWorkReceipt} />
          </BoxContainer>

          <FormItemContainer>
            {file && <FileItem fileName={fileName} removeFile={removeFile} />}
            <FormSelect
              required
              title="States"
              placeholder="Select States"
              name="state"
              value={state}
              onChange={(e) => setState(e.target.value)}
              options={statesOptions}
            />
            <FormSelect
              required
              title="Workreceipt Type"
              placeholder="Select Type"
              name="wrType"
              value={type}
              onChange={(e) => setType(e.target.value)}
              options={workReceiptModeOptions}
            />
            <ButtonContainer>
              <ResetButton onClick={handleReset}>Reset</ResetButton>
              <PrimaryButton onClick={handleSubmit}>Submit</PrimaryButton>
            </ButtonContainer>
          </FormItemContainer>
        </WorkReceiptAdminContainer>
      </PaperBackground>
    </>
  );
};
WorkReceiptAdmin.propTypes = {};

WorkReceiptAdmin.defaultProps = {};

FileItem.propTypes = {
  fileName: PropTypes.string.isRequired,
  removeFile: PropTypes.func.isRequired,
};

export default WorkReceiptAdmin;
