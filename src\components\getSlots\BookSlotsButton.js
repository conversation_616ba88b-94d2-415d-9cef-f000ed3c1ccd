import React from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';

import { selectedEventState, availableSlotsAtom, selectedSlotsState } from '@recoil/eventSidebar';
import { SecondaryButton } from '@components/global/Buttons/Buttons';

const BookSlotsButton = ({ handleBookSlots, allowAgentSelect, perUnitMultFamilyEvents }) => {
  const availableSlots = useRecoilValue(availableSlotsAtom);
  const selectedEvent = useRecoilValue(selectedEventState);
  const selectedSlots = useRecoilValue(selectedSlotsState);
  if (!availableSlots) return null;

  const {
    date: selectedDate,
    startTime: selectedTime,
    oids: selectedAgents,
    lock,
    notes,
    numUnit,
  } = selectedEvent;

  const slots = availableSlots[selectedDate]?.[selectedTime];
  if (!slots?.length) return null;
  if (lock && !notes?.fieldNotes) return null;
  // Don't show book slots button on multi slot select unless they have selected all slots for the units
  if (perUnitMultFamilyEvents && selectedSlots.length !== numUnit) return null;
  if (allowAgentSelect && !selectedAgents.length) return null;
  return (
    <SecondaryButton center onClick={handleBookSlots}>
      Book slots
    </SecondaryButton>
  );
};

BookSlotsButton.propTypes = {
  handleBookSlots: PropTypes.func.isRequired,
  allowAgentSelect: PropTypes.bool,
  perUnitMultFamilyEvents: PropTypes.bool,
};

BookSlotsButton.defaultProps = {
  allowAgentSelect: true,
  perUnitMultFamilyEvents: false,
};

export default BookSlotsButton;
