import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue } from 'recoil';
import { formSettingsState, formValuesState } from '@recoil/dataIntakeForm';
import DataIntakeFormElement from './DataIntakeFormElement';

const FieldsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const PerUnitContainer = styled.div`
  margin-left: 12px;
  display: flex;
  flex-direction: column;
`;

const PerUnitFieldContainer = styled.div`
  margin-left: 12px;
`;

const DataIntakeFormFields = (props) => {
  const { fields, isCollapsed, unitNum, readOnlyForReview } = props;
  const { numUnit } = useRecoilValue(formSettingsState);
  const formValues = useRecoilValue(formValuesState);
  if (!formValues) return null;

  /**
   * This function is used by the multifamily work receipt
   *
   * If a field is labeled as 'perUnit', it renders the requested field {numUnit} times
   *
   * @param {object} field
   * @returns jsx
   */
  const renderPerUnit = (field) => {
    const { perUnit, fieldName, fieldType, readOnly = false } = field;

    const supportedTypes = ['checkbox', 'radio'];
    const isSupportedType = supportedTypes.includes(fieldType);

    // unitNum --> If the field already has a unit number, we have already rendered it through a repeated 'section' and don't need to duplicate it here
    // perUnit --> If the field is not 'perUnit', we don't need to duplicate (answer applies for all units)
    // isSupportedType --> If we don't support the type, don't repeat
    // TODO: might need to think if '!isSupportedType' should throw an error or if there should just be a different way of handling different types of fields)
    if (unitNum || !perUnit || !isSupportedType) return null;
    return (
      <PerUnitContainer>
        {[...Array(numUnit)].map((_, index) => {
          const unitNum = index + 1;
          return (
            <PerUnitFieldContainer key={`${fieldName}${unitNum}`}>
              <DataIntakeFormElement
                {...field}
                unitNum={unitNum}
                text={`Unit ${unitNum}`}
                readOnly={readOnly}
              />
            </PerUnitFieldContainer>
          );
        })}
      </PerUnitContainer>
    );
  };
  const renderFields = () => {
    return fields.map((field) => {
      let isReadOnly = field?.readOnly || false;
      if (field?.conditionalDisable && !isReadOnly) {
        // The Conditional Disable is passed from the formSchema. It allows us to make other fields Disabled if certain conditional dependencies are met.
        // For example, if the "Information on All Unit" field is a checkbox,
        // we can Disable the fields related to the "Information Unit" field within the Conditional Disable.
        isReadOnly = field?.conditionalDisable(formValues, unitNum - 1) || false;
      }
      const isDisable = readOnlyForReview || isReadOnly;
      return (
        <React.Fragment key={field.name}>
          <DataIntakeFormElement {...field} unitNum={unitNum} readOnly={isDisable} />
          {renderPerUnit(field)}
        </React.Fragment>
      );
    });
  };
  // TODO: implement transition for isCollapsed.
  // Since you need a set height to transition to, it takes more work than im getting into right now
  return <FieldsContainer isCollapsed={isCollapsed}>{renderFields()}</FieldsContainer>;
};

DataIntakeFormFields.propTypes = {
  fields: PropTypes.arrayOf(PropTypes.shape({ perUnit: PropTypes.bool })).isRequired,
  isCollapsed: PropTypes.bool,
  // unitNum comes from a section that is 'perUnit', or will not exist
  unitNum: PropTypes.number,
  readOnlyForReview: PropTypes.bool,
};

DataIntakeFormFields.defaultProps = {
  isCollapsed: false,
  unitNum: 1,
  readOnlyForReview: false,
};

export default DataIntakeFormFields;
