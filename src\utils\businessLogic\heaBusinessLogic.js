const returnVisitReasons = [
  'Additional ISMs',
  'Collect EBR Information',
  'New CST',
  '(Re)Spec New/Additional Work',
  'VEA QC',
  'Other',
];

const cancelReasons = [
  'Customer Cancellation Request',
  'Unqualified - Had Previous Assessment',
  'Scheduling Error',
  'Unqualified - Other',
  'Overbooking Cancel',
  'Unqualified - 5+ Units',
  'HEA Not Approved',
  '<PERSON><PERSON>',
  'Visit Not Confirmed',
  'Unqualified - Low Income',
  'Approval / Task Not Completed in Time',
  'Online scheduling not confirmed',
  'Approval - Customer Unresponsive',
  'CAP Vetting Reschedule',
  'Sent to Local CAP',
  'Not Approved - Incorrect Program',
  'Name on Bill Incorrect',
];

const rescheduleReasons = [
  'Customer No-Show',
  'Customer Reschedule Request',
  'HES Cancel',
  'Cancel At Door',
  'Unqualified - Low Income',
  'Unqualified - Had Previous Assessment',
  'Unqualified - 5+ Units',
  'Unqualified - Other',
  'Scheduling Error',
  'Overbooking Cancel',
  'HEA Not Approved',
  'Covid <PERSON>',
  'Visit Not Confirmed',
  'Online scheduling not confirmed',
  'Approval / Task Not Completed in Time',
  'Approval - Customer Unresponsive',
  'CAP Vetting Reschedule',
  'Sent to Local CAP',
  'Not Approved - Incorrect Program',
  'Name on Bill Incorrect',
];

const leadVendors = [
  '',
  'Abode',
  'CleaResult',
  'RISE',
  'RISE on Cape',
  'RISE – IIC',
  'CleaResult – IIC',
  'Berkshire - IIC',
];

const capRestrictedDocs = [
  'Check (CHK)',
  'Plan View (PV)',
  'Pre QC Plan View (PQCPV)',
  'Pre QC Ventilation Requirements (PQCVENT)',
  'Ventilation Requirements (VENT)',
  'Work Order',
  'Work Order revised (Work_Order_revised)',
];

const returnVisits = ['000004', '000005'];

export {
  returnVisitReasons,
  cancelReasons,
  rescheduleReasons,
  returnVisits,
  leadVendors,
  capRestrictedDocs,
};
