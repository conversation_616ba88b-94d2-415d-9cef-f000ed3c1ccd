import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HesWxWeeklyIncentiveForm = ({ record }) => {
  const { finalContractAmount, recordCount, wxIncentive } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput
            readOnly
            name="recordCount"
            value={recordCount}
            title="Record Count"
            placeholder=""
          />
          {wxIncentive && (
            <FormInput
              readOnly
              name="wxIncentive"
              value={wxIncentive}
              title="Insulation Incentive"
              placeholder=""
            />
          )}
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HesWxWeeklyIncentiveForm.propTypes = {
  record: PropTypes.shape({
    finalContractAmount: PropTypes.string,
    recordCount: PropTypes.string,
    wxIncentive: PropTypes.string,
  }),
};

HesWxWeeklyIncentiveForm.defaultProps = {
  record: {},
};

export default HesWxWeeklyIncentiveForm;
