import React, { memo } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { calculateVentMathRound } from '@utils/functions';
import { TableHeader, TableCell, TableRow, Table } from './TableCellsColors';
import { ventingOptions } from '../consts';

const InputField = styled.input`
  width: 80px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
`;

const LowVentingContainer = styled.div`
  flex-grow: 1;
`;

export const LowVenting = memo(({ handleVentingChange, formValues }) => {
  const { lowVentingTotal, balanced, hiLo } = formValues;
  const calculateCellValue = ({ fieldName, price }) =>
    calculateVentMathRound(formValues[fieldName] * price * 100) / 100;
  return (
    <LowVentingContainer>
      <Table>
        <thead>
          <TableRow>
            <TableHeader colSpan="4">Low Venting</TableHeader>
          </TableRow>
        </thead>
        <tbody>
          {ventingOptions.lowVenting.map((option) => (
            <TableRow key={`lowVenting-${option.name}-key`}>
              <TableCell>
                <InputField
                  data-price={option.price}
                  data-parent={option.parent}
                  data-ventingname="lowVenting"
                  type="number"
                  name={option.fieldName}
                  value={formValues[option.fieldName]}
                  onChange={handleVentingChange}
                />
              </TableCell>
              <TableCell whiteSpace="nowrap" margin={0}>
                {option.name}
              </TableCell>
              <TableCell>{option.price}</TableCell>
              <TableCell>{calculateCellValue(option)}</TableCell>
            </TableRow>
          ))}
          <TableRow>
            <TableCell />
            <TableCell fontWeight={700} textAlign="right">
              total:
            </TableCell>
            <TableCell />
            <TableCell>{lowVentingTotal.toFixed(2)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              % (1:300)
            </TableCell>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              {Math.round((Number(lowVentingTotal) / Number(balanced)) * 100) || 0}%
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              % (1:150)
            </TableCell>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              {Math.round((Number(lowVentingTotal) / Number(hiLo)) * 100) || 0}%
            </TableCell>
          </TableRow>
        </tbody>
      </Table>
    </LowVentingContainer>
  );
});

LowVenting.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  formValues: PropTypes.objectOf(PropTypes.any).isRequired,
  handleVentingChange: PropTypes.func,
};

LowVenting.defaultProps = {
  handleVentingChange: () => {},
};
