import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const ToolTipParent = styled.div`
  position: relative;
`;

const ToolTipText = styled.div`
  position: absolute;
  z-index: 1;
  top: 100%;
  left: -20px;
  right: -20px;
  color: ${({ theme }) => theme.secondary[700]};
  display: block;
  width: fit-content;
  height: auto;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  border-radius: 6px;
  box-shadow: 0 0.5rem 1rem rgba(#000, 0.15);
  padding: 1rem 1rem;
  text-align: center;
`;

const Tooltip = (props) => {
  const { text } = props;
  return (
    <ToolTipParent>
      <ToolTipText>{text}</ToolTipText>
    </ToolTipParent>
  );
};

Tooltip.propTypes = {
  text: PropTypes.string.isRequired,
};

Tooltip.defaultProps = {};

export default Tooltip;
