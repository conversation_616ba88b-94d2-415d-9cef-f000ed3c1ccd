import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useParams } from 'react-router-dom';
import { useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';

import { ListCard } from '@components/global/ScreenPartitionView';
import { selectedUserState } from '@recoil/admin/users';
import { adminInfoChangesState } from '@recoil/admin';
import { activeTabIndexAtom } from '@recoil/app';

const getNameInitials = (firstname, lastname) => {
  const firstnameInitial = firstname?.[0]?.toUpperCase() || '';
  const lastnameInitial = lastname?.[0]?.toUpperCase() || '';
  return `${firstnameInitial}${lastnameInitial}`;
};

const DepartmentText = styled.span`
  color: ${({ theme }) => theme.secondary[500]};
`;

const CompanyText = styled.span`
  font-weight: 500;
  margin-right: 5px;
`;

const DetailsContainer = styled.p`
  display: flex;
  color: ${({ theme }) => theme.secondary[500]};
  margin-bottom: 0rem;
`;

const UserListCard = (props) => {
  const { user } = props;

  const { oid, displayName, firstname, lastname, departments, company } = user;

  const { oid: selectedUserOid } = useRecoilValue(selectedUserState);
  const { oid: urlOid } = useParams();
  const setActiveTab = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const setSelectedUser = useSetRecoilState(selectedUserState);
  const resetAgentInfoChanges = useResetRecoilState(adminInfoChangesState);

  const handleSelectUser = () => {
    setSelectedUser(user);
    setActiveTab(0);
    resetAgentInfoChanges();
  };

  if (urlOid === oid) handleSelectUser();

  const initials = getNameInitials(firstname, lastname);

  const DetailsNode = (
    <DetailsContainer>
      <CompanyText>{company ? `${company} |` : '- |'}</CompanyText>
      <DepartmentText>{departments.join(', ')}</DepartmentText>
    </DetailsContainer>
  );

  return (
    <ListCard
      onClick={handleSelectUser}
      active={oid === selectedUserOid}
      iconText={initials}
      title={displayName}
      detailsNode={DetailsNode}
    />
  );
};

UserListCard.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
  user: PropTypes.shape({
    oid: PropTypes.string.isRequired,
    firstname: PropTypes.string.isRequired,
    lastname: PropTypes.string.isRequired,
    displayName: PropTypes.string.isRequired,
    company: PropTypes.string,
    departments: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};

UserListCard.defaultProps = {
  location: { search: null },
};

export default UserListCard;
