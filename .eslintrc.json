{"env": {"browser": true, "es6": true}, "extends": ["airbnb", "prettier", "eslint:recommended"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parser": "babel-es<PERSON>", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["react", "react-hooks", "prettier"], "rules": {"comma-dangle": 0, "linebreak-style": ["error", "unix"], "quotes": ["error", "single", "avoid-escape"], "semi": ["error", "always"], "no-console": 0, "no-cycle": 0, "import/no-cycle": 0, "no-self-import": 0, "no-plusplus": 0, "no-use-before-define": 0, "no-shadow": 0, "import/prefer-default-export": 0, "react/jsx-one-expression-per-line": 0, "react/jsx-curly-newline": 0, "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/jsx-props-no-spreading": 0, "react/jsx-wrap-multilines": 0, "react/jsx-indent": 0, "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "prettier/prettier": ["error"]}, "settings": {"import/resolver": {"alias": {"map": [["@assets", "./src/assets"], ["@components", "./src/components"], ["@config", "./src/config"], ["@contexts", "./src/contexts"], ["@utils", "./src/utils"], ["@style", "./src/style"], ["@recoil", "./src/recoil"], ["@hooks", "./src/hooks"], ["@pages", "./src/pages"]], "extensions": [".ts", ".js", ".jsx", ".json"]}}}}