import { useRecoilValue } from 'recoil';
import moment from 'moment';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { calendarTypeAtom } from '@recoil/app';
import { draggingEventAtom } from '@recoil/event';

const useValidateCell = (cell) => {
  const calendarType = useRecoilValue(calendarTypeAtom);
  const draggingEvent = useRecoilValue(draggingEventAtom);

  // If no event being dragged, nothing changes
  if (!draggingEvent) return false;

  const { rowDisabled, date, status } = cell;

  // If the row is disabled, the cell is automatically disabled
  if (rowDisabled) return true;

  const { business: department, state } = decodeEventType(calendarType);

  const { date: previousDateString } = draggingEvent;

  const previousDateMoment = moment(previousDateString, 'MM/DD/YYYY');

  // MA HEA can only reassign via drag and drop. Disable if not the same date
  const isReassign =
    department === 'HEA' && state === 'MA' ? previousDateMoment.isSame(date, 'day') : true;

  // Check if date is closed
  const isClosed = department === 'HEA' ? status === 'closed' : false;

  return !isReassign || isClosed;
};

export default useValidateCell;
