import React from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { useValidateRow } from '@recoil/hooks';
import { calendarTypeAtom } from '@recoil/app';
import { getTimeInDayEveningFormat } from '@utils/functions';

import CalendarCell from './CalendarCell';
import RowTitleCell from './RowTitleCell';
import DisabledOverlay from './DisabledOverlay';

const StyledCalendarRow = styled.div`
  position: relative;
  display: flex;
  justify-content: space-evenly;
  border-bottom: 1px solid ${({ theme }) => theme.colors.calendarBorder};

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    flex-direction: column;
    margin-bottom: 20px;
  }
`;

const CalendarRow = (props) => {
  // Agent's permitted attributes and event types
  const { holidays } = props;
  const calendarType = useRecoilValue(calendarTypeAtom);

  // Don't restrict multifam etc from reassign on hea
  const restrictDndByEventType = !['000000'].includes(calendarType);

  const isDisabled = useValidateRow(props, restrictDndByEventType);

  const renderCells = () => {
    const { rowTitle, oid, startDate, dates, isMonthly, details } = props;

    const populatedDays = isMonthly
      ? []
      : [
          <RowTitleCell key={-1} oid={oid} rowTitle={rowTitle} imageUrl="" details={details}>
            {isDisabled ? <DisabledOverlay /> : null}
          </RowTitleCell>,
        ];
    const date = moment(startDate);
    for (let i = 0; i < 7; i++) {
      const formattedDate = date.format('YYYY-MM-DD');
      const dateInfo = dates[formattedDate];
      if (!dateInfo) return null;
      const { events, status, dayStart = '', dayEnd = '' } = dateInfo;
      const startTime = getTimeInDayEveningFormat(dayStart);
      const endTime = getTimeInDayEveningFormat(dayEnd);
      // Show one whole week
      populatedDays.push(
        <CalendarCell
          key={i}
          oid={oid}
          date={date.clone()}
          events={events}
          status={status}
          isMonthly={isMonthly}
          holiday={holidays[moment(date).format('MM-DD-YYYY')] || null}
          rowDisabled={isDisabled}
          maxAppt={status === 'open' ? dateInfo?.maxAppt : false}
          agentName={rowTitle}
          startTime={startTime}
          endTime={endTime}
        />,
      );
      date.add(1, 'days');
    }
    return populatedDays;
  };

  return <StyledCalendarRow>{renderCells()}</StyledCalendarRow>;
};

CalendarRow.propTypes = {
  dates: PropTypes.shape({
    events: PropTypes.arrayOf(PropTypes.shape({})),
    status: PropTypes.string,
  }).isRequired,
  rowTitle: PropTypes.string,
  oid: PropTypes.string,
  startDate: PropTypes.instanceOf(moment).isRequired,
  isMonthly: PropTypes.bool,
  details: PropTypes.string,
  holidays: PropTypes.objectOf(PropTypes.string),
};

CalendarRow.defaultProps = {
  rowTitle: '',
  oid: '',
  isMonthly: false,
  details: '',
  holidays: {},
};

export default CalendarRow;
