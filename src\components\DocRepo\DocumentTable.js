import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Header from '@components/global/Header';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload, faEye, faTrashAlt, faUpload } from '@fortawesome/free-solid-svg-icons';
import { startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import { DocRepoManager, EventsManager, SalesforceManager } from '@utils/APIManager';
import { getSalesforceUrl } from '@utils/functions';
import { isHWEUser } from '@homeworksenergy/utility-service';
import DocRepoFooterForButtons from './DocRepoFooter';
import { sfMissingDocField } from './DocRepoFuncs';

const StyledDivContainer = styled.div``;
const StyledInputContainer = styled.input``;
const StyledButtonBody = styled.div`
  width: 75%;
`;
const StyledFooterWrapper = styled.div`
  position: fixed;
  bottom: 35px;
`;
const StyledHeaderStatus = styled(Header)`
  font-size: 25px;
  font-weight: bold;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 16px;
  }
`;
const StyledHeaderDisplay = styled(Header)`
  display: flex;
  justify-content: center;
  width: 100%;
  word-break: break-all;
  font-weight: bold;
  color: ${({ theme }) => theme.primary[400]};
  &.doc-checked {
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
    border-radius: 5px 5px 0 0;
  }
  & :hover {
    cursor: pointer;
  }
`;
const StyledEditCellContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 180px;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    flex-direction: column;
  }
`;
const StyledResultingDisplay = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 12px;
  justify-content: space-evenly;
`;
const StyledCellDivider = styled.div`
  height: 1px;
  width: 80%;
  margin: -3px 0 5px 0;
  background-color: ${({ theme }) => theme.primary[400]};
  &.button-divider {
    width: 100%;
  }
`;
const StyleBottomButton = styled.button`
  background: transparent;
  border: none;
  border-top: solid 1px;
  height: 30px;
  cursor: pointer;
  width: 200px;
  font-size: 20px;
  font-weight: bold;
  &:hover {
    border-radius: 0px 0px 20px 20px;
    cursor: pointer;
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
    border-radius: 25px;
  }
`;
const StyledAttributeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 10px;
  width: 90%;
  &.top {
    margin: 0px;
    padding-bottom: 20px;
    padding-top: 15px;
    width: 100%;
  }
  &.bottom {
    margin: 0px;
    padding-top: 15px;
    padding-bottom: 20px;
    width: 100%;
  }
`;
const StyledAttributeLabel = styled.div`
  font-weight: bold;
  font-size: 11px;
  color: ${({ theme }) => theme.secondary[600]};
`;
const StyledAttribute = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 16px;
  line-height: 28px;
  &.width{
    @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
    (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
      width: 200px;
    }
  }
`;
const StyledEditCellWrapper = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 23%;
  justify-content: space-between;
  align-items: center;
  box-shadow: 3px 2px 4px lightgray;
  background-color: white;
  border-radius: 20px;
  margin: 10px 20px 10px 0;
  padding: 10px;
  padding-top: 10px;
  &.uploaded {
    padding: 0px;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    margin-right: 0px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    height: 203px;
  }
`;
const StyledUploadButton = styled.button`
  position: relative;
  overflow: hidden;
  display: inline-block;
  background: transparent;
  border: none;
  border-top: solid 1px;
  width: -webkit-fill-available;
  height: 30px;
  cursor: pointer;
  width: 200px;
  font-size: 20px;
  font-weight: bold;
  &:hover {
    cursor: pointer;
    border-radius: 0px 0px 20px 20px;
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
  }
  input[type='file'] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    &:hover {
      cursor: pointer;
    }
  }
`;
const StyledDivCheckbox = styled.div`
  padding-top: 10px;
  width: 100%;
`;
const StyledDivButton = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 16px;
  line-height: 28px;
  width: 200px;
  font-size: 20px;
  font-weight: bold;
  &:hover {
    cursor: pointer;
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
    border-radius: 25px;
  }
`;

const DocumentTable = (props) => {
  // Imports
  const {
    state,
    department,
    valuesOnSf,
    reqDocsAndQuestionsObj,
    setRenderStatus,
    sfObjectForResulting,
    company,
  } = props;
  const { uniqueId } = props;

  // Resulting Status will always be the first question
  const { currentValue } = valuesOnSf?.[0] || {};
  let showResultingButton = true;
  if (sfObjectForResulting === 'Opportunity') showResultingButton = currentValue === 'Sales Visit';
  // CT Doc Repo Temp Logic
  if (state === 'CT') showResultingButton = false;

  const {
    getDocsFromS3,
    uploadFileToS3,
    downloadFileFromS3,
    deleteFileFromS3,
    uploadRequiredFile,
    getSignedUrl,
  } = DocRepoManager;

  const newReqDocs = () => {
    if (state === 'CT') return [];
    let additionalReqDocs = [];
    const { questions, requiredDocuments, partnerDocuments } = reqDocsAndQuestionsObj;
    for (let k = 0; k < valuesOnSf.length; k++) {
      const { currentValue } = valuesOnSf[k];
      // This loops through each of the questions and if the questionsObj has the value that SF is returning then
      // there is a required document. So we add the document to the list of required documents.
      if (questions[k][currentValue]) {
        additionalReqDocs = [...additionalReqDocs, ...questions[k][currentValue].requiredDoc];
      }
    }
    let returnReqDocs = [...requiredDocuments, ...additionalReqDocs];
    const hweUser = isHWEUser({ company });
    // Partner companies have extra required documents that must be uploaded
    if (!hweUser) returnReqDocs = [...returnReqDocs, ...partnerDocuments];
    return returnReqDocs;
  };

  // State
  const [uploadedDocs, setDocs] = useState({});
  const [reqDoc, setReqDocs] = useState(newReqDocs());
  const [customerName, setCustomerName] = useState('');
  const [formattedAddress, setFormattedAddress] = useState('');

  // Use Effect
  useEffect(() => {
    getCustomerName();
    fetchDocumentsFromS3();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Logic to get CT customer information from Project
  const getCustomerName = async () => {
    if (state !== 'CT') return;

    const objectTypeMap = {
      '006': 'Opportunity',
      a47: 'Project__c',
      a04: 'Project__c',
      a00: 'Barrier__c',
      a1b: 'Barrier__c',
    };

    const objectType = objectTypeMap[uniqueId.substring(0, 3)];

    const response = await SalesforceManager.getObject(
      objectType,
      uniqueId,
      objectType === 'Project__c' ? '1.0' : '2.0',
    );

    if (!response) throwError(`Couldnt find a ${objectType} for that ID!`);
    const accountResponse =
      department === 'WX-Barrier'
        ? await SalesforceManager.getCTHEAEventInfo({
            objectName: 'account',
            sfId: response.Account__c,
          })
        : await SalesforceManager.getCTHEAEventInfoWithAccountId(response.AccountId);
    if (!accountResponse) throwError('Couldnt find an Account for that ID!');
    const { customerName, address } = accountResponse;
    const { street, city, postalCode } = address;
    setFormattedAddress(`${street}, ${city}, ${postalCode}`);
    setCustomerName(customerName);
  };

  // Global Variables
  const s3ParamsObj = {
    bucketKeys: `${state}/${department}/${uniqueId}/`,
    department,
  };
  const uploadedDocsKeys = Object.keys(uploadedDocs);

  // Functions
  // Gets all current documents in S3 for current id of doc repo.
  const fetchDocumentsFromS3 = async (docName = false) => {
    const { finalDocuments, alreadyUploadedDocs } = await getDocsFromS3(s3ParamsObj);
    // This checks the current documents that are uploaded on SF with the current required Docs and filters out the docs already uplaoded.
    const requiredDocuments = newReqDocs();
    const newReqDocArr = requiredDocuments.filter((doc) => !alreadyUploadedDocs.includes(doc));
    // * docName will be true, false, or a document.
    // * True will be passed on a multi upload and if the file bulk inclues a required file
    // * A document will also cause this is be true, on a delete file it will pass the document deleted.
    if (docName) {
      const sfField = sfMissingDocField(state, department);
      // * If you delete an required document which caused the required document array to become a length of 1 and
      // * The 1 required document is the document that was passed
      // * This means it is ready for scheduling currently but the user deleted a required document and now it is missing a required document.
      if (
        newReqDocArr.length === 1 &&
        newReqDocArr.includes(docName) &&
        requiredDocuments.includes(docName)
      ) {
        await EventsManager.updateNeedsDocs(false, sfField, sfObjectForResulting, uniqueId);
      }
      // * If we get down here it was an upload. If the upload caused the new required documents array length to become 0 then it is ready for scheduling.
      if (newReqDocArr.length === 0) {
        await EventsManager.updateNeedsDocs(true, sfField, sfObjectForResulting, uniqueId);
      }
    }
    setReqDocs(newReqDocArr);
    setDocs(finalDocuments);
  };

  // Required Document Upload
  // File name must be changed in the backend when setting S3 bucket key.
  // Need to check reqDocs to see if Sf WT_Docs_Complete__c field needs to be updated
  // If reqDocs length === 0 update this checkbox with true
  // * This function can only be invoked by a required document.
  const handleUploadRequiredFile = async (event) => {
    const { name, files } = event.target;
    const urlParamsObj = {
      state,
      department,
      uniqueId,
    };
    const data = new FormData();
    data.append('file', files[0]);
    const uploadFile = await uploadRequiredFile(data, urlParamsObj, name);
    if (!uploadFile) {
      return false;
    }
    return fetchDocumentsFromS3(name);
  };

  // Multi Upload
  // Also need to check reqDocs length. If it is === 0 also need to update sf WT_Docs_Complete__c === true.
  // In the backend, if a file uploaded already exists it will rename the existing file with deleted_timeStamp_fileName then upload the new file.
  const handleUpload = async (event) => {
    const { target } = event;
    const files = Array.from(event.target.files);
    const filesThatFailedToUpload = [];
    const requiredDocuments = newReqDocs();
    let ifCheckBool = false;
    // Loops through each file, checks if it is a required document, then upload.
    startLoading('Uploading Files...');
    for (let k = 0; k < files.length; k++) {
      const file = files[k];
      if (requiredDocuments.includes(file.name)) ifCheckBool = true;
      const urlParamsObj = {
        state,
        department,
        uniqueId,
      };
      const data = new FormData();
      data.append('file', file);
      // eslint-disable-next-line no-await-in-loop
      const uploadFile = await uploadFileToS3(data, urlParamsObj);
      if (!uploadFile) {
        filesThatFailedToUpload.push(file);
      }
    }
    stopLoading();
    if (filesThatFailedToUpload.length > 0) {
      throwError({
        message: 'Looks like files failed to upload. Please see below to see which files failed.',
        params: `${filesThatFailedToUpload.join(',')}`,
      });
    }
    target.value = null;
    // * CheckBool will be true or false here. It will be true if the multi file upload bulk includes a required document.
    return fetchDocumentsFromS3(ifCheckBool);
  };

  // Opens document in new tab
  const openFile = async (docName) => {
    const urlParamsObj = {
      state,
      department,
      uniqueId,
      docName,
    };
    const getUrl = await getSignedUrl(urlParamsObj);
    if (getUrl) window.open(getUrl, '_blank');
  };

  // Opens salesforce for the unique id for this doc repo
  const openSfLink = () => {
    const url = `${getSalesforceUrl()}${uniqueId}`;
    window.open(url, '_blank');
  };

  // Downloads file
  const downloadFile = async (docName) => {
    const params = {
      doc: docName,
      state,
      department,
      uniqueId,
    };
    return downloadFileFromS3(params);
  };

  // Deletes document
  // We don't actually delete documents. We rename them delete_timeStamp_documentName and put it back in the S3 bucket.
  // Need to check after a file is deleted, check reqDocs length and if its === 0 uncheck WT_Docs_Complete__c on SF
  const deleteFile = async (docName) => {
    const params = {
      doc: docName,
      state,
      department,
      uniqueId,
    };
    const success = await deleteFileFromS3(params);
    if (success) {
      // * The document deleted will be passed here
      await fetchDocumentsFromS3(docName);
    }
  };

  // Checks or unchecks the checkbox
  const checkBoxClick = (docName) => {
    const stateCopy = { ...uploadedDocs };
    stateCopy[docName].checked = !stateCopy[docName].checked;
    setDocs(stateCopy);
  };

  // Make all documents checkboxes the opposite of their current state.
  const handleCheckAllButton = () => {
    const stateCopy = { ...uploadedDocs };
    const allFilesInState = Object.keys(stateCopy);
    allFilesInState.forEach((docName) => {
      stateCopy[docName].checked = !stateCopy[docName].checked;
    });
    setDocs(stateCopy);
  };

  // Opens every checked document in a new tab.
  /* Google Chrome's pop up blocker will block opening multiple new tabs the first time. You will need to close the 1 tab that opened then
    back in Doc Repo in the url on the right side there will be a message saying multiple pop ups were blocked. Will need to allow then reclick view all
  */
  const handleViewAllButton = () => {
    const filesThatAreChecked = [];
    uploadedDocsKeys.forEach((docName) => {
      if (uploadedDocs[docName].checked) {
        filesThatAreChecked.push(docName);
      }
    });
    if (filesThatAreChecked.length > 0) {
      filesThatAreChecked.forEach((docName) => {
        openFile(docName);
      });
    }
  };

  // Downloads all documents checked.
  const handleDownloadAllButton = () => {
    const filesToDownload = [];
    uploadedDocsKeys.forEach((docName) => {
      if (uploadedDocs[docName].checked) {
        filesToDownload.push(docName);
      }
    });
    if (filesToDownload.length > 0) {
      filesToDownload.forEach((docName) => {
        downloadFile(docName);
      });
    }
  };

  // JSX for NOT uploaded required document display.
  // Only has uploadFile Button
  const reqDocRender = (requiredDoc) => {
    return (
      <StyledEditCellWrapper key={requiredDoc}>
        <StyledHeaderDisplay h3> {requiredDoc} </StyledHeaderDisplay>
        <StyledCellDivider />
        <StyledAttributeContainer>
          <StyledAttribute className="width">Required Document</StyledAttribute>
        </StyledAttributeContainer>
        <StyledAttributeContainer>
          <StyledAttributeLabel>Note</StyledAttributeLabel>
          <StyledAttribute>Must Upload To Result</StyledAttribute>
        </StyledAttributeContainer>
        <StyledUploadButton type="button">
          <FontAwesomeIcon icon={faUpload} />
          Upload File
          <StyledInputContainer
            type="file"
            name={requiredDoc}
            id="file"
            onChange={handleUploadRequiredFile}
          />
        </StyledUploadButton>
      </StyledEditCellWrapper>
    );
  };

  // JSX for ALL currently uploaded documents.
  const uploadedDocRender = (doc) => {
    const { checked: docIsChecked, docUploadedTimeStamp, author } = uploadedDocs[doc];
    return (
      <StyledEditCellWrapper className="uploaded" key={doc}>
        {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
        <StyledDivCheckbox
          aria-checked={docIsChecked}
          tabIndex={0}
          role="checkbox"
          onClick={() => checkBoxClick(doc)}
        >
          <StyledHeaderDisplay h3 className={docIsChecked ? 'doc-checked' : null}>
            {' '}
            {doc}{' '}
          </StyledHeaderDisplay>
        </StyledDivCheckbox>
        {author && docUploadedTimeStamp && (
          <StyledDivContainer>
            <StyledHeaderDisplay h5>
              Uploaded by {author} on <br />
              {docUploadedTimeStamp}.
            </StyledHeaderDisplay>
          </StyledDivContainer>
        )}
        <StyledCellDivider />
        <StyledButtonBody>
          <StyledAttributeContainer className="top">
            {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
            <StyledDivButton onClick={() => openFile(doc)} role="button" tabIndex={0}>
              <FontAwesomeIcon icon={faEye} />
              &nbsp; View File
            </StyledDivButton>
          </StyledAttributeContainer>
          <StyledCellDivider className="button-divider" />
          <StyledAttributeContainer className="bottom">
            {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
            <StyledDivButton onClick={() => downloadFile(doc)} tabIndex={0} role="button">
              <FontAwesomeIcon icon={faDownload} />
              &nbsp;Download
            </StyledDivButton>
          </StyledAttributeContainer>
        </StyledButtonBody>
        <StyleBottomButton type="button" onClick={() => deleteFile(doc)}>
          <FontAwesomeIcon icon={faTrashAlt} />
          &nbsp;Delete
        </StyleBottomButton>
      </StyledEditCellWrapper>
    );
  };

  return (
    <StyledDivContainer>
      <StyledDivContainer>
        <StyledDivContainer>
          <StyledDivContainer>
            <StyledResultingDisplay>
              {state !== 'CT' && (
                <>
                  <StyledHeaderStatus h3>
                    Result Status : {currentValue || 'Missing Resulting Status on Salesforce'}
                  </StyledHeaderStatus>
                  <StyledHeaderStatus h3>
                    Document Status :
                    {reqDoc.length === 0 ? 'Ready for scheduling' : 'Missing Documents'}
                  </StyledHeaderStatus>
                </>
              )}
              {state === 'CT' && (
                <>
                  <StyledHeaderStatus h3>
                    Customer Name : {customerName || 'Missing Name'}
                  </StyledHeaderStatus>
                  <StyledHeaderStatus h3>
                    Address : {formattedAddress || 'Missing Address'}
                  </StyledHeaderStatus>
                </>
              )}
            </StyledResultingDisplay>
            <StyledEditCellContainer>
              {reqDoc.length > 0 &&
                reqDoc.map((requiredDoc) => {
                  return reqDocRender(requiredDoc);
                })}
              {uploadedDocsKeys.length > 0 &&
                uploadedDocsKeys.map((doc) => {
                  return uploadedDocRender(doc);
                })}
            </StyledEditCellContainer>
          </StyledDivContainer>
        </StyledDivContainer>
      </StyledDivContainer>
      <StyledFooterWrapper>
        <DocRepoFooterForButtons
          handleUpload={handleUpload}
          handleCheckAllButton={handleCheckAllButton}
          handleViewAllButton={handleViewAllButton}
          handleDownloadAllButton={handleDownloadAllButton}
          updateRenderStatus={setRenderStatus}
          openSfLink={openSfLink}
          showResultingButton={showResultingButton}
        />
      </StyledFooterWrapper>
    </StyledDivContainer>
  );
};

DocumentTable.propTypes = {
  state: PropTypes.string.isRequired,
  department: PropTypes.string.isRequired,
  company: PropTypes.string.isRequired,
  uniqueId: PropTypes.string.isRequired,
  reqDocsAndQuestionsObj: PropTypes.shape({
    requiredDocuments: PropTypes.arrayOf(PropTypes.string.isRequired),
    questions: PropTypes.arrayOf(
      PropTypes.shape({
        blockerValues: PropTypes.arrayOf(PropTypes.string).isRequired,
        currentValue: PropTypes.string,
        key: PropTypes.number.isRequired,
        passThroughValue: PropTypes.arrayOf(PropTypes.string).isRequired,
        question: PropTypes.string.isRequired,
        sfTargetName: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        values: PropTypes.arrayOf(PropTypes.string).isRequired,
        'Price Change': PropTypes.shape({
          requiredDoc: PropTypes.arrayOf(PropTypes.string),
        }),
        'Scope Change': PropTypes.shape({
          requiredDoc: PropTypes.arrayOf(PropTypes.string),
        }),
        'First Payment': PropTypes.shape({
          requiredDoc: PropTypes.arrayOf(PropTypes.string),
        }),
        'Full Payment': PropTypes.shape({
          requiredDoc: PropTypes.arrayOf(PropTypes.string),
        }),
        'Not Collected': PropTypes.shape({
          requiredDoc: PropTypes.arrayOf(PropTypes.string),
        }),
      }),
    ),
    partnerDocuments: PropTypes.arrayOf(PropTypes.string.isRequired),
  }).isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  valuesOnSf: PropTypes.array.isRequired,
  setRenderStatus: PropTypes.func.isRequired,
  sfObjectForResulting: PropTypes.string.isRequired,
};

export default DocumentTable;
