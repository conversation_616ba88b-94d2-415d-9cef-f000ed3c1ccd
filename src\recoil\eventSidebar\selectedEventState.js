// TODO: this can be updated to use the getComplexObjectRecoilState util in @recoil/utils

import { atom, selector, DefaultValue } from 'recoil';
import utilityService from '@homeworksenergy/utility-service';

import allEventsAtom from '@recoil/event/allEventsAtom';

const startEndTimesState = atom({
  key: 'selectedEvent-startEndTimesAtom',
  default: selector({
    key: 'startEndTimesDefaultsSelector',
    get: ({ get }) => {
      const allEvents = get(allEventsAtom);
      const associatedEventIds = get(selectedEventFieldStates.associatedEventIds);
      const jobLength = get(selectedEventFieldStates.jobLength);
      const date = get(selectedEventFieldStates.date);
      const startTime = get(selectedEventFieldStates.startTime);
      const endTime = get(selectedEventFieldStates.endTime);

      let startEndTimes = null;

      // If no associated event ids, it is a new event and we just want startEndTimes
      if (!associatedEventIds?.length)
        startEndTimes = utilityService.getDefaultStartEndTimes(
          jobLength,
          date,
          undefined,
          parseInt(startTime, 10),
        );
      // Else, pull the startTime, endTime and date from the database event and create startEndTimes
      else
        startEndTimes = utilityService.getStartEndTimesForEvent(
          { associatedEventIds, date, startTime, endTime },
          allEvents,
        );

      return startEndTimes;
    },
  }),
});

const defaultValues = {
  id: null,
  date: '',
  type: null,
  startTime: '09:00:00',
  endTime: '17:00:00',
  arrivalWindow: null,
  associatedEventsId: null,
  customerName: '',
  lock: false,
  leadVendor: '',
  capApprovalLeadVendor: '',
  secondaryLeadVendor: '',
  amount: '',
  sfIds: { operationsId: '', dealId: '', accountId: '' },
  siteId: '',
  address: null,
  notes: {
    officeNotes: '',
    fieldNotes: '',
    equipmentOrderNotes: '',
    cancelNotes: '',
  },
  phoneNumber: '',
  secondaryPhoneNumber: '',
  timeFrameForProject: '',
  monthlyPaymentExpectations: '',
  primaryHeatingFuel: '',
  centralAc: '',
  email: '',
  status: '',
  confirmationStatus: '',
  jobStatus: '',
  wxVisitResult: '',
  program: '',
  eventName: '',
  needsDocs: false,
  migrated: false,
  jobLength: 1, // Days
  // Not on database table:
  eventDuration: null, // Hours, not to be used in combination with jobLength
  oids: [],
  oid: '',
  agentName: '',
  agentSfId: '',
  oldAgentOid: '',
  associatedEventIds: [],
  attributes: [],
  eventTypeName: '',
  estimatedJobLength: 0,
  formFieldErrors: {},
  regions: [],
  includeAgents: [],
  numUnit: 1,
  fuelType: '',
  scheduledBy: '',
  scheduledDate: null,
  scheduledOnline: false,
  lastModified: null,
  virtual: false,
  isInstant: false,
  rescheduleReason: '',
  returnReason: '',
  cancelReason: '',
  createMileageEvent: false,
  equipmentOrderStatus: null,
  leadVetted: null,
  workOrders: [],
  removedUnits: [], // Used to keep track of removed unit numbers. { unit2: { cancelOrReschedule: 'cancel', accountId: '', dealId: '' }, unit4: { cancelOrReschedule: 'reschedule', accountId: '', dealId: '' } }
  unitsSelected: [],
  pattern: null,
  patternName: '',
  customerInterests: [],
  townWarnings: [],
  leadSource: '',
  shadow: false,
  projectManager: '',
  concierge: '',
  finalContractAmountAtApproval: 0,
  revisedContractAmount: 0,
  isHEASchedulingHvacSales: false,
  mergeDocsTitle: '',
  squareFeet: '',
  houseBuilt: '',
  approvalSoftware: 'Hancock',
  company: '',
  gasProjectNumber: '',
  electricProjectNumber: '',
  incomeEligibleOrMarketRate: '',
  heaConfirmationStatus: '',
  sealingServiceResult: '',
  heaResult: '',
  heaCoPayWaived: '',
  initialBlowerDoorReading: 0,
  ductSealingOpp: 0,
  numberOfActiveBarriers: 0,
  hvacResultDetail: '',
  windowResultDetail: '',
  wxResultDetail: '',
  unitNumber: '',
  leadSourceDetail: '',
};

// Allow custom atoms/selectors to be applied directly
// All others will be set to atoms with their default value from above
const selectedEventFieldStates = { startEndTimes: startEndTimesState };

// Create a separate atom for each field on the selectedEvent.
// This way, changing the customerName doesn't need to rerender the siteId
// We can just update the customerName atom without effecting the siteId atom
Object.keys(defaultValues).forEach((fieldName) => {
  selectedEventFieldStates[fieldName] = atom({
    key: `selectedEvent-${fieldName}Atom`,
    default: defaultValues[fieldName],
  });
});

/**
 * Handles converting startEndTimes from date to moment.
 * If there is any other calculated values that we need we can do this here.
 */
const selectedEventSelector = selector({
  key: 'selectedEventSelector',
  get: ({ get }) => {
    const selectedEvent = {};
    const propertyNames = Object.keys(selectedEventFieldStates);

    // Get value of each atom, then return together in an object
    propertyNames.forEach((propertyName) => {
      selectedEvent[propertyName] = get(selectedEventFieldStates[propertyName]);
    });

    return selectedEvent;
  },
  set: ({ set, reset }, newValue) => {
    // Handle Resetting selected event
    if (newValue instanceof DefaultValue) {
      const propertyNames = Object.keys(selectedEventFieldStates);
      propertyNames.forEach((propertyName) => reset(selectedEventFieldStates[propertyName]));
      return;
    }

    // New values we are trying to update.
    // Don't need to pass the { ...selectedEvent, [updateField]: changedValue }
    // Since we only update the properties that are present in the newValue object
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      // We can only update atoms that we have created above. Each property needs a default value if it should be updated
      // Might also need one just to access the property.
      // Previously, when selecting an event on the calendar, it put the whole object from the backend into state.
      // This means that all properties on that object were automatically accounted for, and set to state even if they were undefined to start.
      // Now, we have no way of setting an unknown property, since there are individual atoms for each.
      // If necessary, we could potentially create a new atom and add it to the selectedEventFieldStates here
      // But I think that it might be better just to enforce that all properties are created to start with
      if (!selectedEventFieldStates[propertyName]) return;
      set(selectedEventFieldStates[propertyName], newValue[propertyName]);
    });
  },
});

export { selectedEventFieldStates };
export default selectedEventSelector;
