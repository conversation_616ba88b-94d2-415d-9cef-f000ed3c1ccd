import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useTheme } from 'styled-components';

import { getCityAndZipcodeFromAddress, renderStartAndEndTimes } from '@utils/functions';
import { decodeEventType } from '@homeworksenergy/utility-service';
import { isAuthorized } from '@utils/AuthUtils';

import Event from './Event';

const HVACSalesEvent = ({ event, ...otherProps }) => {
  const theme = useTheme();
  const { address, notes, needsDocs, lock, type, date, leadVetted } = event;
  const { business: department } = decodeEventType(type);
  const isSchedulerOrAbove = isAuthorized('Scheduler', department);
  let noSidebar = false;
  // If you are not a Scheduler or above, there are rules for viewing event details.
  if (!isSchedulerOrAbove) {
    const currentTime = moment().format('HH:mm');
    const canViewtime = moment('16:00', 'HH:mm').format('HH:mm');
    const oneDaysAhead = moment()
      .add(1, 'days')
      .format('YYYY-MM-DD');
    const formattedEventDate = moment(date).format('YYYY-MM-DD');
    // Events with a start date that is greater than tomorrow, event details can't be viewed.
    if (formattedEventDate > oneDaysAhead) noSidebar = true;
    // If the event is tomorrow but the current time is not 4PM or later, tomorrow's events can't be viewed.
    if (formattedEventDate === oneDaysAhead && canViewtime > currentTime) noSidebar = true;
  }

  const cityAndZipcode = getCityAndZipcodeFromAddress(address);

  const eventColorMap = {
    Vetted: theme.colors.eventN,
    Waiting: theme.colors.eventM,
    'Not Vetted': theme.colors.eventJ,
    'Move Up': theme.colors.eventGreen,
    '000100': theme.colors.eventA,
    '000102': theme.colors.eventG,
    'Direct to HVAC': theme.colors.orange,
  };

  const eventColor = eventColorMap[leadVetted || type] || theme.colors.eventG;

  return (
    <Event
      event={event}
      backgroundColor={eventColor}
      tooltip={notes.officeNotes}
      headerText={cityAndZipcode || 'No Address Found'}
      bodyText={[renderStartAndEndTimes(event)]}
      needsDocs={needsDocs}
      lockable
      pinnable
      lock={lock}
      noSidebar={noSidebar}
      {...otherProps}
    />
  );
};

HVACSalesEvent.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string.isRequired,
    sfIds: PropTypes.shape({
      contractId: PropTypes.string,
    }),
    date: PropTypes.string.isRequired,
    lock: PropTypes.bool.isRequired,
    customerName: PropTypes.string.isRequired,
    eventTypeName: PropTypes.string.isRequired,
    address: PropTypes.shape({
      city: PropTypes.string,
      postalCode: PropTypes.string,
    }),
    numUnit: PropTypes.number,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    type: PropTypes.string,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
      fieldNotes: PropTypes.string,
    }),
    needsDocs: PropTypes.bool,
    equipmentOrderStatus: PropTypes.string,
    leadVetted: PropTypes.string,
  }),
};

HVACSalesEvent.defaultProps = {
  event: {
    sfIds: {},
    address: {},
    numUnit: null,
  },
};

export default HVACSalesEvent;
