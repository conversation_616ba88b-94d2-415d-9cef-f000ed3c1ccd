import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import Form<PERSON>ieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import RecoilFieldOptions from './RecoilFieldOptions';

const FormSelectContent = styled.select`
  width: 100%;
  min-height: 32px;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  box-sizing: border-box;
  border-radius: 4px;
  pointer-events: ${({ readOnly }) => (readOnly ? 'none' : 'auto')};
`;

const FormSelect = (props) => {
  const {
    title,
    placeholder,
    readOnly,
    required,
    options,
    recoilOptions,
    name,
    type,
    onChange,
    testid,
    compact,
    customPaddingRight,
    customWidth,
    // TODO: the first 'empty' option was disabled,
    // I'm not sure why but i don't want to break any other forms
    // So i am adding an override
    disablePlaceholder,
    description,
  } = props;

  let { value } = props;
  // We can have different kind of data structure like [{key:'', value:''}] & ['Some text1'. 'Some text 2']
  // To deal with both kind of data structure this condition will check if value exist or not
  // if not then assign '' to value so React doesnt throw any warnings

  const valueExists = options?.find((option) => {
    const { value: optionValue } = option;
    if (type === 'object') return JSON.stringify(value) === JSON.stringify(optionValue);

    return value === option || optionValue === value;
  });
  if (value == null || !valueExists) value = '';

  if (recoilOptions) return <RecoilFieldOptions Component={FormSelect} {...props} />;

  const handleChange = (e) => {
    const changeEvent =
      // eslint-disable-next-line no-nested-ternary
      type === 'number'
        ? { target: { name: e.target.name, value: parseInt(e.target.value, 10) } }
        : type === 'object'
        ? { target: { name: e.target.name, value: JSON.parse(e.target.value) } }
        : e;
    return onChange(changeEvent);
  };

  return (
    <FormFieldContainer
      required={required}
      fieldName={name}
      compact={compact}
      customPaddingRight={customPaddingRight}
      customWidth={customWidth}
    >
      <FormFieldLabel description={description}>{title}</FormFieldLabel>
      <FormSelectContent
        default=""
        readOnly={readOnly || false}
        value={type === 'object' ? JSON.stringify(value) : value}
        onChange={handleChange}
        name={name}
        data-testid={testid}
      >
        <option value="" disabled={disablePlaceholder}>
          {placeholder}
        </option>
        {options.map((option) => {
          // Accept array of objects with [{key: 'WX Install Event', value: '000500'}] type notation
          // Or just array of strings
          const { key } = option;
          let { value } = option;

          if (type === 'object') value = JSON.stringify(value);

          return (
            <option key={JSON.stringify(option)} value={value || option}>
              {key || option}
            </option>
          );
        })}
      </FormSelectContent>
    </FormFieldContainer>
  );
};

FormSelect.propTypes = {
  title: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  name: PropTypes.string,
  type: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        key: PropTypes.string,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      }),
    ]),
  ),
  recoilOptions: PropTypes.shape({}),
  testid: PropTypes.string,
  compact: PropTypes.bool,
  customPaddingRight: PropTypes.bool,
  customWidth: PropTypes.bool,
  disablePlaceholder: PropTypes.bool,
  description: PropTypes.string,
};

FormSelect.defaultProps = {
  readOnly: false,
  placeholder: '',
  required: false,
  value: '',
  name: '',
  type: null,
  onChange: () => {},
  options: undefined,
  recoilOptions: null,
  testid: '',
  customPaddingRight: false,
  customWidth: false,
  compact: false,
  disablePlaceholder: true,
  description: '',
};

export default FormSelect;
