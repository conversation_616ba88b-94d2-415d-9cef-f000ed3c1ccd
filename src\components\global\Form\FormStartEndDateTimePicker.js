import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import FormDateTimePicker from './FormDateTimePicker';

const DatePickerContainer = styled.div`
  display: flex;
  flex-direction: ${({ direction }) => direction};
  padding-bottom: 10px;
  justify-content: space-between;
  flex: 1;

  // TODO: this pattern seems to work pretty well, might be useful for improving our row/column form components
  > * {
    flex: 1;
    margin-right: ${({ direction }) => (direction === 'row' ? '1.5em' : '0')};

    &:last-child {
      margin-right: 0;
    }
  }
`;

const FormStartEndDateTimePicker = ({
  day,
  startTime,
  endTime,
  onChange,
  displayDay,
  placeholder,
  dateFormat,
  timeIntervals,
  timeCaption,
  timeFormat,
  direction,
  allowDateSelect,
}) => {
  return (
    <DatePickerContainer direction={direction}>
      <FormDateTimePicker
        title={displayDay ? `Day ${day} start: ` : 'Start Time'}
        name={displayDay ? `day${day}Start` : 'startTime'}
        value={startTime}
        onChange={(time) => onChange(time, 'start')}
        maxTime={moment(endTime).subtract(15, 'minutes')}
        timeIntervals={timeIntervals}
        timeCaption={timeCaption}
        dateFormat={dateFormat}
        placeholderText={placeholder}
        timeFormat={timeFormat}
        // Only allow the first date to be changed, since we need to update the other dates if this one changes
        allowDateSelect={day === 1 && allowDateSelect}
      />
      <FormDateTimePicker
        title={displayDay ? `Day ${day} end: ` : 'End Time'}
        name={displayDay ? `day${day}End` : 'endTime'}
        value={endTime}
        onChange={(time) => onChange(time, 'end')}
        minTime={moment(startTime).add(15, 'minutes')}
        timeIntervals={timeIntervals}
        timeCaption={timeCaption}
        dateFormat={dateFormat}
        placeholderText={placeholder}
        timeFormat={timeFormat}
      />
    </DatePickerContainer>
  );
};

FormStartEndDateTimePicker.propTypes = {
  day: PropTypes.number,
  displayDay: PropTypes.bool,
  startTime: PropTypes.instanceOf(moment).isRequired,
  endTime: PropTypes.instanceOf(moment).isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  dateFormat: PropTypes.string,
  timeIntervals: PropTypes.number,
  timeCaption: PropTypes.string,
  timeFormat: PropTypes.string,
  direction: PropTypes.oneOf(['row', 'column']),
  allowDateSelect: PropTypes.bool,
};

FormStartEndDateTimePicker.defaultProps = {
  day: 1,
  placeholder: '',
  dateFormat: 'MMMM d, yyyy h:mm aa',
  timeIntervals: 15,
  timeCaption: 'Time',
  timeFormat: 'h:mm aa',
  displayDay: true,
  direction: 'column',
  allowDateSelect: true,
};

export default FormStartEndDateTimePicker;
