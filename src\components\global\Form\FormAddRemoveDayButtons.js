import React from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { isSlotsSearchAtom, selectedEventState } from '@recoil/eventSidebar';
import { verifySingleWeek } from '@utils/functions';

const AddRemoveButtonsContainer = styled.div`
  display: flex;
  justify-content: space-around;
  width: 90px;
`;

const TitleText = styled.span`
  padding-left: 2%;
  padding-right: 2%;
  align-self: center;
`;

const AddRemoveButtons = styled.button`
  background-color: white;
  color: ${({ theme }) => theme.colors.green};
  border: solid 2px ${({ theme }) => theme.colors.green};
  height: 120%;
  align-self: center;
`;

const AddRemoveDayButtons = ({ children, name, value, amount, onChange, testid }) => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const isSlotsSearch = useRecoilValue(isSlotsSearchAtom);
  const { formFieldErrors, date } = selectedEvent;
  const floatValue = parseFloat(value);
  const handleChange = (direction) => {
    // If + use amount, if - use -amount
    const changeAmount = direction === '+' ? amount : amount * -1;
    const newValue = floatValue + changeAmount;
    // Minimum job length must be > 0 days, max 5 days
    if (newValue <= 0 || newValue > 5) return false;
    // Don't need to verifySingleWeek for slots search.
    if (!isSlotsSearch && !verifySingleWeek(date, newValue))
      return setSelectedEvent({
        ...selectedEvent,
        formFieldErrors: {
          ...formFieldErrors,
          jobLength: 'Events must start and end on the same week',
        },
      });

    // Mimick an HTML form element with e.target.value/name
    return onChange({ target: { name, value: newValue } });
  };

  return (
    <AddRemoveButtonsContainer>
      <AddRemoveButtons
        type="button"
        onClick={() => handleChange('+')}
        data-testid={`add-${testid}-time-button`}
      >
        +
      </AddRemoveButtons>
      <TitleText>{children}</TitleText>
      <AddRemoveButtons
        type="button"
        onClick={() => handleChange('-')}
        data-testid={`remove-${testid}-time-button`}
      >
        -
      </AddRemoveButtons>
    </AddRemoveButtonsContainer>
  );
};
AddRemoveDayButtons.propTypes = {
  children: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  amount: PropTypes.number.isRequired,
  testid: PropTypes.string,
};

AddRemoveDayButtons.defaultProps = {
  testid: '',
};

export default AddRemoveDayButtons;
