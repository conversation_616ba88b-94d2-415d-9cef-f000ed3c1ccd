import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAISMIncentive = ({ record }) => {
  const { dealId, ismIncentives, date, subject } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="ismIncentives"
            value={ismIncentives}
            title="ISM Incentive"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput readOnly name="date" value={date} title="Date" placeholder="" />
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
        </Col>
      </Row>
    </>
  );
};

HEAISMIncentive.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    ismIncentives: PropTypes.string,
    date: PropTypes.string,
    subject: PropTypes.string,
  }),
};

HEAISMIncentive.defaultProps = {
  record: {},
};

export default HEAISMIncentive;
