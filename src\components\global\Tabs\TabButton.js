import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const Button = styled.div`
  font-size: 14px;
  font-weight: ${({ active }) => (active ? 600 : 400)};
  color: ${({ theme }) => theme.primary[500]};
  border-bottom: ${({ active }) => {
    return active ? '3px solid orange' : '2px solid transparent';
  }};
  border-top: 2px solid transparent;
  cursor: pointer;
`;

const Text = styled.div`
  padding: 8px 0;
`;

const TabButton = ({ title, active, onClick }) => {
  return (
    <Button onClick={onClick} active={active}>
      <Text>{title}</Text>
    </Button>
  );
};

TabButton.propTypes = {
  title: PropTypes.string.isRequired,
  active: PropTypes.bool.isRequired,
  onClick: PropTypes.func,
};

TabButton.defaultProps = {
  onClick: () => {},
};

export default TabButton;
