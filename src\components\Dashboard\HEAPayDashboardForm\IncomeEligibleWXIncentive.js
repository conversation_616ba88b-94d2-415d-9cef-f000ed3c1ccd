import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const IncomeEligibleWXIncentive = ({ record }) => {
  const { dealId, finalContractAmount, ieWxIncentive, date, subject } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="ieWxIncentive"
            value={ieWxIncentive}
            title="IE WX Incentive"
            placeholder=""
          />
          <FormInput readOnly name="date" value={date} title="Date" placeholder="" />
        </Col>
      </Row>
    </>
  );
};

IncomeEligibleWXIncentive.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    finalContractAmount: PropTypes.string,
    ieWxIncentive: PropTypes.string,
    date: PropTypes.string,
    subject: PropTypes.string,
  }),
};

IncomeEligibleWXIncentive.defaultProps = {
  record: {},
};

export default IncomeEligibleWXIncentive;
