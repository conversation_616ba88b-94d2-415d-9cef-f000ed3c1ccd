import React, { useState } from 'react';
import styled, { useTheme } from 'styled-components';
import {
  useRecoilState,
  useRecoilValue,
  useResetRecoilState,
  // eslint-disable-next-line camelcase
  useRecoilBridgeAcrossReactRoots_UNSTABLE,
  useRecoilCallback,
} from 'recoil';
import PropTypes from 'prop-types';
import swal from 'sweetalert2/dist/sweetalert2';

import { Edit } from '@styled-icons/boxicons-solid/Edit';

import { Row, Col } from '@components/global/Form';
import { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import { mergeDocsConfirmation } from '@components/confirmations/MergeDocsConfirmation';

import { capHeaResultingQuestionsState, documentsSelector } from '@recoil/docRepo';
import { selectedEventState } from '@recoil/eventSidebar';
import { throwError, startLoading, stopLoading } from '@utils/EventEmitter';
import { capRestrictedDocs } from '@utils/businessLogic/heaBusinessLogic';
import { UtilityManager, DocRepoManager } from '@utils/APIManager';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import { openFile, downloadFile, handleUpload, fetchDocumentsFromS3 } from '../DocRepoButtons';
import DocRepoBody from '../DocRepoBody';

const EditResulting = styled(Edit)`
  color: ${({ theme }) => theme.primary[400]};
  height: 25px;
  margin: -5px 10px 0px 0px;
`;

const HeaderInfoTextStyle = styled(HeaderLabel)`
  cursor: pointer;
  font-size: x-large;
`;

const DisplayResultingContainer = styled.div`
  margin: 20px;
  margin-bottom: 10px;
  margin-top: 10px;
`;

const TextContainer = styled.div``;

const TextStyle = styled(Row)`
  justify-content: center;
  font-size: 15px;
  margin-bottom: 5px;
`;

const Divider = styled.hr``;

const DocList = styled.div`
  justify-content: center;
  margin-bottom: 5px;
`;

const DocNameStyle = styled.div`
  color: red;
  padding-bottom: 5px;
`;

const DocRepo = ({ department, uniqueId, setShowResulting }) => {
  const theme = useTheme();
  const RecoilBridge = useRecoilBridgeAcrossReactRoots_UNSTABLE();
  const event = useRecoilValue(selectedEventState);
  const [documents, setDocuments] = useRecoilState(documentsSelector);
  const [resulting, setResulting] = useRecoilState(capHeaResultingQuestionsState);
  const resetResulting = useResetRecoilState(capHeaResultingQuestionsState);
  const [docsSelected, setDocsSelected] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [allDocsSelected, setAllDocsSelected] = useState(false);

  const s3Params = { state: 'MA', department, uniqueId };

  const displayRestrictedDocsList = department === 'HEA-CAP' && docsSelected;

  const handleUploadAllButton = (event) => {
    handleUpload(event, s3Params, setDocuments, resulting, setResulting, {
      documents,
      checkDuplicate: true,
    });
  };

  const handleDownloadAllButton = () => {
    Object.keys(documents).forEach((document) => {
      if (documents[document].checked)
        downloadFile({
          docName: documents[document].fileName,
          ...s3Params,
        });
    });
  };

  const handleViewAllButton = () => {
    if (allDocsSelected)
      Object.keys(documents).forEach((document) => {
        if (documents[document].checked) viewDocument(documents[document]);
      });
    else selectedFiles.forEach((doc) => viewDocument(documents[doc.split('.')[0]]));
  };

  const viewDocument = ({ fileName }) => {
    openFile({
      ...s3Params,
      docName: fileName,
    });
  };

  const handleEmailDocsButton = async () => {
    const { email, customerName } = event;
    const swalResponse = await swal.fire({
      title: 'Are you sure?',
      html: parseJSXContentForSwalPopup(
        <>
          <TextContainer>
            <TextStyle>This will send an email to {email}</TextStyle>
            <TextStyle>Is that correct?</TextStyle>
          </TextContainer>
          {displayRestrictedDocsList && (
            <>
              <Divider />
              <TextContainer>
                <TextStyle>
                  The Following docs will not be sent because they are Restricted:
                </TextStyle>
                <DocList>
                  {capRestrictedDocs.map((doc) => {
                    return <DocNameStyle>{doc}</DocNameStyle>;
                  })}
                </DocList>
              </TextContainer>
            </>
          )}
        </>,
      ),
      icon: 'warning',
      showCancelButton: true,
      closeOnConfirm: false,
      animation: 'slide-from-top',
    });
    const { isConfirmed } = swalResponse;
    if (isConfirmed) {
      startLoading('Emailing Documents to Customer.');
      const selectedDocs = [];
      Object.keys(documents).forEach((document) => {
        if (documents[document].checked) selectedDocs.push(documents[document].fileName);
      });

      try {
        const response = await UtilityManager.emailDocsToCustomer({
          dealId: uniqueId,
          email,
          name: customerName,
          selectedDocs,
          department,
        });
        const { error } = response;
        if (error) throw error;
        stopLoading();
      } catch (error) {
        stopLoading();
        throwError('Error Emailing Documents to Customer.');
      }
    }
  };

  const handleMergeSelectedDocssButton = async () => {
    const { value: confirmed } = await mergeDocsConfirmation(RecoilBridge, getMergeDocsName, theme);
    if (!confirmed) return false;
    const mergeDocsTitle = await getMergeDocsName();
    const pdfFiles = [...selectedFiles].filter((fileName) => {
      return fileName.includes('.pdf');
    });
    await DocRepoManager.mergeDocuments({
      ...s3Params,
      documents: pdfFiles,
      mergeDocsTitle,
    });
    return fetchDocumentsFromS3(s3Params, setDocuments, resulting, setResulting, resetResulting);
  };

  const getMergeDocsName = useRecoilCallback(
    ({ snapshot }) => async () => {
      const event = await snapshot.getPromise(selectedEventState);
      const { mergeDocsTitle } = event;
      if (mergeDocsTitle?.length === 0) return throwError('Please enter file name.');
      return mergeDocsTitle;
    },
    [],
  );

  const handleDocsSelection = (e) => {
    const { name, value } = e.target;
    const formattedDocuments = {};
    let selectedFilesCopy = [...selectedFiles];
    Object.keys(documents).map((document) => {
      const doc = { ...documents[document] };
      const { uploaded, fileName } = doc;
      if (fileName === name) {
        doc.checked = value;
        if (uploaded) {
          if (value) selectedFilesCopy.push(fileName);
          else
            selectedFilesCopy = selectedFilesCopy.filter((fileName) => {
              return fileName !== name;
            });
        }
      }
      formattedDocuments[document] = doc;
      return doc;
    });
    setDocsSelected(selectedFilesCopy.length > 0);
    setSelectedFiles(selectedFilesCopy);
    setDocuments(formattedDocuments);
  };

  const handleSelectAll = (e) => {
    const { value } = e.target;
    const formattedDocuments = {};
    Object.keys(documents).forEach((document) => {
      const doc = { ...documents[document] };
      if (doc.uploaded) doc.checked = value;
      formattedDocuments[document] = doc;
    });
    setDocsSelected(value);
    setAllDocsSelected(value);
    setDocuments(formattedDocuments);
  };

  return (
    <>
      {department !== 'HVAC_Sales-CAP' && (
        <DisplayResultingContainer>
          <Row>
            <Col>
              <HeaderInfoTextStyle>
                Resulting : {resulting.visitResult}
                <EditResulting onClick={() => setShowResulting(true)} />
              </HeaderInfoTextStyle>
            </Col>
          </Row>
        </DisplayResultingContainer>
      )}
      <DocRepoBody
        documents={documents}
        s3Params={s3Params}
        handleDocsSelection={handleDocsSelection}
        viewDocument={viewDocument}
        handleUploadAllButton={handleUploadAllButton}
        handleDownloadAllButton={handleDownloadAllButton}
        handleViewAllButton={handleViewAllButton}
        handleEmailDocsButton={handleEmailDocsButton}
        handleMergeSelectedDocssButton={handleMergeSelectedDocssButton}
        setDocuments={setDocuments}
        docsSelected={docsSelected}
        resulting={resulting}
        setResulting={setResulting}
        canMergePdfFiles={selectedFiles.length > 1}
        allDocsSelected={allDocsSelected}
        handleSelectAll={handleSelectAll}
      />
    </>
  );
};

DocRepo.propTypes = {
  department: PropTypes.string,
  uniqueId: PropTypes.string,
  setShowResulting: PropTypes.func,
};

DocRepo.defaultProps = {
  department: '',
  uniqueId: '',
  setShowResulting: () => {},
};

export default DocRepo;
