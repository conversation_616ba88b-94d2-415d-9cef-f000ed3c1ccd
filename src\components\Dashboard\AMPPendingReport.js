import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const AMPPendingReport = ({ record }) => {
  const { dealId, amp__Amount: ampAmount, heaInvoicingProcess, heaQcFailNotes, isCap } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="ampAmount"
            value={ampAmount}
            title="AMP Amount"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="heaInvoicingProcess"
            value={heaInvoicingProcess}
            title="HEA Invoicinf Process"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="heaQcFailNotes"
        value={heaQcFailNotes}
        title="HEA QC Fail Notes"
        placeholder=""
      />
    </>
  );
};

AMPPendingReport.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    amp__Amount: PropTypes.string,
    heaQcFailNotes: PropTypes.string,
    heaInvoicingProcess: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

AMPPendingReport.defaultProps = {
  record: {},
};

export default AMPPendingReport;
