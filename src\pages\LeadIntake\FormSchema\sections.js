import { addKeyNameToObject } from '@components/DataIntakeForm/dataIntakeFormHelpers';
import { formSchema } from './formSchema';

const leadIntakeFields = addKeyNameToObject(formSchema);
// Section fields are imported in leadIntakeMap object
// Section field means its a group of fields and there output will be in array string format
// Suppose if we expose unitInfo to leadIntake map.
// each field of unitInfo will have list values
// unitNumber = ['911', '132' , '553']
// means 1st unit number is 911 and 2nd is 132, 3rd is 553
// so same for remaining fields, Everything is index based in Section Fields
const sectionFields = {
  unitInfo: {
    type: 'section',
    fields: [
      leadIntakeFields.unitNumber,
      leadIntakeFields.occupantType,
      leadIntakeFields.heatingFuel,
      leadIntakeFields.gasProvider,
      leadIntakeFields.hasAlternateGasBillName,
      leadIntakeFields.gasBillName,
      leadIntakeFields.gasAccountNumber,
      leadIntakeFields.electricProvider,
      leadIntakeFields.hasAlternateElectricBillName,
      leadIntakeFields.electricBillName,
      leadIntakeFields.electricAccountNumber,
    ],
    perUnit: true,
    conditional: (values) => values.singleOrMultiFamily === 'Single Family',
  },
  unitInfoCT: {
    type: 'section',
    fields: [
      leadIntakeFields.unitNumber,
      leadIntakeFields.preferredLanguageCT,
      leadIntakeFields.occupantType,
      leadIntakeFields.squareFeet,
      leadIntakeFields.customerIEPreApproved,
      leadIntakeFields.financialAssistanceFromState,
      leadIntakeFields.receivedFinancialAssistanceFromUtility,
      leadIntakeFields.houseHoldSize,
      leadIntakeFields.houseHoldMaxAnnualIncome,
      leadIntakeFields.incomeEligibleOrMarketRate,
      leadIntakeFields.openConstructionRemodeling,
      leadIntakeFields.anyMoldAsbestosORVermiculitePresent,
      leadIntakeFields.heatingFuelCT,
      leadIntakeFields.gasProviderCT,
      leadIntakeFields.hasAlternateGasBillName,
      leadIntakeFields.gasBillName,
      leadIntakeFields.gasAccountNumber,
      leadIntakeFields.electricProviderCT,
      leadIntakeFields.hasAlternateElectricBillName,
      leadIntakeFields.electricBillName,
      leadIntakeFields.electricAccountNumber,
    ],
    perUnit: true,
    conditional: (values) => values.singleOrMultiFamily === 'Single Family',
  },
  multiUnitInfo: {
    type: 'section',
    fields: [
      leadIntakeFields.unitNumber,
      leadIntakeFields.occupantType,
      leadIntakeFields.heatingFuel,
      leadIntakeFields.gasProvider,
      leadIntakeFields.hasAlternateGasBillName,
      leadIntakeFields.gasBillName,
      leadIntakeFields.gasAccountNumber,
      leadIntakeFields.electricProvider,
      leadIntakeFields.hasAlternateElectricBillName,
      leadIntakeFields.electricBillName,
      leadIntakeFields.electricAccountNumber,
    ],
    perUnit: true,
    collapse: true,
    conditional: (values) => values.singleOrMultiFamily === 'Multi Family',
  },
  multiUnitInfoCT: {
    type: 'section',
    fields: [
      leadIntakeFields.unitNumber,
      leadIntakeFields.preferredLanguageCT,
      leadIntakeFields.occupantType,
      leadIntakeFields.squareFeet,
      leadIntakeFields.customerIEPreApproved,
      leadIntakeFields.financialAssistanceFromState,
      leadIntakeFields.receivedFinancialAssistanceFromUtility,
      leadIntakeFields.houseHoldSize,
      leadIntakeFields.houseHoldMaxAnnualIncome,
      leadIntakeFields.incomeEligibleOrMarketRate,
      leadIntakeFields.openConstructionRemodeling,
      leadIntakeFields.anyMoldAsbestosORVermiculitePresent,
      leadIntakeFields.heatingFuelCT,
      leadIntakeFields.gasProviderCT,
      leadIntakeFields.hasAlternateGasBillName,
      leadIntakeFields.gasBillName,
      leadIntakeFields.gasAccountNumber,
      leadIntakeFields.electricProviderCT,
      leadIntakeFields.hasAlternateElectricBillName,
      leadIntakeFields.electricBillName,
      leadIntakeFields.electricAccountNumber,
    ],
    perUnit: true,
    collapse: true,
    defaultCollapsed: false,
    conditional: (values) => values.singleOrMultiFamily === 'Multi Family',
  },
  facilitatorInfo: {
    type: 'section',
    text: 'Facilitator Info',
    fields: [
      leadIntakeFields.facilitatorNotice,
      leadIntakeFields.facilitatorFirstName,
      leadIntakeFields.facilitatorLastName,
      leadIntakeFields.facilitatorPhoneNumber,
      leadIntakeFields.facilitatorEmail,
      leadIntakeFields.facilitatorRole,
    ],
    conditional: (values) => values.singleOrMultiFamily === 'Multi Family',
  },
  customerInfoReview: {
    type: 'section',
    text: 'Customer Info',
    fields: [
      leadIntakeFields.customerFirstName,
      leadIntakeFields.customerLastName,
      leadIntakeFields.customerEmail,
      leadIntakeFields.customerPrimaryPhoneNumber,
      leadIntakeFields.customerAddress,
    ],
    perUnit: false,
  },
  utilityInfoReview: {
    type: 'section',
    text: 'Utility Account',
    fields: [
      leadIntakeFields.heatingFuel,
      leadIntakeFields.electricProvider,
      leadIntakeFields.gasProvider,
    ],
    perUnit: true,
  },
  siteId: {
    type: 'section',
    text: 'Site ID',
    fields: [leadIntakeFields.siteId],
    perUnit: true,
  },
  homeHeatingSystem: {
    type: 'section',
    text: 'Home Heating System',
    fields: [
      leadIntakeFields.typeOfSystem,
      leadIntakeFields.systemLocated,
      leadIntakeFields.basementDescription,
      leadIntakeFields.atticDescription,
      leadIntakeFields.crawlspaceDescription,

      leadIntakeFields.optionsInBasement,
      leadIntakeFields.noOfBathrooms,
      leadIntakeFields.typeOfHeatingEquipment,
      leadIntakeFields.ifRadiatorSteamOrHotWater,
      leadIntakeFields.heatingSystemAge,

      leadIntakeFields.howOldIsYourHotWaterSystem,
      leadIntakeFields.howManyZonesThermostatsDoYouHave,
      leadIntakeFields.doYouHaveAc,
      leadIntakeFields.ifYesWhatKindOfACDoYouHave,
    ],
    perUnit: true,
  },
  scheduleEventQuestionsCT: {
    type: 'section',
    text: '',
    fields: [leadIntakeFields.interestedInWaitlist, leadIntakeFields.waitlistDays],
  },
};

export { sectionFields };
