import React, { useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSetRecoilState, useRecoilState } from 'recoil';
import moment from 'moment';
import styled from 'styled-components';

import { useHasChanged } from '@hooks';
import { Checkbox } from '@components/global';
import {
  FormInput,
  FormRadioButtons,
  FormStartEndDateTimePicker,
  FormCheckboxes,
  FormStartEndDatePicker,
  handleFormFieldChange,
} from '@components/global/Form';
import { agentAvailabilityInfoState } from '@recoil/admin/agents';
import { adminInfoHasChangedState } from '@recoil/admin';

const TimePickerContainer = styled.div`
  width: 30%;
`;

const EditAvailabilityFields = (props) => {
  const { oid, departmentName } = props;
  const [agentAvailabilityInfo, setAgentAvailabilityInfo] = useRecoilState(
    agentAvailabilityInfoState,
  );
  const setAgentInfoHasChanged = useSetRecoilState(adminInfoHasChangedState);
  const oidChanged = useHasChanged(oid);

  const isInsulation = departmentName === 'Insulation';
  const isHea = departmentName === 'HEA';
  const {
    days,
    overrideHoliday,
    startDate,
    endDate,
    startTime,
    endTime,
    action,
    maxAppt,
  } = agentAvailabilityInfo;

  useEffect(() => {
    // Need to check if oid changed to avoid infinite loop when setting agentAvailabilityInfo
    if (oid && oidChanged) {
      // HEA default to 8
      const startHour = isHea ? 8 : 9;
      // Set oid to selected agent
      setAgentAvailabilityInfo({
        ...agentAvailabilityInfo,
        oids: [oid],
        startTime: moment()
          .set('hour', startHour)
          .set('minute', 0),
      });
    }
  }, [oid, oidChanged, isHea, agentAvailabilityInfo, setAgentAvailabilityInfo]);

  const handleFieldChange = useCallback(
    (e, updatedSchedule = agentAvailabilityInfo) => {
      handleFormFieldChange(e, updatedSchedule, setAgentAvailabilityInfo);
      setAgentInfoHasChanged(true);
    },
    [agentAvailabilityInfo, setAgentAvailabilityInfo, setAgentInfoHasChanged],
  );

  const handleDateChange = (newDate, startOrEndDate) => {
    const event = { target: { name: startOrEndDate, value: moment(newDate).startOf('day') } };
    handleFieldChange(event);
  };

  const handleTimeChange = (time, startOrEnd) => {
    const newTime = { target: { name: `${startOrEnd}Time`, value: moment(time) } };
    handleFieldChange(newTime);
  };

  const showOpenCrewOnlyFields = action === 'open';

  return (
    <>
      <FormRadioButtons
        name="action"
        required
        title="Open / Close / Add Block"
        options={[
          { key: 'Open Crew', value: 'open' },
          { key: 'Close Crew', value: 'close' },
        ]}
        value={action}
        onChange={handleFieldChange}
      />
      <FormCheckboxes
        title="Select Day of the week to open/close"
        required
        name="days"
        options={[
          { key: 'Sunday', value: 7 },
          { key: 'Monday', value: 1 },
          { key: 'Tuesday', value: 2 },
          { key: 'Wednesday', value: 3 },
          { key: 'Thursday', value: 4 },
          { key: 'Friday', value: 5 },
          { key: 'Saturday', value: 6 },
        ]}
        onChange={handleFieldChange}
        value={days}
      />
      {showOpenCrewOnlyFields && (
        <Checkbox
          name="overrideHoliday"
          label="Override Holidays"
          value={overrideHoliday}
          onChange={handleFieldChange}
        />
      )}
      {showOpenCrewOnlyFields && !isInsulation && (
        <FormInput
          name="maxAppt"
          title="Max Appointments"
          value={maxAppt}
          type="number"
          max={5}
          min={0}
          onChange={handleFieldChange}
        />
      )}
      <TimePickerContainer>
        <FormStartEndDatePicker
          required
          startDate={moment(startDate)}
          endDate={moment(endDate)}
          onChange={handleDateChange}
        />

        {showOpenCrewOnlyFields && (
          <FormStartEndDateTimePicker
            key="startEndTime"
            startTime={startTime}
            endTime={endTime}
            displayDay={false}
            dateFormat="h:mm aa"
            allowDateSelect={false}
            onChange={handleTimeChange}
          />
        )}
      </TimePickerContainer>
    </>
  );
};

EditAvailabilityFields.propTypes = {
  oid: PropTypes.string,
  departmentName: PropTypes.string,
};

EditAvailabilityFields.defaultProps = {
  oid: null,
  departmentName: null,
};

export default EditAvailabilityFields;
