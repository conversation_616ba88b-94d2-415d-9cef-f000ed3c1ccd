import React from 'react';
import { useRecoilState } from 'recoil';
import styled, { ThemeProvider } from 'styled-components';
import PropTypes from 'prop-types';

import { selectedEventState } from '@recoil/eventSidebar';

import { handleFormFieldChange, FormSelect, FormTextBox } from '@components/global/Form';
import { Checkbox } from '@components/global';
import { cancelReasons as heaCancelReasons } from '@utils/businessLogic/heaBusinessLogic';
import {
  cancelReasons as hvacCancelReasons,
  siteEvalCancelReasons,
} from '@utils/businessLogic/hvacBusinessLogic';
import { cancelReasonsCT } from '@utils/businessLogic/ctBusinessLogic';

const StyledConfirmationBody = styled.div``;

const CancelEventConfirmation = ({ showMileageEventOption, showCancelNotesOption, theme }) => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);

  const { cancelReason, notes = {}, createMileageEvent, type } = selectedEvent;
  const departmentType = type?.substring(0, 4);
  const cancelReasons =
    {
      '0001': hvacCancelReasons,
      '0004': siteEvalCancelReasons,
      '0100': cancelReasonsCT,
    }[departmentType] || heaCancelReasons;

  const handleFieldChange = (e, updatedEvent = selectedEvent) => {
    return handleFormFieldChange(e, updatedEvent, setSelectedEvent);
  };

  return (
    <ThemeProvider theme={theme}>
      <StyledConfirmationBody>
        <FormSelect
          required
          name="cancelReason"
          value={cancelReason}
          title="Reason for Cancel"
          options={cancelReasons}
          onChange={handleFieldChange}
        />
        {showCancelNotesOption && (
          <FormTextBox
            required
            name="notes.cancelNotes"
            value={notes?.cancelNotes}
            title="Cancel Notes"
            placeholder=""
            onChange={handleFieldChange}
          />
        )}

        {showMileageEventOption && (
          <Checkbox
            name="createMileageEvent"
            value={createMileageEvent}
            label="Create mileage event?" // Create mileage placeholder? Preserve mileage? Pay mileage for this visit?
            onChange={handleFieldChange}
          />
        )}
      </StyledConfirmationBody>
    </ThemeProvider>
  );
};

const fireCancelConfirmation = async (
  RecoilBridge,
  showMileageEventOption,
  showCancelNotesOption,
  theme,
) => {
  // Need to lazy load swal config so that the .app-content node exists in dom
  const { createSwalWithTheme } = await import('@config/swalConfig');
  const swal = createSwalWithTheme(theme);

  return swal.fire({
    titleText: 'Are you sure you want to cancel this event?',
    html: (
      <RecoilBridge>
        <CancelEventConfirmation
          showMileageEventOption={showMileageEventOption}
          showCancelNotesOption={showCancelNotesOption}
          theme={theme}
        />
      </RecoilBridge>
    ),
    icon: 'warning',
    confirmButtonText: 'Yes',
    showCancelButton: true,
    cancelButtonText: 'No',
  });
};

export { fireCancelConfirmation };

CancelEventConfirmation.propTypes = {
  showMileageEventOption: PropTypes.bool,
  showCancelNotesOption: PropTypes.bool,
  // eslint-disable-next-line react/forbid-prop-types
  theme: PropTypes.object.isRequired,
};

CancelEventConfirmation.defaultProps = {
  showMileageEventOption: true,
  showCancelNotesOption: true,
};

export default CancelEventConfirmation;
