import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput } from '@components/global/Form';

import InteractiveButtons from '../InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const GoodSpecBonusForm = ({ record }) => {
  const {
    dealId,
    finalContractAmount,
    goodSpecAreas,
    badSpecAreas,
    miscellaneousVentilationSpec,
    anyPreWxs,
    atticSpec,
    crawlSpaceSpec,
    kwSpec,
    wallSpec,
    goodSpec,
    installed,
    goodSpecBonus,
  } = record;
  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="goodSpecAreas"
            value={goodSpecAreas}
            title="Good Spec Areas"
            placeholder=""
          />
          <FormInput
            readOnly
            name="miscellaneousVentilationSpec"
            value={miscellaneousVentilationSpec}
            title="Miscellaneous/Ventilation Spec"
            placeholder=""
          />
          <FormInput
            readOnly
            name="atticSpec"
            value={atticSpec}
            title="Attic Spec"
            placeholder=""
          />
          <FormInput readOnly name="kwSpec" value={kwSpec} title="KW Spec" placeholder="" />
          <FormInput readOnly name="goodSpec" value={goodSpec} title="Good Spec" placeholder="" />
          <FormInput
            readOnly
            name="goodSpecBonus"
            value={goodSpecBonus}
            title="Good Spec Bonus"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="badSpecAreas"
            value={badSpecAreas}
            title="Bad Spec Areas"
            placeholder=""
          />
          <FormInput
            readOnly
            name="anyPreWxs"
            value={anyPreWxs}
            title="Any Pre WXs"
            placeholder=""
          />
          <FormInput
            readOnly
            name="crawlSpaceSpec"
            value={crawlSpaceSpec}
            title="Crawl Space Spec"
            placeholder=""
          />
          <FormInput readOnly name="wallSpec" value={wallSpec} title="Wall Spec" placeholder="" />
          <FormInput readOnly name="installed" value={installed} title="Installed" placeholder="" />
        </Col>
      </Row>
    </>
  );
};

GoodSpecBonusForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    finalContractAmount: PropTypes.string,
    goodSpecAreas: PropTypes.string,
    badSpecAreas: PropTypes.string,
    miscellaneousVentilationSpec: PropTypes.string,
    anyPreWxs: PropTypes.string,
    atticSpec: PropTypes.string,
    crawlSpaceSpec: PropTypes.string,
    kwSpec: PropTypes.string,
    wallSpec: PropTypes.string,
    goodSpec: PropTypes.string,
    installed: PropTypes.string,
    goodSpecBonus: PropTypes.string,
  }),
};

GoodSpecBonusForm.defaultProps = {
  record: {},
};

export default GoodSpecBonusForm;
