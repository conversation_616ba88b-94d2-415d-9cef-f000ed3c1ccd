import React, { useState } from 'react';
import { useRecoilValue, useResetRecoilState, useSetRecoilState } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { useDrag } from 'react-dnd';
import { decodeEventType } from '@homeworksenergy/utility-service';

import Clickable from '@components/global/Clickable';
import ToolTip from '@components/global/Tooltip/Tooltip';
import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import Confirmation from '@components/Calendar/Confirmation';
import VIP from '@components/Calendar/VIP'; // TODO: Build VIP
import NeedsDocs from '@components/Calendar/NeedsDocs';
import Shadow from '@components/Calendar/Shadow';
import Gas from '@components/Calendar/Gas';
import SolarIcon from '@components/Calendar/SolarIcon';
import GlobeIcon from '@components/Calendar/GlobeIcon';
import WarningIcon from '@components/Calendar/WarningIcon';
import NoEntryIcon from '@components/Calendar/NoEntryIcon';
import HancockIcon from '@components/Calendar/HancockIcon';
import IncomeEligibleIcon from '@components/Calendar/IncomeEligibleIcon';
import { allEventsAtom, draggingEventAtom, pinnedEventsAtom } from '@recoil/event';
import { isAuthorized } from '@utils/AuthUtils';
import DragDropScrollUtil from '@utils/DragDropScrollUtil';

import { selectedEventState, showSidebarState } from '@recoil/eventSidebar';

const StyledEvent = styled(Clickable)(
  ({ $hasBefore: hasBefore, $hasAfter: hasAfter, color, $ignoreMultiDay: ignoreMultiDay }) => {
    // border radius calculations
    const left = hasBefore ? '0px' : '8px';
    const right = hasAfter ? '0px' : '8px';

    return `
    color: ${({ theme }) => theme.secondary[100]};
    align-self: stretch;
    display: flex;
    flex-direction: column;
    border-radius: ${left} ${right} ${right} ${left};
    margin: 5px;
    padding: 6px;
    line-height: 16px;
    margin-right: ${hasAfter && !ignoreMultiDay ? '-1px' : '5px'};
    margin-left: ${hasBefore && !ignoreMultiDay ? '-1px' : '5px'};
    background-color: ${color};
  `;
  },
);

const Header = styled.div`
  margin-bottom: 2px;
  display: flex;
  flex-direction: row;
  font-weight: bold;
`;

const BodyText = styled.div`
  color: ${({ theme }) => theme.secondary[100]}; // white
`;
const TopIcons = styled.div`
  display: flex;
  justify-content: space-between;
`;

const TopIconsRight = styled.div`
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
`;

const TopIconsLeft = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const Body = styled.div``;
const BodyHeader = styled.div``;
const Footer = styled.div``;

const SummaryNotes = styled.div``; // TODO: handle the text to overflow on adjacent event in series

const HiddenContent = styled.div`
  visibility: ${({ isHidden }) => (isHidden ? 'hidden' : 'inherit')};
  color: ${({ theme }) => theme.secondary[100]};
`;

const Event = (props) => {
  const {
    /**
     * Be careful when using the event params.
     * This is the generic event component and should care as little as possible
     * about the event itself. That's why we're passing all the text from the parent
     * component.
     *
     * However, for things like drag and drop and styling of the multiday events, we may
     * need access to things like the date and start and end times of the events
     * */
    event,
    event: {
      id,
      type,
      associatedEventIds,
      associatedEventIds: [firstEventId],
      notes: { summaryNotes },
      townWarnings,
    },
    onClick,
    backgroundColor,
    tooltip,
    headerText,
    bodyHeader,
    bodyText,
    pinnable,
    lockable,
    isVIP,
    needsDocs,
    shadow,
    noSidebar,
    lock: isLocked,
    confirmation,
    ignoreMultiDay,
    showHeatingType,
    showSolarIcon,
    showGlobeIcon,
    showNoDHWIcon,
    showHancockIcon,
    showIncomeEligibleIcon,
  } = props;

  const [showTooltip, setShowTooltip] = useState(false);
  const { business: department, state } = decodeEventType(type);
  const allEvents = useRecoilValue(allEventsAtom);
  const allPinnedEvents = useRecoilValue(pinnedEventsAtom);
  const firstInSeries = allEvents[firstEventId] || allPinnedEvents[firstEventId];

  let bodyTextArray = bodyText;
  if (bodyText && !Array.isArray(bodyText)) bodyTextArray = [bodyText];

  // Department names are different on our Database and HWE's npm module utility-service.
  const isUserScheduler = isAuthorized('Scheduler', department.replace(' ', '-'), null, state);
  const checkAuth =
    department === 'HEA' && state === 'MA'
      ? isAuthorized('Manager', department, null, state)
      : isAuthorized('Scheduler', department, null, state);
  const canLock = checkAuth && lockable;
  const canPin = isUserScheduler && !isLocked && pinnable;
  const showConfirmation = department === 'Insulation';

  const resetDraggingEvent = useResetRecoilState(draggingEventAtom);
  const setDraggingEvent = useSetRecoilState(draggingEventAtom);
  const [, drag] = useDrag({
    type: 'CALENDAR_EVENT',
    item: () => {
      // New react-dnd feature to cancel drag event
      if (!canPin) return null;
      DragDropScrollUtil.addEventListenerForDragScroll('calendar-rows');
      setDraggingEvent(event);
      return {
        id: firstEventId,
      };
    },
    end: () => {
      DragDropScrollUtil.removeEventListenerForDragScroll();
      resetDraggingEvent();
    },
  });

  const setSelectedEvent = useSetRecoilState(selectedEventState);
  const setShowSidebar = useSetRecoilState(showSidebarState);

  const handleEventClick = () => {
    onClick(); // additional onClick functionality if necessary
    if (noSidebar) return;
    setSelectedEvent(firstInSeries);
    setShowSidebar(true);
  };

  // If it is not the first in associated ids array, there must be events before
  const hasBefore = associatedEventIds.indexOf(event.id) !== 0;

  // If it is not the last in associated ids array, there must be events after
  const hasAfter = associatedEventIds.indexOf(event.id) !== associatedEventIds.length - 1;

  // If we only want to see one cell regardless of the job length, hide the other days of the event
  if (ignoreMultiDay && id !== associatedEventIds[0]) return null;

  return (
    <StyledEvent
      color={backgroundColor}
      onClick={handleEventClick}
      $hasBefore={hasBefore}
      $hasAfter={hasAfter}
      $ignoreMultiDay={ignoreMultiDay}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      ref={drag}
    >
      <HiddenContent isHidden={hasBefore}>
        <TopIcons>
          <TopIconsLeft>
            {!!townWarnings && <WarningIcon />}
            {showHeatingType && <Gas />}
            {showSolarIcon && <SolarIcon />}
            {showGlobeIcon && <GlobeIcon />}
            {showNoDHWIcon && <NoEntryIcon />}
            {showHancockIcon && <HancockIcon />}
            {showIncomeEligibleIcon && <IncomeEligibleIcon />}
          </TopIconsLeft>
          <TopIconsRight>
            {showConfirmation && <Confirmation value={confirmation || ''} />}
            {canPin && <Pin event={event} />}
            {canLock && <Lock event={event} />}
          </TopIconsRight>
        </TopIcons>
        <Header>{headerText}</Header>
        <Body>
          {bodyHeader && <BodyHeader>{bodyHeader}</BodyHeader>}
          {bodyTextArray && bodyTextArray.map((text) => <BodyText key={text}>{text}</BodyText>)}
          {summaryNotes && <SummaryNotes>{summaryNotes}</SummaryNotes>}
        </Body>
        <Footer>
          {shadow && <Shadow />}
          {needsDocs && <NeedsDocs />}
          {isVIP && <VIP />}
        </Footer>
      </HiddenContent>
      {tooltip && showTooltip && <ToolTip text={tooltip} />}
      {/* <ToolTip text={tooltip} /> */}
    </StyledEvent>
  );
};

Event.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string.isRequired,
    date: PropTypes.string,
    type: PropTypes.string.isRequired,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    associatedEventIds: PropTypes.arrayOf(PropTypes.string),
    associatedEventsId: PropTypes.string,
    jobLength: PropTypes.number,
    sfIds: PropTypes.shape({}),
    startEndTimes: PropTypes.arrayOf(PropTypes.shape({})),
    notes: PropTypes.shape({
      summaryNotes: PropTypes.string,
    }),
    townWarnings: PropTypes.arrayOf(PropTypes.shape({})),
  }), // I was going to make these required
  onClick: PropTypes.func,
  backgroundColor: PropTypes.string,
  tooltip: PropTypes.string,
  headerText: PropTypes.string,
  bodyHeader: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  bodyText: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),

  pinnable: PropTypes.bool,
  lockable: PropTypes.bool,
  isVIP: PropTypes.bool,
  needsDocs: PropTypes.bool,
  shadow: PropTypes.bool,
  noSidebar: PropTypes.bool,
  lock: PropTypes.bool,
  confirmation: PropTypes.string,
  ignoreMultiDay: PropTypes.bool,
  showHeatingType: PropTypes.bool,
  showSolarIcon: PropTypes.bool,
  showGlobeIcon: PropTypes.bool,
  showNoDHWIcon: PropTypes.bool,
  showHancockIcon: PropTypes.bool,
  showIncomeEligibleIcon: PropTypes.bool,
};

Event.defaultProps = {
  event: {},
  onClick: () => null,
  backgroundColor: null,
  tooltip: null,
  headerText: null,
  bodyHeader: null,
  bodyText: null,

  pinnable: false,
  lockable: false,
  isVIP: false,
  needsDocs: false,
  shadow: false,
  noSidebar: false,
  lock: false,
  confirmation: '',
  ignoreMultiDay: false,
  showHeatingType: false,
  showSolarIcon: false,
  showGlobeIcon: false,
  showNoDHWIcon: false,
  showHancockIcon: false,
  showIncomeEligibleIcon: false,
};

export default Event;
