import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const RadioButtonContainer = styled.div`
  padding-right: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 5px;
`;

const StyledRadio = styled.input`
  width: 16px;
  height: 16px;
  border: 1px solid ${({ theme }) => theme.secondary[200]};
  border-radius: 2px;
`;

const Label = styled.label`
  padding-left: 8px;
  font-size: 14px;
  line-height: 20px;
  color: ${({ theme }) => theme.primary[500]};
  margin-bottom: 0px;
`;

const RadioButton = (props) => {
  const { name, label, value, onChange, checked } = props;
  return (
    <RadioButtonContainer>
      <StyledRadio name={name} type="radio" onChange={onChange} value={value} checked={checked} />
      <Label htmlFor={name}>{label}</Label>
    </RadioButtonContainer>
  );
};

RadioButton.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number]).isRequired,
  checked: PropTypes.bool,
};

RadioButton.defaultProps = {
  checked: false,
};

export default RadioButton;
