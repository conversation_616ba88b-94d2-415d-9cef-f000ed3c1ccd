import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { ListHeader } from '@components/global';

const RegionContainer = styled.div`
  position: sticky;
  top: 0;
  z-index: 900;
  background-color: ${({ theme }) => theme.secondary[100]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.calendarBorder};
`;

const GroupHeader = (props) => {
  const { title, defaultTitle } = props;

  return (
    <RegionContainer>
      <ListHeader>{title || defaultTitle}</ListHeader>
    </RegionContainer>
  );
};

GroupHeader.propTypes = {
  title: PropTypes.string,
  defaultTitle: PropTypes.string,
};

GroupHeader.defaultProps = {
  title: null,
  defaultTitle: '-',
};

export default GroupHeader;
