/* eslint-disable import/no-cycle */
import React from 'react';
import PropTypes from 'prop-types';
import { Select } from '@components/global';

const InputOnEdit = (props) => {
  const {
    isEditing,
    value,
    displayValue = value,
    onChange,
    inputType,
    children,
    ...otherProps
  } = props;
  return (
    <div className="input-on-edit-container">
      {!isEditing && (
        <div className="not-editing-div" {...otherProps}>
          {displayValue}
        </div>
      )}
      {isEditing && inputType === 'select' && (
        <Select value={value} onChange={onChange} {...otherProps}>
          {children}
        </Select>
      )}
      {isEditing && inputType === 'text' && (
        <input className="is-editing-input" value={value} onChange={onChange} {...otherProps} />
      )}
    </div>
  );
};

InputOnEdit.propTypes = {
  isEditing: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  displayValue: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  inputType: PropTypes.string,
  children: PropTypes.node,
};

InputOnEdit.defaultProps = {
  isEditing: false,
  value: null,
  displayValue: undefined,
  inputType: 'text',
  children: null,
};

export default InputOnEdit;
