import React, { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import DataIntakeForm from '@components/DataIntakeForm';
import Swal from 'sweetalert2/dist/sweetalert2';
import { useRecoilValue, useRecoilState } from 'recoil';
import { formValuesState, activeFormState, activeTabState } from '@recoil/dataIntakeForm';
import { capWorkReceiptValuesState } from '@recoil/workreceipt';
import { useTabsAndFieldsForCapWR } from './useTabsAndFieldsForCapWR';
import { incomeEligibleProjectInfo } from './FormSchema/formSchema';

export const CapWorkReceipt = ({ isDealLoaded, handleFieldChange }) => {
  const activeTab = useRecoilValue(activeTabState);
  const [occupanySelected, setOccupanySelected] = useState('');
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const activeForm = useRecoilValue(activeFormState);
  const {
    docRepoStatus = 0,
    interestedInHvac = '',
    areasOnWorkscope = [],
    ownerOccupancyType = '',
    hvacInstallOpportunityId,
    yearHouseBuilt = '',
  } = formValues[activeForm] || {};

  const { capWorkReceiptTabs, capWorkReceiptMap, capWorkReceiptFields } = useTabsAndFieldsForCapWR(
    handleFieldChange,
    {
      activeTab,
      isDealLoaded,
      docRepoStatus,
      interestedInHvac,
      areasOnWorkscope,
      activeForm,
      hvacInstallOpportunityId,
    },
  );

  const showError = useCallback((selection) => {
    return Swal.fire({
      icon: 'error',
      title: 'Invalid Occupancy Type',
      text:
        selection === -1
          ? 'Unable to find a valid index for the selected occupancy type. Please check your selection and try again.'
          : 'The number of deals loaded is less than the selected occupancy type. Please adjust your selection.',
      confirmButtonText: 'OK',
    });
  }, []);

  const setPropertyOwnerInfo = useCallback(async () => {
    const indexMap = {
      'Single Family': 0,
      'Unit 1': 0,
      'Unit 2': 1,
      'Unit 3': 2,
      'Unit 4': 3,
    };
    const infoSwapIndex = indexMap[ownerOccupancyType];
    const occupancySelectionIndex = infoSwapIndex !== undefined ? infoSwapIndex : -1;
    if (infoSwapIndex === undefined || infoSwapIndex > formValues.length - 1)
      return showError(occupancySelectionIndex);
    const {
      customerName,
      customerAddress,
      cityStateZip,
      customerEmail,
      customerPhone,
      siteId,
    } = formValues[infoSwapIndex];

    const { value: confirmed } = await Swal.fire({
      icon: 'warning',
      title: 'Important: Occupancy Type Update',
      text:
        'Are you sure you want to change the Occupancy Type? This action will override the values in the Property Owner Information section with the Customer Information.',
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return false;

    setOccupanySelected(ownerOccupancyType);
    return setFormValues({
      propertyOwner: customerName,
      propertyOwnerAddress: customerAddress,
      propertyOwnerCityStateZip: cityStateZip,
      propertyOwnerEmail: customerEmail,
      propertyOwnerTelephone: customerPhone,
      propertyOwnerSiteId: siteId,
      index: activeForm,
    });
  }, [ownerOccupancyType, activeForm]);

  useEffect(() => {
    // This logic is requested by NORA to adjust CAP WR Property Owner Excel Sheet. If the end user selects "Absentee Landlord" or "Single Family RENTER",
    // The Property Owner information must be entered manually. Otherwise, validation will prevent saving and generating the work receipt.
    // If the selected value is "Single Family," "Unit 1," "Unit 2," "Unit 3," or "Unit 4," a confirmation popup is displayed.
    // In this case, the selected unit's customer information from the form values list will be assigned to the Property Owner section.
    // Another issue addressed here is if the user selects "Unit 4" but there are only 2 units, it should prevent proceeding
    // as Unit 4 does not exist. Validation will also block saving and generating the work receipt.
    setFormValues({
      singleOrMultiFamily: ['Unit 2', 'Unit 3', 'Unit 4'].includes(ownerOccupancyType)
        ? 'Multi-Family'
        : 'Single-Family',
      index: activeForm,
    });

    const isNotLandlord =
      !['', 'Absentee Landlord', 'Single Family RENTER'].includes(ownerOccupancyType) &&
      occupanySelected !== ownerOccupancyType;

    if (isNotLandlord) {
      setPropertyOwnerInfo();
    }
  }, [ownerOccupancyType, occupanySelected]);

  useEffect(() => {
    // Display the "Income Eligible" tab only for the 1st unit. Since this is for the same house,
    // the data entered for the 1st unit should also apply to any additional units.

    // For cases where the user selects multiple units, we automatically duplicate the input values
    // from the 1st unit to the other units. This ensures consistency across shared fields such as
    // "age of house" in the income project info for CAP.

    // This approach helps maintain compatibility with Salesforce validation rules on fields like
    // "age of house." Without this duplication, validation errors might occur, potentially blocking
    // the work receipt generation and saving processes.
    if (isDealLoaded && yearHouseBuilt && formValues.length > 1) {
      const updatedFormValues = formValues.map((value, index) => {
        if (index > 0) {
          const updatedValue = { ...value };
          Object.keys(incomeEligibleProjectInfo).forEach((key) => {
            if (key in formValues[0]) {
              updatedValue[key] = formValues[0][key];
            }
          });
          return updatedValue;
        }
        return value;
      });

      setFormValues(updatedFormValues);
    }
  }, [isDealLoaded, yearHouseBuilt]);

  return (
    <DataIntakeForm
      map={capWorkReceiptMap}
      fields={capWorkReceiptFields}
      valuesState={capWorkReceiptValuesState}
      tabs={capWorkReceiptTabs}
    />
  );
};

CapWorkReceipt.defaultProps = {
  isDealLoaded: false,
  handleFieldChange: () => {},
};

CapWorkReceipt.propTypes = {
  isDealLoaded: PropTypes.bool,
  handleFieldChange: PropTypes.func,
};
