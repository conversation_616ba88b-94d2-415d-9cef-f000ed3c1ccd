// External Dependencies
import React, { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue } from 'recoil';
import moment from 'moment';
import _ from 'lodash';
import swal from 'sweetalert2/dist/sweetalert2';
import { decodeEventType } from '@homeworksenergy/utility-service';

// Utils
import { getFormattedArrivalWindow } from '@utils/functions';
import eventValidation from '@utils/eventValidation';
import { AgentsManager, EventsManager } from '@utils/APIManager';
import { getUserCookie } from '@utils/AuthUtils';

// Components
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  Row,
  Col,
  FormTextBox,
  FormInput,
  FormSelect,
  FormMultiselect,
  handleFormFieldChange,
} from '@components/global/Form';
import { SecondaryButton } from '@components/global/Buttons';
import DataIntakeFormSection from '@components/DataIntakeForm/components/DataIntakeFormSection';
import { sections } from '@pages/LeadIntake/FormSchema/leadIntakeMap';

// Recoil
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';
import { formValuesState } from '@recoil/dataIntakeForm';
import { agentsFormOptionsSelector } from '@recoil/agents';

// Local utils/ components
import { scheduleEventScript } from '../utils/consts';
import { useGetNextAvailableSlots } from '../utils/getters/useGetNextAvailableSlots';
import EventScript from './EventScript';

// TODO: should this be moved to a utils file?
const leadIntakeScheduleTypeOptionsCT = [
  { key: 'HEA Visit', value: '010000' },
  { key: 'Insulation Quote', value: '010001' },
  { key: 'HEA + Sealing Service', value: '010006' },
];

const SchedulingEventContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 24px;
`;

const BookButtonContainer = styled.div`
  width: 75px;
`;

const FormTextBoxContainer = styled.div`
  margin-top: 12px;
`;

const WaitlistDaysContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
`;

const getProgram = ({ numUnitsSchedulingToday, gasProvider, electricProvider }) => {
  const numUnit = Number(numUnitsSchedulingToday);
  let programValue = null;
  const programGasProviders = ['National Grid', 'Eversource', 'Columbia Gas'];
  for (let iterator = 0; iterator < numUnit; iterator++) {
    const gas = gasProvider[iterator];
    if (programGasProviders.includes(gas)) {
      if (gas.replace(/\s/g, '').toLowerCase() === 'columbiagas') {
        programValue = 'CMA';
        return programValue;
      }
      programValue = 'NG';
    }
  }

  if (!programValue) {
    const programElectricProviders = ['National Grid', 'Eversource'];
    for (let iterator = 0; iterator < numUnit; iterator++) {
      const elect = electricProvider[iterator];
      if (programElectricProviders.indexOf(elect) >= 0) {
        programValue = elect.replace(/\s/g, '').toLowerCase();
        programValue = programValue === 'nationalgrid' ? 'NG' : 'EVR';
        return programValue;
      }
    }
  }
  if (!programValue) {
    swal.fire({
      title: 'Unknown Utility provider',
      icon: 'error',
      confirmButtonText: 'OK',
      showCancelButton: false,
    });
    return false;
  }
  return programValue;
};

// TODO: the MA and CT parts of this are diverging, and should be split into separate components
export const ScheduleEvent = ({ nextTab, state = 'MA' }) => {
  const { isManager } = getUserCookie();
  // TODO: we should be using the notes from the selectedEventState, this is different than everywhere else in the code
  const [fieldNotes, setFieldNotes] = useState('');
  const [officeNotes, setOfficeNotes] = useState('');
  const [hvacAgents, setHvacAgents] = useState([]);

  const [availableSlots, setAvailableSlots] = useRecoilState(availableSlotsAtom);
  const [event, setEvent] = useRecoilState(selectedEventState);

  const agents = useRecoilValue(agentsFormOptionsSelector);
  const [formValues, setFormValues] = useRecoilState(formValuesState);

  const { getNextAvailableSlots } = useGetNextAvailableSlots();

  const {
    type: typeFromLeadIntake,
    discountedRateCode,
    customerFirstName,
    customerLastName,
    electricProvider,
    gasProvider,
    heatingFuel,
    operationsId,
    accountId,
    dealId,
    street,
    city,
    heaOrHvac,
    numUnitsSchedulingToday,
  } = formValues;

  const numUnit = Number(numUnitsSchedulingToday);
  const isHVACSales = heaOrHvac === 'HVAC';
  const isCT = state === 'CT';
  const perUnitMultFamilyEvents = isCT;
  const { type, eventDuration, includeAgents } = event;

  useEffect(() => {
    // Since both the LeadIntakeForm and selectedEventState can set the type,
    // We need to keep them in alignment. For CT, the "typeFromLeadIntake" could be InsulationQuote based on their answers,
    // but the selectedEventState would have the default type of HEA Visit. So we're setting it here to keep them in sync.
    // We then use the selectedEventState to create the event.
    if (type !== typeFromLeadIntake) setEvent({ type: typeFromLeadIntake });
  }, [setEvent, typeFromLeadIntake, type]);

  useEffect(() => {
    const fetchHvacAgents = async () => {
      const { crews: agents } = await AgentsManager.getCrewsByStateAndDepartment({
        MA: ['HVAC Sales'],
      });
      const agentsToSet = agents.map(({ displayName, oid, region }) => {
        return { key: displayName, value: oid, region };
      });
      setHvacAgents(agentsToSet);
    };
    if (isHVACSales) {
      fetchHvacAgents();
    }
  }, []);

  const getSlots = async () => {
    const availableSlots = await getNextAvailableSlots(
      { ...formValues, includeAgents, eventDuration },
      'CT',
    );
    setAvailableSlots(availableSlots);

    return availableSlots;
  };

  const getSfIdsForMultiUnit = () => {
    if (numUnit <= 1) return {};
    const multiUnitSfIds = {};
    for (let iterator = 1; iterator < numUnit; iterator++) {
      const indexValue = iterator + 1;
      multiUnitSfIds[`dealId${indexValue}`] = formValues[`dealId${indexValue}`];
      multiUnitSfIds[`accountId${indexValue}`] = formValues[`accountId${indexValue}`];
      multiUnitSfIds[`operationsId${indexValue}`] = formValues[`operationsId${indexValue}`];
      multiUnitSfIds[
        `dealName${indexValue}`
      ] = `${customerFirstName} ${customerLastName} ${street} ${city}`;
    }
    return multiUnitSfIds;
  };

  const handleIntakeFieldChange = useCallback(
    (e, updatedValues = formValues) => {
      handleFormFieldChange(e, updatedValues, setFormValues);
    },
    [formValues, setFormValues],
  );

  const handleEventFieldChange = useCallback(
    (e, updatedValues = event) => {
      handleFormFieldChange(e, updatedValues, setEvent);
    },
    [event, setEvent],
  );

  const handleSaveClick = async () => {
    try {
      const copyEvent = _.cloneDeep(event);
      const startTime = copyEvent?.startEndTimes?.[0]?.start;
      if (!startTime) {
        return swal.fire({
          icon: 'warning',
          title:
            'The start time information is missing from the current payload. Please contact the software team for assistance in resolving this issue.',
          confirmButtonText: 'OK',
          showCancelButton: true,
        });
      }
      const displayTime = startTime.format('dddd, MMMM D, YYYY');
      const arrivalWindow = getFormattedArrivalWindow({
        dateTimeMoment: moment(startTime),
        isHVACSales,
      });

      const confirmationBoxText = `Ok, ${customerFirstName} ${customerLastName}, I have an appointment that should work on ${displayTime} with an arrival window of ${arrivalWindow}. I will book you for that.`;

      const { value: confirmed } = await swal.fire({
        title: confirmationBoxText,
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
      if (!confirmed) return false;

      copyEvent.program = getProgram({ numUnitsSchedulingToday, gasProvider, electricProvider });
      if (!copyEvent.program && !isHVACSales) {
        return swal.fire({
          icon: 'warning',
          title:
            'The program information is missing from the current payload. Please contact the software team for assistance in resolving this issue.',
          confirmButtonText: 'OK',
          showCancelButton: true,
        });
      }

      copyEvent.fuelType = heatingFuel[0].toLowerCase();
      copyEvent.sfIds = {
        operationsId,
        accountId,
        dealId,
        dealName: `${customerFirstName} ${customerLastName} ${street} ${city}`,
        ...getSfIdsForMultiUnit(),
      };
      let type = heaOrHvac === 'HEA' ? '000000' : '000100';
      const { state, business } = decodeEventType(type);
      const isMultiFamHea = Number(numUnitsSchedulingToday > 1) && heaOrHvac === 'HEA';
      const isCap = discountedRateCode === 'Yes' && heaOrHvac === 'HEA';

      if (isMultiFamHea) type = '000001';
      if (isCap) type = '000006';

      const insertObjects = eventValidation[state][business].create({
        ...copyEvent,
        ...formValues,
        address: formValues?.customerAddress,
        isHvacLeadIntake: heaOrHvac === 'HVAC',
        type,
        numUnit,
        notes: {
          fieldNotes,
          officeNotes,
        },
      });

      if (!insertObjects) {
        return swal.fire({
          title: 'Something went wrong!',
          icon: 'error',
          confirmButtonText: 'OK',
          showCancelButton: false,
        });
      }

      let agentName = null;

      const agentList = isHVACSales ? hvacAgents : agents;
      const agentDetail = agentList?.find((agent) => agent.value === insertObjects?.oids?.[0]);
      if (!agentDetail) {
        return swal.fire({
          title: 'Agent not found!',
          icon: 'error',
          confirmButtonText: 'OK',
          showCancelButton: false,
        });
      }
      ({ key: agentName } = agentDetail);
      const response = await EventsManager.create({ ...insertObjects, agentName });
      if (!response) return false;
      return nextTab();
    } catch (error) {
      console.error(error);
      return swal.fire({
        title: error,
        icon: 'error',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
    }
  };

  const handleSaveClickCT = async () => {
    const copyEvent = _.cloneDeep(event);
    try {
      // TODO: clean up this, not all of this needs to be passed and it causes lots of issues with data getting overwritten by the next spread object
      const params = {
        ...copyEvent,
        ...formValues,
        address: formValues?.customerAddress,
        type,
        notes: {
          officeNotes,
        },
      };

      const insertObjects = eventValidation.CT.HEA.create(params);

      if (!insertObjects) {
        return false;
      }
      const response = await EventsManager.create(insertObjects);
      if (!response) return false;
      return nextTab();
    } catch (error) {
      return swal.fire({
        title: error,
        icon: 'error',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
    }
  };

  const { title: eventScriptTitle, arrivalDetails } = scheduleEventScript[state];

  const handleSaveButtonClick = isCT ? handleSaveClickCT : handleSaveClick;

  return (
    <SchedulingEventContainer>
      {state === 'MA' && <EventScript title={eventScriptTitle} arrivalDetails={arrivalDetails} />}
      {isCT && (
        <Row>
          <Col>
            <FormSelect
              title="Visit Type"
              name="type"
              value={type}
              onChange={handleIntakeFieldChange}
              options={leadIntakeScheduleTypeOptionsCT}
            />
            {['010005', '010006'].includes(type) && (
              <FormInput
                title="Duration (Hours)"
                name="eventDuration"
                value={eventDuration}
                type="number"
                min={0.5}
                step={0.5}
                onChange={handleEventFieldChange}
                required
              />
            )}
            <FormMultiselect
              title="Include HES(s)"
              name="includeAgents"
              value={includeAgents}
              options={agents}
              onChange={handleEventFieldChange}
            />
            <SecondaryButton left onClick={getSlots}>
              Find Available Slots
            </SecondaryButton>
          </Col>
        </Row>
      )}
      {availableSlots && (
        <>
          <AvailableSlots
            singleAgent
            perUnitMultFamilyEvents={perUnitMultFamilyEvents}
            allowAgentSelect={isManager}
          />
          <FormTextBoxContainer>
            <FormTextBox
              name={`notes.${isCT ? 'officeNotes' : 'fieldNotes'}`}
              value={isCT ? officeNotes : fieldNotes}
              title="HES Notes"
              placeholder="....."
              onChange={(e) =>
                isCT ? setOfficeNotes(e.target.value) : setFieldNotes(e.target.value)
              }
            />
          </FormTextBoxContainer>
          {isCT && (
            <WaitlistDaysContainer>
              <DataIntakeFormSection {...sections.scheduleEventQuestionsCT} />
            </WaitlistDaysContainer>
          )}
          <BookButtonContainer>
            <BookSlotsButton
              handleBookSlots={() => handleSaveButtonClick()}
              perUnitMultFamilyEvents={false}
              // TODO: clean up.
              // This should work the same as allowAgentSelect on AvailableSlots,
              // but doesn't because it's not compatible with the perUnitMultFamilyEvents in the BookSlotsButton component
              allowAgentSelect={false}
            />
          </BookButtonContainer>
        </>
      )}
    </SchedulingEventContainer>
  );
};

ScheduleEvent.propTypes = {
  nextTab: PropTypes.func.isRequired,
  state: PropTypes.string,
};

ScheduleEvent.defaultProps = {
  state: 'MA',
};
