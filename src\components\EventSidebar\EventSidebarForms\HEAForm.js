import React, { Suspense, useEffect, useState, useRef } from 'react';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';
import moment from 'moment';
import Swal from 'sweetalert2/dist/sweetalert2';
import { EventHistoryModal } from '@components/EventHistoryModal/EventHistoryModal';

import useStartEndTimes from '@hooks/useStartEndTimes';

import {
  selectedEventState,
  isSchedulingViewAtom,
  schedulingTypeAtom,
  showSidebarState,
  persistSelectedEventState,
  availableSlotsAtom,
  isSlotsSearchAtom,
} from '@recoil/eventSidebar';

import { allAgentsFormOptionsState } from '@recoil/agents';
import { jobAttributesSelectorFamily, activeTabIndexAtom } from '@recoil/app';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import Tabs from '@components/global/Tabs/Tabs';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormTextBox,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormMultiselect,
} from '@components/global/Form';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import TownWarnings from '@components/townWarnings/TownWarnings';

import { isAuthorized } from '@utils/AuthUtils';
import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import { DropDownMenu, LoadingIndicator } from '@components/global';
import { Payment } from '@components/Calendar/EventComponents';
import CapDocRepo from '@components/DocRepo/CAPHEA';
import { SalesforceManager, UtilityManager } from '@utils/APIManager';
import {
  returnVisitReasons,
  returnVisits,
  leadVendors,
} from '@utils/businessLogic/heaBusinessLogic';
import { redirectScheduleWx, copyTextToClipboard } from '@utils/functions';
import { History } from '@styled-icons/boxicons-regular/History';

const IconContainer = styled.div`
  margin-right: 10px;
  margin-top: auto;
  height: 24px;
  & :hover {
    cursor: pointer;
  }
`;

const HistoryIcon = styled(History)`
  height: 24px;
  cursor: pointer;
`;

const HeaderActionContainer = styled.div`
  display: flex;
  flex-direction: row;
  background: ${({ theme }) => theme.secondary[200]};
`;
const CapApprovalLeadVendorStyle = styled(HeaderLabel)`
  color: ${({ theme }) => theme.colors.red};
`;
const CancelButtonText = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;
const StyledSpan = styled.span``;

const HEAForm = ({ handleCancelClick, handleSaveClick }) => {
  const handleTimeChange = useStartEndTimes();
  const setPersistSelectedEvent = useSetRecoilState(persistSelectedEventState);
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const setIsSchedulingView = useSetRecoilState(isSchedulingViewAtom);
  const setSchedulingType = useSetRecoilState(schedulingTypeAtom);
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(allAgentsFormOptionsState);
  const activeUnitTab = useRecoilValue(activeTabIndexAtom(['tabs']));
  const activeDepartmentTab = useRecoilValue(activeTabIndexAtom(['department']));
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const paymentRef = useRef();
  const {
    id,
    sfIds,
    customerName,
    notes,
    numUnit,
    phoneNumber,
    email,
    oids,
    startTime,
    endTime,
    date,
    scheduledBy,
    scheduledDate,
    type,
    address,
    leadVendor,
    capApprovalLeadVendor,
    lock,
    townWarnings,
    returnReason,
    startEndTimes,
    attributes,
    approvalSoftware,
  } = event;

  const isCreate = !id;

  const isCap = type === '000006';
  const showDocRepo2 = isCap && moment(date).isSameOrAfter(moment('06-06-2024'));

  const createEventTypes = [
    { key: 'Return Visit', value: '000004' },
    { key: 'Custom Block', value: '999999' },
  ];

  useEffect(() => {
    if (!date || !isCreate) return;

    // If no type for create event, push to the returnVisit form
    if (!type) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  const displayAddress = address?.displayAddress;

  // Return visits events are only allowed to cancel an event
  const isReturn = returnVisits.includes(type);
  const isSchedulerLevel = isAuthorized('Scheduler', 'All');
  const isUserHeaScheduler = isAuthorized('Scheduler', 'HEA');
  const canPerformActions = isSchedulerLevel && !lock;

  const agent = agents.find((agent) => agent.value === oids[0]);

  const hesName = agent?.key;
  const eventTypeName = type ? decodeEventType(type)?.businessEvent : '';

  const convertEventDict = {
    '000000': { name: 'VEA', type: '000002' },
    '000001': { name: 'VEA', type: '000002' },
    '000002': { name: 'HEA', type: numUnit >= 2 ? '000001' : '000000' },
  };

  const {
    dealId,
    dealId2,
    dealId3,
    dealId4,
    operationsId,
    operationsId2,
    operationsId3,
    operationsId4,
    opportunityId,
  } = sfIds;
  const unitDealIdsArr = [dealId, dealId2, dealId3, dealId4];

  const multiUnit = numUnit && Number(numUnit) > 1;

  const allowConvert = Object.keys(convertEventDict);

  const handleConvertEventClick = async () => {
    const updateEvent = { ...event, type: convertEventDict[type].type };
    handleSaveClick(updateEvent);
  };

  const openWRPage = () => {
    setShowSidebar(false);
    setPersistSelectedEvent(true);
    const filteredDeals = unitDealIdsArr.filter((item) => item);
    if (filteredDeals?.length) {
      const isMultiFamily = filteredDeals.length > 1;
      const isOffline = true;
      UtilityManager.openOnlineWorkReceipt(
        filteredDeals.join('-'),
        isCap,
        isOffline,
        isMultiFamily,
      );
    }
    return true;
  };

  const dropDownList = [];
  const list = [{ text: 'SF Deal Page', onClick: (dealId) => onClickDealId(dealId) }];

  if (canPerformActions && allowConvert.includes(type))
    dropDownList.push({
      text: `Convert to ${convertEventDict[type]?.name}`,
      onClick: () => handleConvertEventClick(),
    });

  if (!showDocRepo2)
    list.unshift({
      text: 'Doc Repo link',
      onClick: (dealId) => UtilityManager.openDocRepo(dealId, customerName, leadVendor, numUnit),
    });

  list.forEach(({ text, onClick }) => {
    const childrens = [];
    if (multiUnit)
      for (let k = 0; k < Number(numUnit); k++) {
        childrens.push({
          text: `${text} (unit ${k + 1})`,
          onClick: () => onClick(unitDealIdsArr[k]),
        });
      }
    dropDownList.push({
      text,
      onClick: multiUnit ? {} : () => onClick(unitDealIdsArr[0]),
      childrens,
    });
  });

  dropDownList.push({
    text: 'Work Receipt',
    onClick: () => openWRPage(),
  });

  const handleHistoryModal = () => setIsHistoryModalOpen(!isHistoryModalOpen);

  const getSecondaryTabs = (dealId) => {
    const tabs = [
      {
        name: `docRepo_hea_${dealId}`,
        title: 'HEA',
        component: <CapDocRepo uniqueId={dealId} department="HEA-CAP" />,
      },
      {
        name: `docRepo_hvac_${dealId}`,
        title: 'HVAC',
        component: <CapDocRepo uniqueId={dealId} department="HVAC_Sales-CAP" />,
      },
    ];
    return tabs;
  };

  let tabs = [];
  let secondaryTabs = getSecondaryTabs(dealId);

  const nonReturnVisitTypes = ['000000', '000001', '000002', '000003', '000006'];
  const today = moment();
  const eventDate = moment(date);
  const dealIdsArr = [dealId, dealId2, dealId3, dealId4];
  const validDealIds = dealIdsArr.filter((deal) => deal);
  if (nonReturnVisitTypes.includes(type)) {
    dropDownList.push({
      text: 'Copy All Deal IDs',
      onClick: () => copyTextToClipboard(validDealIds?.join('_') || '', true),
    });

    const canScheduleHvacOrWx = eventDate < today;
    const operationsIdsArr = [operationsId, operationsId2, operationsId3, operationsId4];
    const operationsChildren = [];
    const operationsIds = [];
    const paymentIds = [];
    if (multiUnit) {
      for (let k = 0; k < Number(numUnit); k++) {
        if (operationsIdsArr[k]) {
          if (canScheduleHvacOrWx) {
            operationsChildren.push({
              text: `Schedule Unit ${k + 1}`,
              onClick: () => redirectScheduleWx(operationsIdsArr[k]),
            });
          }
        }
        operationsIds.push({
          text: `Ops Id Unit ${k + 1}`,
          onClick: () => copyTextToClipboard(operationsIdsArr[k]),
        });
        paymentIds.push({
          text: `Payment Unit ${k + 1}`,
          onClick: () => paymentRef.current.handlePaymentButtonClick(k, true),
        });
        if (k > 0 && unitDealIdsArr[k]) {
          secondaryTabs = getSecondaryTabs(unitDealIdsArr[k]);
          tabs.push({
            name: `docRepo${unitDealIdsArr[k]}`,
            title: `Doc Repo (Unit ${k + 1})`,
            component: <Tabs tabs={secondaryTabs} id="department" />,
            children: secondaryTabs,
          });
        }
      }
      dropDownList.push({
        text: 'Payment',
        onClick: () => {},
        childrens: paymentIds,
      });
    }
    if (canScheduleHvacOrWx) {
      dropDownList.push({
        text: 'Schedule HVAC',
        onClick: () => handleHVACScheduleClick(),
      });
      dropDownList.push({
        text: 'Schedule WX',
        onClick: multiUnit ? {} : () => redirectScheduleWx(operationsIdsArr[0]),
        childrens: operationsChildren,
      });
    }
    dropDownList.push({
      text: 'Copy WX Booking ID',
      onClick: multiUnit ? {} : () => copyTextToClipboard(operationsIdsArr[0]),
      childrens: operationsIds,
    });
  }
  dropDownList.push({
    text: 'Sync from SF',
    onClick: () => syncFromSalesforce(),
  });

  const syncFromSalesforce = async () => {
    setLoading(true);
    const filterDealIds = unitDealIdsArr.filter((id) => id);

    const salesforceInfo = await SalesforceManager.getHEAEventInfoWithDealIds(filterDealIds);
    setLoading(false);

    if (!salesforceInfo) return;

    const sfIds = { ...event.sfIds, ...salesforceInfo.siteIds, ...salesforceInfo.sfIds };
    setEvent({
      ...event,
      sfIds,
      leadVendor: salesforceInfo.leadVendor,
      capApprovalLeadVendor: salesforceInfo.capApprovalLeadVendor,
      email: salesforceInfo.email,
      approvalSoftware: salesforceInfo.approvalSoftware,
    });
  };

  // eslint-disable-next-line consistent-return
  const handleHVACScheduleClick = async () => {
    if (notes?.fieldNotes.includes('DO NOT book HVAC Sales call')) {
      return Swal.fire({
        icon: 'error',
        title:
          'This customer already has an HVAC appointment booked with our partner company. DO NOT BOOK AN HVAC SALES APPOINTMENT.',
      });
    }
    const canSchedule = await UtilityManager.validateZipCode(address?.postalCode, '000100');
    if (canSchedule) {
      handleFormFieldChange(
        { target: { name: 'type', value: '000100' } },
        { ...event, notes: { ...notes, fieldNotes: '' }, isHEASchedulingHvacSales: true },
        setEvent,
      );
      resetAvailableSlots();
      setIsSlotsSearch(true);
    } else {
      return Swal.fire({ icon: 'error', title: 'Out of Territory Zip Code for HVAC Schedule.' });
    }
  };

  const handleSchedulingActionClick = (action) => {
    setSchedulingType(action);
    setIsSchedulingView(true);
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  // If creating a return visit, don't attempt to open the deal page
  const onClickDealId = (dealId) => {
    if (!isCreate && dealId) UtilityManager.openSfPage(dealId, 'Deal__c');
  };

  const renderFormFields = () => {
    return (
      <>
        <TownWarnings townWarnings={townWarnings} />
        <EventSidebarBody>
          <Row>
            <Col size={1}>
              {numUnit && Number(numUnit) && (
                <SfIdInputs sfObjectType="deal" title="salesforce" setLoading={setLoading} />
              )}
            </Col>
            <Col size={1}>
              <SfIdInputs
                sfObjectType="site"
                setLoading={setLoading}
                allowMultiUnit={false}
                allowEdit
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput readOnly name="date" value={date} title="date" placeholder="" />
            </Col>
            <Col size={1}>
              {isCreate ? (
                <FormSelect
                  required
                  title="Visit Type"
                  placeholder="Select Visit Type"
                  name="type"
                  value={type}
                  onChange={handleFieldChange}
                  options={createEventTypes}
                />
              ) : (
                <FormInput
                  readOnly
                  name="eventTypeName"
                  value={eventTypeName}
                  title="Visit Type"
                  placeholder=""
                />
              )}
            </Col>
          </Row>
          {/* TODO: currently no way to see reason for return after create. Doesn't get stored anywhere other than salesforce */}
          {isReturn && isCreate && (
            <Row>
              <Col>
                <FormSelect
                  required
                  title="Reason for Return"
                  placeholder="Select Reason for Return"
                  name="returnReason"
                  value={returnReason}
                  onChange={handleFieldChange}
                  options={returnVisitReasons}
                />
              </Col>
            </Row>
          )}
          <Row>
            {isCreate ? (
              <Col size={2}>
                <FormStartEndDateTimePickers
                  key="startEndTime"
                  name="startEndTime"
                  displayDay={false}
                  startEndTimes={startEndTimes}
                  onChange={handleTimeChange}
                  dateFormat="h:mm aa"
                  allowDateSelect={false}
                  direction="row"
                />
              </Col>
            ) : (
              <>
                <Col size={1}>
                  <FormInput
                    readOnly
                    name="startTime"
                    value={moment(startTime, 'HH:mm:ss').format('h:mm A')}
                    title="Start Time"
                    placeholder=""
                  />
                </Col>
                <Col size={1}>
                  <FormInput
                    readOnly
                    name="endTime"
                    value={moment(endTime, 'HH:mm:ss').format('h:mm A')}
                    title="End Time"
                    placeholder=""
                  />
                </Col>
              </>
            )}
          </Row>
          {!isCreate && !['CAP', 'Mixed Income'].includes(leadVendor) && (
            <Row>
              <Col>
                <FormSelect
                  required
                  title="Lead Vendor"
                  placeholder="Select Lead Vendor"
                  name="leadVendor"
                  value={leadVendor}
                  onChange={handleFieldChange}
                  options={leadVendors}
                />
              </Col>
            </Row>
          )}
          <Row>
            {attributes.length > 0 && (
              <Col>
                <FormMultiselect
                  required
                  title="Requirement(s)"
                  name="attributes"
                  recoilOptions={jobAttributesSelectorFamily({ type })}
                  onChange={handleFieldChange}
                  value={attributes}
                />
              </Col>
            )}
            <Col>
              <FormInput
                readOnly
                name="address"
                value={displayAddress || ''}
                title="location"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormTextBox
                readOnly={!isUserHeaScheduler}
                name="notes.fieldNotes"
                value={notes.fieldNotes}
                title="Notes"
                placeholder=""
                onChange={handleFieldChange}
                required={isReturn}
              />
            </Col>
          </Row>

          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="phoneNumber"
                value={phoneNumber}
                title="Customer Phone Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="email"
                value={email}
                title="Customer Email"
                placeholder=""
              />
            </Col>
          </Row>
          {isSchedulerLevel && !isCreate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
      </>
    );
  };

  tabs = [
    {
      name: 'details',
      title: 'Details',
      component: renderFormFields(),
    },
    {
      name: 'docRepo',
      title: multiUnit ? 'Doc Repo (Unit 1)' : 'Doc Repo',
      component: <Tabs tabs={getSecondaryTabs(dealId, opportunityId)} id="department" />,
      children: getSecondaryTabs(dealId, opportunityId),
    },
    ...tabs,
  ];
  const FormComponent = showDocRepo2 ? (
    <>
      <HeaderActionContainer>
        <Tabs tabs={tabs} />
      </HeaderActionContainer>
      {tabs?.[activeUnitTab]?.component}
      {tabs?.[activeUnitTab]?.children?.[activeDepartmentTab]?.component}
    </>
  ) : (
    renderFormFields()
  );

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
              <HeaderLabel>Lead Vendor: {leadVendor}</HeaderLabel>
              <HeaderLabel $bold>Approval Software: {approvalSoftware}</HeaderLabel>
              {capApprovalLeadVendor?.length !== 0 && isCap && (
                <CapApprovalLeadVendorStyle>
                  CAP Approval Lead Vendor: {capApprovalLeadVendor}
                </CapApprovalLeadVendorStyle>
              )}
            </Col>
            <Col size={1} right>
              <HeaderLabel>HES:</HeaderLabel>
              <HeaderTitle>{hesName}</HeaderTitle>
            </Col>
            <Col size={0} left>
              <Row>
                <IconContainer>
                  <HistoryIcon onClick={handleHistoryModal} />
                </IconContainer>
                <IconContainer>
                  {canPerformActions && <Pin event={event} onSidebar />}
                </IconContainer>
                <IconContainer>{isUserHeaScheduler && <Lock event={event} />}</IconContainer>
                <DropDownMenu listItems={dropDownList} />
              </Row>
              <Row>
                <Payment
                  ref={paymentRef}
                  showButton={!multiUnit}
                  onClick={() => paymentRef.current.handlePaymentButtonClick()}
                />
              </Row>
            </Col>
          </Row>
        </EventSidebarHeader>
        {FormComponent}

        {!isCreate && event?.id && (
          <EventHistoryModal
            isOpen={isHistoryModalOpen}
            id={event.id}
            handleClose={handleHistoryModal}
          />
        )}
        {activeUnitTab === 0 && (
          <EventSidebarFooter
            leftButtons={
              <>
                <PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton>
                {canPerformActions && !isCreate && (
                  <CancelButton onClick={() => handleCancelClick()} shrink>
                    <CancelButtonText>
                      <StyledSpan>Cancel Event</StyledSpan>
                      <StyledSpan>Do Not Reschedule</StyledSpan>
                    </CancelButtonText>
                  </CancelButton>
                )}
              </>
            }
            rightButtons={
              canPerformActions && !isReturn ? (
                <>
                  <PrimaryButton onClick={() => handleSchedulingActionClick('reschedule')}>
                    Reschedule
                  </PrimaryButton>
                  <PrimaryButton onClick={() => handleSchedulingActionClick('return')}>
                    Return
                  </PrimaryButton>
                  <PrimaryButton onClick={() => handleSchedulingActionClick('reassign')}>
                    Reassign
                  </PrimaryButton>
                </>
              ) : null
            }
          />
        )}
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

HEAForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default withRouter(HEAForm);
