import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { isAuthorized, hasRole } from '@utils/AuthUtils';
import { toggleSidebar } from '@utils/functions';
import { SideBarLink, SideBarLinkGroup } from './components';

const hweLogo = require('@assets/hwe-logo.svg');

const ClickOutside = styled.div`
  @media (min-width: ${({ theme }) => theme.breakpoints.lg}) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 0;
    background-color: rgba(0, 0, 0, 0.5);

    &.closed {
      display: none;
    }
  }
`;
const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  top: 0;
  left: 0;
  overflow-x: hidden;
  height: 100%;
  width: 20%;
  padding-top: 50px;
  color: ${({ theme }) => theme.secondary[200]};
  background-color: ${({ theme }) => theme.primary[500]};
  transition: width 0.5s;
  @media (min-width: ${({ theme }) => theme.breakpoints.lg}) {
    position: absolute;
    z-index: 901;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    width: 40%;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 80%;
  }
  &.closed {
    width: 0;
  }
`;
const Logo = styled(Link)`
  position: relative;
  margin: 0 auto;
  width: 75%;
`;
const SidebarDivider = styled.div`
  position: relative;
  margin: 20px auto;
  width: 80%;
  height: 1px;
  background-color: white;
`;
const StyledSidebarLinkGroup = styled(SideBarLinkGroup)`
  display: flex;
  flex-direction: column;
  .arrow {
    transition: 0.3s;
    &.down {
      transform: rotate(90deg);
    }
  }
  .links {
    height: 0px;
    margin-left: 10px;
    overflow: hidden;
    transition: height 0.3s ease;
  }
`;
const StyedLinks = styled(SideBarLink)`
  height: 0px;
  margin-left: 10px;
  overflow: hidden;
  transition: 0.3s;
`;

// Partner Agents are of Two Types i.e NON HWE Agent and HWE Agent
// Non HWE Agent must view only Sub Hub Page
const isPartner =
  !isAuthorized('Agent') &&
  (hasRole('Agent', 'Partners') || hasRole('Agent', 'Insulation Partners'));

// TODO: Figure out how to disable the home and region for Partners

// Width of sidebar is controlled from menu bar
const SideBar = () => {
  const [scheduler2Url, setScheduler2Url] = useState();
  const menuItems = [
    { title: 'Home', route: 'home' },
    {
      title: 'Lead Intake',
      route: 'lead-intake',
      permissions: { department: 'HEA', minRole: 'Scheduler', state: 'MA' },
    },
    {
      title: 'Pay Dashboard',
      route: 'pay-dashboard',
      permissions: { department: 'HEA', minRole: 'Agent', state: 'MA' },
    },
    {
      title: 'Work Receipt Beta',
      route: 'work-receipt-dev',
      permissions: { department: 'HEA', minRole: 'Agent', state: 'MA' },
      url: `${scheduler2Url}/work-receipt-dev`,
    },
    {
      title: 'Lead Intake (Beta)',
      route: 'lead-intake-dev',
      permissions: [
        { department: 'CIA', minRole: 'Agent', state: 'MA' },
        { department: 'Marketing', minRole: 'Agent', state: 'MA', allowExternal: true },
      ],
      url: `${scheduler2Url}/lead-intake-dev`,
    },
    {
      title: 'Connecticut - Lead Intake',
      route: 'lead-intake-CT',
      permissions: [
        { department: 'CIA', minRole: 'Agent', state: 'CT' },
        { department: 'HEA', minRole: 'Scheduler', state: 'CT' },
        { department: 'Marketing', minRole: 'Agent', state: 'CT', allowExternal: true },
      ],
      url: `${scheduler2Url}/lead-intake-CT`,
    },
    {
      title: 'View Schedule',
      permissions: { minRole: 'Agent', allowExternal: true },
      children: [
        {
          title: 'HEA',
          route: 'view-hea-schedule',
          permissions: { department: 'HEA', state: 'MA', minRole: 'Scheduler' },
        },
        {
          title: 'HVAC Sales',
          route: 'view-hvac-sales-schedule',
          permissions: { department: 'HVAC-Sales', state: 'MA', minRole: 'Scheduler' },
        },
        {
          title: 'HVAC Install',
          route: 'view-hvac-install-schedule',
          permissions: {
            department: 'HVAC-Install',
            state: 'MA',
            minRole: 'Basic',
            allowExternal: true,
          },
        },
        {
          title: 'Insulation Install',
          route: 'view-insulation-install-schedule',
          permissions: {
            department: 'Insulation',
            state: 'MA',
            minRole: 'Basic',
            allowExternal: true,
          },
        },
        {
          title: 'Connecticut - HEA',
          route: 'view-hea-schedule-CT',
          permissions: { department: 'HEA', minRole: 'Agent', state: 'CT' },
        },
      ],
    },
    {
      title: 'Sub Hub',
      route: 'view-partners-schedule',
      permissions: { department: 'Partners', minRole: 'Agent', state: 'MA', allowExternal: true },
    },
    {
      title: 'Payroll',
      permissions: { department: 'Payroll', minRole: 'Basic', state: 'MA' },
      children: [
        {
          title: 'WX Payroll',
          route: 'wx-payroll',
          permissions: { department: 'Payroll', minRole: 'Basic' },
        },
        {
          title: 'Finance Payroll',
          route: 'finance-payroll',
          permissions: { department: 'Payroll', minRole: 'Scheduler' },
        },
        {
          title: 'Money Collection',
          route: 'money-collection',
          permissions: { department: 'Payroll', minRole: 'Scheduler' },
        },
      ],
    },
    {
      title: 'Administrator Functions',
      permissions: { department: null, minRole: 'Scheduler', allowExternal: true },
      children: [
        // TODO: set up permissions based on an array of departments
        {
          title: 'Edit Users',
          permissions: { minRole: 'Manager' },
          route: 'edit-user-info',
        },
        {
          title: 'Edit Agents',
          route: 'edit-agent-info',
          permissions: {
            minRole: 'Manager',
            department: 'All',
            allowExternal: true,
          },
        },
        {
          title: 'Reporting',
          route: 'reporting',
          permissions: { minRole: 'Super User', department: 'All', state: 'MA' },
        },
        {
          title: 'Event Patterns',
          route: 'event-patterns',
          permissions: { minRole: 'Manager', department: 'HEA', state: 'MA' },
        },
        {
          title: 'Work Receipt',
          permissions: { minRole: 'Super User', state: 'MA' },
          children: [{ title: 'Upload New Tempate', route: 'work-receipt-admin' }],
        },
        {
          title: 'Application Settings',
          permissions: { minRole: 'Super User', state: 'MA' },
          route: 'application-settings',
        },
      ],
    },
    {
      title: 'Region Map',
      url: 'https://storymaps.arcgis.com/stories/486f75f4ad13456c9b2b15993fcd3d1d',
      permissions: { minRole: 'Agent', state: 'MA' },
    },
    {
      title: 'Customers',
      route: 'customers',
      permissions: [
        { department: 'CIA', minRole: 'Agent', state: 'MA' },
        { department: 'Marketing', minRole: 'Agent', state: 'MA' },
      ],
    },
    {
      title: 'Search For Project',
      route: 'search-salesforce',
      permissions: [
        { minRole: 'Agent', state: 'CT' },
        { department: 'CT', minRole: 'Agent' },
      ],
    },
  ];

  const renderMenuItems = (item) => {
    const { title, children, route, permissions, url } = item;
    const linkId = title?.toLowerCase().replace(/ /g, '-');
    if (permissions) {
      const arrayPermissions = Array.isArray(permissions) ? permissions : [permissions];
      // Check permissions for multi departments permissions. A user can have multi roles can be manager in one department and agent in other
      const isAuthorizedForAnyDepartment = arrayPermissions.some(
        ({ department, minRole, allowExternal, state }) =>
          isAuthorized(minRole, department, allowExternal, state),
      );
      if (!isAuthorizedForAnyDepartment) return null;
    }

    if (children) {
      return (
        <StyledSidebarLinkGroup key={linkId} linkId={linkId} title={title}>
          {children.map(renderMenuItems)}
        </StyledSidebarLinkGroup>
      );
    }

    return (
      <StyedLinks key={linkId} linkId={linkId} route={route} url={url}>
        {title}
      </StyedLinks>
    );
  };

  useEffect(() => {
    let scheduler2Url;
    const currentHost = window.location.origin;
    switch (currentHost) {
      case 'https://sch.homeworksenergy.com':
        scheduler2Url = 'https://sch.homeworksenergy.com';
        break;
      case 'http://localhost:8083':
        scheduler2Url = 'http://localhost:8083';
        break;
      case 'https://staging.homeworksenergy.com':
        scheduler2Url = 'https://staging.homeworksenergy.com';
        break;
      default:
        scheduler2Url = 'https://ts01.homeworksenergy.com';
        break;
    }
    setScheduler2Url(scheduler2Url);
  }, []);
  return (
    <>
      {/* lint error for accessibility, but this is just a click outside so we can ignore */}
      {/* eslint-disable-next-line */}
      <ClickOutside className="click-outside" onClick={toggleSidebar} />
      <Sidebar className={`side-bar ${isPartner ? 'closed' : ''}`}>
        <Logo to="/home">
          <img src={hweLogo} alt="logo" />
        </Logo>
        <SidebarDivider />
        {menuItems.map(renderMenuItems)}
      </Sidebar>
    </>
  );
};

export default SideBar;
