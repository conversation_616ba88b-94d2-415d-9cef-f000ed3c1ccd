import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HesHvacSubmitToCAPForm = ({ record }) => {
  const { installContractPrice, opportunityId, hvacSubmittedIncentive, capQuoteStatus } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput
            readOnly
            name="opportunityId"
            value={opportunityId}
            title="Opportunity Id"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hvacSubmittedIncentive"
            value={hvacSubmittedIncentive}
            title="HVAC Submitted Incentive"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="installContractPrice"
            value={installContractPrice}
            title="Installed Contract Price"
            placeholder=""
          />
          <FormInput
            readOnly
            name="capQuoteStatus"
            value={capQuoteStatus}
            title="CAP Quote Status"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HesHvacSubmitToCAPForm.propTypes = {
  record: PropTypes.shape({
    installContractPrice: PropTypes.string,
    opportunityId: PropTypes.string,
    hvacSubmittedIncentive: PropTypes.string,
    capQuoteStatus: PropTypes.string,
  }),
};

HesHvacSubmitToCAPForm.defaultProps = {
  record: {},
};

export default HesHvacSubmitToCAPForm;
