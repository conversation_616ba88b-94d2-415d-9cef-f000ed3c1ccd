import React, { useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';

import { decodeEventType } from '@homeworksenergy/utility-service';

import { showFilterBarState } from '@recoil/calendar';
import { calendarTypeAtom } from '@recoil/app';

// eslint-disable-next-line import/no-cycle
import { RefreshButton } from '@components/global';
import CalendarFilter from './CalendarFilter';
import Legend from './Legend';
import ManagerTrucksFilter from './ManagerTrucksFilter';
// eslint-disable-next-line import/no-cycle
import DatePicker from './DatePicker';
import FilterToggle from '../FilterToggle';
// eslint-disable-next-line import/no-cycle
import ScrollCalendarArrows from './ScrollCalendarArrows';

const StyledCalendarControlHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 15px 15px 0px 15px;
  width: 100%;

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptop)} {
    display: none;
  }
`;

const StyledCalendarInteractionRow = styled.div`
  display: flex;
  justify-content: space-between;
`;

const FlexRowContainer = styled.div`
  display: flex;
  flex-direction: row;
  padding-bottom: 10px;
`;

/**
 * Need left and right sides to be the same width for
 * centering the date picker and right aligning the scroll arrow
 */
const FlexFix = styled.div`
  width: 130px;
  overflow: visible;
`;

const MobileCalendarControlHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 20px auto;
  gap: 10px;
  ${({ theme }) => theme.screenSize.up(theme.breakpoints.laptop)} {
    display: none;
  }
`;

const CalendarControlHeader = (props) => {
  const {
    startDate,
    scrollCalendar,
    isMonthly,
    showMonthYearPicker,
    setRegionFilter,
    setEventTypeFilter,
    setAgentTypeFilter,
    regionTypeSelected,
    eventTypeSelected,
    agentTypeSelected,
  } = props;

  const calendarType = useRecoilValue(calendarTypeAtom);
  const showFilterBar = useRecoilValue(showFilterBarState);

  const datePicker = (
    <DatePicker
      startDate={startDate}
      isMonthly={isMonthly}
      handleDateChange={scrollCalendar}
      showMonthYearPicker={showMonthYearPicker}
    />
  );

  const regionFilter = (
    <CalendarFilter
      value={regionTypeSelected}
      showFilterBar={showFilterBar}
      setFilter={(value) => setRegionFilter(Number(value))}
      isRegion
    />
  );
  const eventTypeFilter = (
    <CalendarFilter
      value={eventTypeSelected}
      setFilter={(value) => setEventTypeFilter(value)}
      isEventType
    />
  );
  const agentTypeFilter = (
    <CalendarFilter
      value={agentTypeSelected}
      showFilterBar={showFilterBar}
      setFilter={(value) => setAgentTypeFilter(value)}
      isRegion
    />
  );

  const calendarActionButtons = (
    <FlexRowContainer>
      <FilterToggle />
      <Legend />
      <RefreshButton />
    </FlexRowContainer>
  );

  const calendarScrollArrows = (
    <ScrollCalendarArrows scrollCalendar={scrollCalendar} isMonthly={isMonthly} />
  );

  // TODO: this doesn't really work for the monthly calendar
  // It seems like when you navigate to the page, it resets the calendarType, so it always thinks it's a MA HEA
  // It only really affects the legend and the filters, so I'm leaving it for now
  const { business: department } = decodeEventType(calendarType || '');

  const displayActionButtons =
    calendarType && ['0000', '0001', '0004', '0005', '0100'].includes(calendarType?.slice(0, 4));
  const displayEventTypeFilter = calendarType && department === 'HEA'; // Both CT and MA
  const displayAgentTypeFilter = calendarType && calendarType?.slice(0, 4) === '0004';
  const displayRegionFilter = calendarType !== '000400';
  const displayManagerFilter = calendarType !== '000400' && calendarType !== '000100';

  const calendarInteractionRow = (
    <StyledCalendarInteractionRow>
      <FlexFix>{displayActionButtons && calendarActionButtons}</FlexFix>
      {datePicker}
      <FlexFix>{calendarScrollArrows}</FlexFix>
    </StyledCalendarInteractionRow>
  );
  const filterRow = showFilterBar ? (
    <FlexRowContainer>
      {!isMonthly && displayManagerFilter && <ManagerTrucksFilter />}
      {displayRegionFilter && regionFilter}
      {displayEventTypeFilter && eventTypeFilter}
      {displayAgentTypeFilter && agentTypeFilter}
    </FlexRowContainer>
  ) : null;

  const showFilterRegionOnMobile = displayRegionFilter && showFilterBar;

  useEffect(() => {
    if (!showFilterBar) {
      setRegionFilter(0);
    }
  }, [showFilterBar, setRegionFilter]);

  return (
    <>
      <StyledCalendarControlHeader>
        {calendarInteractionRow}
        {filterRow}
      </StyledCalendarControlHeader>
      {/* Mobile needs drastically different structure/style, so I just built it separate from the rest of the header */}
      {/* TODO: actual mobile styles, maybe a menu to show or hide some of these options to save real estate? */}
      <MobileCalendarControlHeader>
        {datePicker}
        {calendarScrollArrows}
        {showFilterRegionOnMobile && regionFilter}
        {displayActionButtons && calendarActionButtons}
      </MobileCalendarControlHeader>
    </>
  );
};

CalendarControlHeader.propTypes = {
  startDate: PropTypes.instanceOf(moment),
  scrollCalendar: PropTypes.func,
  isMonthly: PropTypes.bool,
  showMonthYearPicker: PropTypes.bool,
  setRegionFilter: PropTypes.func,
  setEventTypeFilter: PropTypes.func,
  setAgentTypeFilter: PropTypes.func,
  regionTypeSelected: PropTypes.number,
  eventTypeSelected: PropTypes.string,
  agentTypeSelected: PropTypes.string,
};

CalendarControlHeader.defaultProps = {
  startDate: moment(),
  scrollCalendar: () => {},
  setRegionFilter: () => {},
  setEventTypeFilter: () => {},
  setAgentTypeFilter: () => {},
  isMonthly: false,
  showMonthYearPicker: false,
  regionTypeSelected: null,
  eventTypeSelected: '',
  agentTypeSelected: '',
};

export default CalendarControlHeader;
