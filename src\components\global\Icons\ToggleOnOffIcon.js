import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { ToggleRight } from '@styled-icons/boxicons-solid/ToggleRight';
import { ToggleLeft } from '@styled-icons/boxicons-solid/ToggleLeft';
import ToolTip from '@components/global/Tooltip/Tooltip';

const ArchiveJobContainer = styled.div`
  width: 50%;
  display: flex;
  flex-direction: row-reverse;
  margin-top: -8px;
  margin-left: 30px;
  margin-right: 0px;
  cursor: pointer;
`;

const ToggleRightStyle = styled(ToggleRight)`
  color: red;
  margin-top: 10px;
`;

const ToggleLeftStyle = styled(ToggleLeft)`
  color: ${({ theme }) => theme.secondary[800]};
  margin-top: 10px;
`;

const Text = styled.p`
  margin-top: 22px;
`;

const ToggleOnOffIcon = ({ toggleOn, hoverTextToOn, hoverTextToOff, onClick, text }) => {
  const [hovering, setHovering] = useState(false);
  return (
    <ArchiveJobContainer
      onMouseEnter={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
      onClick={onClick}
    >
      {toggleOn ? <ToggleRightStyle size="45" /> : <ToggleLeftStyle size="45" />}
      {hovering && <ToolTip text={toggleOn ? hoverTextToOff : hoverTextToOn} />}
      {text && <Text>{text}</Text>}
    </ArchiveJobContainer>
  );
};

ToggleOnOffIcon.propTypes = {
  toggleOn: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  hoverTextToOn: PropTypes.string,
  hoverTextToOff: PropTypes.string,
  text: PropTypes.string,
};

ToggleOnOffIcon.defaultProps = {
  hoverTextToOn: '',
  hoverTextToOff: '',
  text: '',
};

export default ToggleOnOffIcon;
