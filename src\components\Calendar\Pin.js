import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';
import { PinAngle } from '@styled-icons/bootstrap/PinAngle';
import { PinAngleFill } from '@styled-icons/bootstrap/PinAngleFill';

import { preventParentElementClick } from '@utils/functions';
import { pinnedEventSelectorFamily } from '@recoil/event';

const pinIconStyle = css`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  & :hover {
    color: ${({ onSidebar, theme }) => {
      return onSidebar ? theme.secondary[500] : theme.secondary[700];
    }};
  }
`;

const PinEmptyIcon = styled(PinAngle)`
  ${pinIconStyle}
`;
const PinFilledIcon = styled(PinAngleFill)`
  ${pinIconStyle}
`;

const Pin = ({ event }, otherProps) => {
  const { id } = event;
  const [pinnedEvent, setPinnedEvent] = useRecoilState(pinnedEventSelectorFamily(id));

  if (!id) return null;

  const onPinClick = async (e) => {
    preventParentElementClick(e);
    setPinnedEvent(id);
  };

  return (
    <>
      {pinnedEvent ? (
        <PinFilledIcon {...otherProps} onClick={(event) => onPinClick(event)} />
      ) : (
        <PinEmptyIcon {...otherProps} onClick={(event) => onPinClick(event)} />
      )}
    </>
  );
};

Pin.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  onSidebar: PropTypes.bool,
};

Pin.defaultProps = {
  onSidebar: false,
};

export default Pin;
