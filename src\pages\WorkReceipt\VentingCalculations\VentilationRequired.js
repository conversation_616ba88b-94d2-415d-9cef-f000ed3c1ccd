import React, { memo } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { calculateVentMathRound } from '@utils/functions';
import { activeFormState } from '@recoil/dataIntakeForm';
import { useRecoilValue } from 'recoil';

const VentilationRequiredTable = styled.table`
  border-collapse: collapse;
  width: 250px;
  margin-top: 20px;
  height: 80px;
`;

const VentilationRequiredTableHeader = styled.th`
  background-color: #f2f2f2;
  padding: 8px;
  text-align: center;
`;

const VentilationRequiredTableRow = styled.tr`
  &:nth-child(even) {
    background-color: #f2f2f2;
  }
`;

const TableCell = styled.td`
  padding: 8px;
  text-align: left;
`;

const InputField = styled.input`
  width: 80px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
`;

export const VentilationRequired = memo(({ atticSqFt, setFormValues }) => {
  const activeFormIndex = useRecoilValue(activeFormState);
  return (
    <VentilationRequiredTable>
      <thead>
        <VentilationRequiredTableRow>
          <VentilationRequiredTableHeader colSpan="2">
            Ventilation Required
          </VentilationRequiredTableHeader>
        </VentilationRequiredTableRow>
      </thead>
      <tbody>
        <VentilationRequiredTableRow>
          <TableCell>Attic Sq Ft</TableCell>
          <TableCell>
            <InputField
              type="text"
              value={atticSqFt || 0}
              onChange={(e) => setFormValues({ atticSqFt: e.target.value, index: activeFormIndex })}
            />
          </TableCell>
        </VentilationRequiredTableRow>
        <VentilationRequiredTableRow>
          <TableCell>Balanced (1:3000):</TableCell>
          <TableCell>
            <InputField type="text" value={calculateVentMathRound(atticSqFt / 300)} readOnly />
          </TableCell>
        </VentilationRequiredTableRow>
        <VentilationRequiredTableRow>
          <TableCell>HI/LO: (1:150):</TableCell>
          <TableCell>
            <InputField type="text" value={calculateVentMathRound(atticSqFt / 150)} readOnly />
          </TableCell>
        </VentilationRequiredTableRow>
      </tbody>
    </VentilationRequiredTable>
  );
});

VentilationRequired.propTypes = {
  atticSqFt: PropTypes.number,
  setFormValues: PropTypes.func,
};

VentilationRequired.defaultProps = {
  atticSqFt: 0,
  setFormValues: () => {},
};
