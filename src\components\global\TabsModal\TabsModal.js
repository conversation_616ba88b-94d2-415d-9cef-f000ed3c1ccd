import React, { useState, useEffect } from 'react';
import Modal from 'react-bootstrap/Modal';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { StepsProcessButtons } from '@components/global';

const StyledTabs = styled.div`
  margin-top: 20px;
`;
const StyledInfoTab = styled.div`
  font-size: 14px;
  font-weight: 350;
  color: ${({ theme }) => theme.colors.lightGray};
`;
const StyledFooter = styled(Modal.Footer)`
  background-color: #f6f8ff;
  border: none;
`;

const TabsModal = (props) => {
  const { title, tabs, enableTabsClick, handleSubmit, name } = props;
  const [allTabs, setAllTabs] = useState(tabs);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [tabsDataChanged, setTabsDataChanged] = useState([]);
  const [enableSubmit, setEnableSubmit] = useState(false);
  const setModalDisplay = (index) => {
    setActiveTabIndex(index);
  };

  useEffect(() => {
    const shouldSubmit = tabsDataChanged.filter((tab) => tab).length === allTabs.length - 1;
    setEnableSubmit(shouldSubmit);
  }, [allTabs.length, tabsDataChanged]);

  /* The following functions is setting the state and changing the state of this parent component 
   This approch is for maintaining the state across all the tabs 
   Switching between the tabs losts the state so it is necessary to maintain the state in parent component */

  // the dynamic approach to change the parent component state for different tabs
  const dataChanged = (fieldName, index) => {
    const noOfObjectKeys = Object.keys([...allTabs][index][fieldName]).length;
    const noOfObjectValuesFilled = Object.values([...allTabs][index][fieldName]).filter(
      (value) => typeof value === 'boolean' || (value && value.length > 0) || value,
    ).length;
    const allValuesFilled = noOfObjectKeys === noOfObjectValuesFilled;
    const tabsDataFilled = [...tabsDataChanged];
    tabsDataFilled[index] = allValuesFilled;
    setTabsDataChanged(tabsDataFilled);
  };

  // Any change in the child component is to change the state of parent component
  const handleIndividualComponentChange = (eventName, eventValue, fieldName, index) => {
    const allTabsData = [...allTabs];
    allTabsData[index][fieldName][eventName] = eventValue;
    setAllTabs(allTabsData);
    dataChanged(fieldName, index);
  };

  // Setting the state of components in the parent components states which passes these states to childern as props
  const setComponentStates = (fieldName, index, value) => {
    const allTabsData = [...allTabs];
    allTabsData[index][fieldName] = { ...allTabsData[index][fieldName], ...value };
    setAllTabs(allTabsData);
  };

  // render the component to modal body on click of the tab
  const renderActiveComponent = () => {
    const Component = allTabs[activeTabIndex].component;
    const otherTabsData = allTabs.filter((data, index) => {
      return index !== activeTabIndex;
    });
    return (
      <Component
        tabsData={allTabs[activeTabIndex]}
        handleOnChange={handleIndividualComponentChange}
        setComponentStates={setComponentStates}
        tabIndex={activeTabIndex}
        otherTabsData={otherTabsData}
      />
    );
  };

  // Remove Crew Context From This. It wasn't doing anything so I harded false and empty function
  // TODO:  Remove this file if not using
  return (
    <Modal className="tabs-modal-container" show={false} onHide={() => {}}>
      <Modal.Header className="tabs-modal-header" closeButton>
        <Modal.Title className="modal-title-tabs">
          <div className="header-title">{title}</div>
          <StyledInfoTab>{name}</StyledInfoTab>
          <StyledTabs>
            <ul className="nav nav-pills">
              {tabs.map((tab, index) => {
                return (
                  <li key={tab.title}>
                    <button
                      type="button"
                      className={activeTabIndex === index ? 'active-tab tab-button' : 'tab-button'}
                      disabled={!enableTabsClick}
                      onClick={() => setModalDisplay(index)}
                    >
                      {tab.title}
                    </button>
                  </li>
                );
              })}
            </ul>
          </StyledTabs>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="tabs-modal-body">
        {allTabs.length > 0 ? renderActiveComponent() : <p>Loading...</p>}
      </Modal.Body>
      {!enableTabsClick && (
        <StyledFooter>
          <StepsProcessButtons
            setModalDisplay={setModalDisplay}
            activeTabIndex={activeTabIndex}
            handleSubmit={handleSubmit}
            tabsData={allTabs}
            enableSubmit={enableSubmit}
          />
        </StyledFooter>
      )}
    </Modal>
  );
};

TabsModal.propTypes = {
  title: PropTypes.string,
  name: PropTypes.string,
  tabs: PropTypes.arrayOf(PropTypes.shape({})),
  enableTabsClick: PropTypes.bool,
  handleSubmit: PropTypes.func.isRequired,
};

TabsModal.defaultProps = {
  title: '',
  name: '',
  tabs: [{}],
  enableTabsClick: false,
};

export default TabsModal;
