import React from 'react';
import PropTypes from 'prop-types';
import FormInput from './FormInput';

const FormNumberInput = (props) => {
  const { value = 0 } = props;
  return <FormInput min="0" {...props} value={value} type="number" />;
};

FormNumberInput.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};
FormNumberInput.defaultProps = {
  value: 0,
};

export default FormNumberInput;
