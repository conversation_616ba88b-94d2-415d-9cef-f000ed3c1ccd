import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import { openPhoneCall, displayPhoneNumber } from '@utils/functions';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const AuditQCFailForm = ({ record }) => {
  const {
    dealId,
    siteId_Ops_: siteID,
    subject,
    heaQcFail,
    tsHeaFail,
    timeStampHeaPerformed,
    heaQcFailNotes,
    heaInvoiceAmount,
    heaVisitResultDetail,
    heaRevenue,
    phoneNumber,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="siteID" value={siteID} title="Site ID (Ops)" placeholder="" />
          <FormInput
            readOnly
            name="heaVisitResultDetail"
            value={heaVisitResultDetail}
            title="HEA Visit Result Detail"
            placeholder=""
          />
          <FormInput
            readOnly
            name="tsHeaFail"
            value={tsHeaFail}
            title="TS HEA Fail"
            placeholder=""
          />
          <FormInput
            readOnly
            name="timeStampHeaPerformed"
            value={moment(timeStampHeaPerformed).format('MM-DD-YYYY  h:mm A')}
            title="Time Stamp HEA Performed"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
          <FormInput
            readOnly
            name="heaQcFail"
            value={heaQcFail}
            title="HEA QC Fail"
            placeholder=""
          />
          <FormInput
            readOnly
            name="heaRevenue"
            value={`${heaRevenue.length === 0 ? '$0.00' : heaRevenue}`}
            title="HEA Revenue"
            placeholder=""
          />
          <FormInput
            readOnly
            name="heaInvoiceAmount"
            value={`${heaInvoiceAmount.length === 0 ? '$0.00' : heaInvoiceAmount}`}
            title="HEA Invoice Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="phoneNumber"
            value={displayPhoneNumber(phoneNumber)}
            title="Phone Number"
            onClick={() => openPhoneCall(phoneNumber)}
            placeholder="Phone Number"
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="heaQcFailNotes"
        value={heaQcFailNotes}
        title="HEA QC Fail Notes"
        placeholder=""
      />
    </>
  );
};

AuditQCFailForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    siteId_Ops_: PropTypes.string,
    subject: PropTypes.string,
    heaQcFail: PropTypes.string,
    tsHeaFail: PropTypes.string,
    timeStampHeaPerformed: PropTypes.string,
    heaQcFailNotes: PropTypes.string,
    heaInvoiceAmount: PropTypes.string,
    heaVisitResultDetail: PropTypes.string,
    heaRevenue: PropTypes.string,
    phoneNumber: PropTypes.string,
  }),
};

AuditQCFailForm.defaultProps = {
  record: {},
};

export default AuditQCFailForm;
