import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';
import { returnVisits } from '@utils/businessLogic/heaBusinessLogic';
import { getCapHeaRequiredDocs } from '@utils/getRequiredDocs';

const getSlots = (params) => {
  const { program, fuelType, numUnit, sfIds, type } = params;

  const requiredFields = returnVisits.includes(type)
    ? {
        'Salesforce Id': sfIds.dealId,
        ...getMultiFamilyDealIdsRequiredFields(numUnit, sfIds),
      }
    : {
        'Salesforce Id': sfIds.dealId,
        'Primary Fuel Type': fuelType,
        'Number Of Units': numUnit,
        'Program fuel provider': program,
        ...getMultiFamilyDealIdsRequiredFields(numUnit, sfIds),
      };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return [params];
};

const create = (params) => {
  let updatedParams = { ...params };
  const { type } = params;

  const isCap = ['0006'].includes(type.slice(2, 6));
  if (isCap) {
    // cap validation updates lead vendor for mixed income
    updatedParams = capValidation(updatedParams);
    if (!updatedParams) return false;
  }

  const {
    program,
    date,
    fuelType,
    numUnit,
    oids,
    startEndTimes,
    startTime,
    attributes,
    sfIds: { dealId },
    sfIds,
    isInstant,
    notes,
    leadVendor,
    capApprovalLeadVendor,
    lock,
    approvalSoftware,
  } = updatedParams;

  const requiredFields = returnVisits.includes(type)
    ? {
        'Deal ID': sfIds.dealId,
        HES: oids,
        'Start Time': startTime,
      }
    : {
        'Program fuel provider': program,
        'Fuel Type': fuelType,
        Units: numUnit,
        HES: oids,
        'Start Time': startTime,
        Date: date,
        'Deal ID': sfIds.dealId,
        'Lead Vendor': leadVendor,
        ...getMultiFamilyDealIdsRequiredFields(numUnit, sfIds),
      };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  if (![15, 18].includes(dealId.length) || !dealId.startsWith('a0h'))
    return throwError(
      `Invalid Deal Id entered. Please enter a valid Deal Id. Current Deal ID: ${dealId}`,
    );

  const createData = {
    date,
    oids,
    numUnit,
    startTime,
    program,
    type: type || '000000',
    startEndTimes,
    attributes,
    sfIds,
    isInstant,
    notes,
    leadVendor,
    capApprovalLeadVendor,
    fuelType,
    lock,
    approvalSoftware,
  };

  return createData;
};

const update = (params) => {
  const {
    id,
    date,
    associatedEventIds,
    associatedEventsId,
    oids,
    sfIds: { dealId },
    sfIds,
    type,
    numUnit,
    jobLength,
    attributes,
    notes,
    startEndTimes,
    phoneNumber,
    email,
    scheduledBy,
    scheduledDate,
    removedUnits,
    leadVendor,
    capApprovalLeadVendor,
    approvalSoftware,
  } = params;

  const requiredFields = {
    HES: oids,
    'Deal Id': dealId,
    Date: date,
    'Start Time': startEndTimes,
    ...getMultiFamilyDealIdsRequiredFields(numUnit, sfIds),
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  const updateData = {
    id,
    date,
    oids,
    type,
    numUnit,
    sfIds,
    attributes,
    notes,
    jobLength,
    associatedEventIds,
    associatedEventsId,
    startEndTimes,
    phoneNumber,
    email,
    scheduledBy,
    scheduledDate,
    removedUnits,
    leadVendor,
    capApprovalLeadVendor,
    approvalSoftware,
  };

  return updateData;
};

const reschedule = (params) => {
  const { rescheduleReason, date, startTime, oids } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleReason,
    Date: date,
    'Start Time': startTime,
    Agent: oids[0],
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return requiredFields;
};

const rescheduleLater = (params) => {
  const { rescheduleReason } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleReason,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return requiredFields;
};

const returnVisit = (params) => {
  const {
    id,
    returnReason,
    notes: { fieldNotes },
    program,
    date,
    fuelType,
    numUnit,
    oids,
    startEndTimes,
    startTime,
    sfIds: { dealId },
    sfIds,
    type,
    notes,
    removedUnits,
    jobLength,
    associatedEventIds,
  } = params;

  const requiredFields = {
    Notes: fieldNotes,
    Units: numUnit,
    HES: oids,
    'Start Time': startTime,
    Date: date,
    'Deal ID': dealId,
    ...getMultiFamilyDealIdsRequiredFields(numUnit, sfIds),
  };
  if (!id) requiredFields['Return Reason'] = returnReason;

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const returnParams = {
    date,
    oids,
    numUnit,
    startTime,
    program,
    type,
    startEndTimes,
    jobLength,
    sfIds,
    notes,
    returnReason,
    fuelType,
    removedUnits,
    associatedEventIds,
  };

  if (id) returnParams.id = id;

  return returnParams;
};

const capValidation = (params) => {
  const {
    sfIds: { siteId, siteId2, siteId3, siteId4 },
    numUnit,
  } = params;

  const siteIds = [siteId, siteId2, siteId3, siteId4].slice(0, numUnit).map((id) => {
    return id || '';
  });
  const capSiteIds = siteIds.filter((id) => id.includes('CAP'));
  const capPercentage = capSiteIds.length / siteIds.length;
  let isValidForCapCheck = false;
  siteIds.forEach((siteId) => {
    if (siteId?.length || siteId !== '') isValidForCapCheck = true;
  });
  if (!isValidForCapCheck) return params;

  if (capPercentage === 0)
    return throwError(
      'You are trying to schedule a CAP job but there are no units marked with Lead Vendor "CAP" on Salesforce',
    );

  if (capPercentage >= 0.5 && capPercentage < 1)
    return throwError(
      'It looks like this job should qualify for the 50% rule. Please make sure all units are marked with "CAP" as their Lead Vendor and have a valid CAP Site ID in Salesforce',
    );

  if (capPercentage < 0.5) return { ...params, leadVendor: 'Mixed Income' };

  return params;
};

const getMultiFamilyDealIdsRequiredFields = (numUnit, sfIds) => {
  const dealsRequiredFields = {};
  if (numUnit > 1) {
    const { dealId2, dealId3, dealId4 } = sfIds;
    const dealIdsArr = [dealId2, dealId3, dealId4];
    for (let i = 0; i < numUnit - 1; i++)
      dealsRequiredFields[`Salesforce Deal Id ${i + 2}`] = dealIdsArr[i];
  }
  return dealsRequiredFields;
};

const capDocRepoResulting = (params, department) => {
  const {
    visitResult,
    ampRun,
    largeAppliances,
    atticWork,
    knt,
    lta,
    recommendFridge,
    recommendFreezer,
    recommendWasher,
    recommendDehumidifier,
    recommendWindowAcUnit,
    displaceGasHeatWithHeatPump,
    cstFailForGasSystem,
    replaceDelivFuelWithHeatPump,
    recommendedHvac,
    recommendedDhw,
    existingHvac,
    existingDhw,
  } = params;

  let requiredFields = {};
  let requiredDocs = getCapHeaRequiredDocs(params, department);
  requiredDocs = [...new Set(requiredDocs)];

  if (department === 'HEA-CAP') {
    requiredFields = {
      'HEA Visit Result': visitResult,
      AMP: ampRun,
      'Attick Work': atticWork,
      'Knob and Tube': knt,
      'LTA/Waiver': lta,
    };

    if (ampRun === 'yes') requiredFields['Large Appliances'] = largeAppliances;

    if (largeAppliances === 'yes') {
      requiredFields = {
        ...requiredFields,
        'Recommend Fridge': recommendFridge,
        'Recommend Freezer': recommendFreezer,
        'Recommend Washer': recommendWasher,
        'Recommend Dehumidifier': recommendDehumidifier,
        'Recommend Window AC Unit': recommendWindowAcUnit,
      };
    }
  } else {
    requiredFields = {
      'Displacing Gas Heat with Heat Pumps ': displaceGasHeatWithHeatPump,
      'CST fail for a gas system': cstFailForGasSystem,
      'Replace deliverable fuel with Heat Pump': replaceDelivFuelWithHeatPump,
      'Recommended HVAC': recommendedHvac,
      'Recommended DHW': recommendedDhw,
      'Existing HVAC': existingHvac,
      'Existing DHW': existingDhw,
    };
  }

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return { ...params, requiredDocs: requiredDocs.join(',') };
};

const heaInstall = {
  getSlots,
  create,
  rescheduleLater,
  reschedule,
  update,
  returnVisit,
  capDocRepoResulting,
};

export default heaInstall;
