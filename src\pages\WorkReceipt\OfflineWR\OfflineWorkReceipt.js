import React from 'react';
import PropTypes from 'prop-types';
import DataIntakeForm from '@components/DataIntakeForm';
import { useRecoilValue } from 'recoil';
import { formValuesState, activeFormState, activeTabState } from '@recoil/dataIntakeForm';
import { offlineWorkReceiptValuesState } from '@recoil/workreceipt';
import { useTabsAndFieldsForOfflineWR } from './useTabsAndFieldsForOfflineWR';

export const OfflineWorkReceipt = ({ isDealLoaded }) => {
  const formValues = useRecoilValue(formValuesState);
  const activeForm = useRecoilValue(activeFormState);
  const activeTab = useRecoilValue(activeTabState);
  const {
    docRepoStatus = 0,
    hasReturnVisit = false,
    crawlSpaceSpec = '',
    atticSpec = '',
    kwSpec = '',
    miscVentSpec = '',
    wallSpec = '',
    anyPrewxSpec = '',
  } = formValues[activeForm] || {};

  const isBadSpec = [
    crawlSpaceSpec,
    atticSpec,
    kwSpec,
    miscVentSpec,
    wallSpec,
    anyPrewxSpec,
  ].includes('Bad Spec');
  const {
    offlineWorkReceiptMap,
    offlineWorkReceiptFields,
    offlineWorkReceiptTabs,
  } = useTabsAndFieldsForOfflineWR({
    isDealLoaded,
    docRepoStatus,
    hasReturnVisit,
    isBadSpec,
    activeTab,
  });

  return (
    <DataIntakeForm
      map={offlineWorkReceiptMap}
      fields={offlineWorkReceiptFields}
      valuesState={offlineWorkReceiptValuesState}
      tabs={offlineWorkReceiptTabs}
    />
  );
};

OfflineWorkReceipt.defaultProps = {
  isDealLoaded: false,
};

OfflineWorkReceipt.propTypes = {
  isDealLoaded: PropTypes.bool,
};
