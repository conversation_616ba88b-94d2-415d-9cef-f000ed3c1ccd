import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const Text = styled.div`
  font-size: 1.4rem;
  line-height: 1;
  position: relative;
  top: 0.7rem;
  color: ${({ theme }) => theme.secondary[100]};
`;

const Clickable = styled.div`
  border: 1px solid ${({ theme }) => theme.primary[200]};
  background-color: ${({ theme }) => theme.colors.nameIniBg};
  border-radius: 50%;
  height: 3rem;
  text-align: center;
  width: 3rem;
`;

const NameInitialsImage = ({ text }) => {
  return (
    <Clickable>
      <Text>{text}</Text>
    </Clickable>
  );
};

NameInitialsImage.propTypes = {
  text: PropTypes.string,
};

NameInitialsImage.defaultProps = {
  text: '',
};

export default NameInitialsImage;
