import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';

import { ListHeader } from '@components/global';

const DATE_FORMAT = 'dddd, MM/DD/YYYY';

const DateHeader = ({ dateMoment }) => {
  const formattedDate = dateMoment.format(DATE_FORMAT);
  return <ListHeader>{formattedDate}</ListHeader>;
};

DateHeader.propTypes = {
  dateMoment: PropTypes.instanceOf(moment).isRequired,
};

DateHeader.defaultProps = {};

export default DateHeader;
