import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAReturnISMPay = ({ record }) => {
  const { dealId, returnVisitIsmIncentives, operationsName } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="operationsName"
            value={operationsName}
            title="Operation Name"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="returnVisitIsmIncentives"
            value={returnVisitIsmIncentives}
            title="Return Visit ISM Incentive"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HEAReturnISMPay.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    returnVisitIsmIncentives: PropTypes.string,
    operationsName: PropTypes.string,
  }),
};

HEAReturnISMPay.defaultProps = {
  record: {},
};

export default HEAReturnISMPay;
