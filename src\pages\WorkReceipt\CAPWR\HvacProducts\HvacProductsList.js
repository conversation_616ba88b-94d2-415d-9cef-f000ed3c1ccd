import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { EditAlt } from '@styled-icons/boxicons-solid/EditAlt';
import { HorizontalLine } from '@components/global';

const EditIcon = styled(EditAlt)`
  height: 18px;
  color: green;
`;

const DeleteIconStyled = styled(Trash)`
  height: 18px;
  color: green;
`;

const ActionButtons = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  opacity: 1;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  margin-bottom: 8px;
`;

const ProductCard = styled.div`
  position: relative;
  width: 300px;
  border-radius: 2;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
`;

const ProductTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 4px;
  text-align: left;
  font-weight: normal;
  font-size: 0.9em;
  color: #666;
`;

const PriceBadge = styled.div`
  font-size: 24px;
`;

const PriceIcon = styled.div`
  font-size: 12px;
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: #f9f9f9;
  }
`;

const TableCell = styled.td`
  padding: 4px;
  text-align: left;
  font-size: '12px';
`;

const TableActionContainer = styled.td`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ActionItem = styled.td`
  display: flex;
`;

export const HvacProductList = ({ list, handleEdit, handleDelete }) => {
  const [hoveredId, setHoveredId] = React.useState(null);

  const handleActionsVisibilty = (id, leaving) => {
    if (leaving) {
      return setHoveredId(null);
    }
    return setHoveredId(id);
  };

  return (
    <>
      {list?.map(({ record }) => (
        <ProductCard
          key={record?.id}
          onMouseEnter={() => handleActionsVisibilty(record?.id)}
          onMouseLeave={() => handleActionsVisibilty(record?.id, true)}
        >
          <>
            <TableActionContainer>
              <TableCell>{record?.product2?.name}</TableCell>
              <ActionItem>
                <PriceIcon>$</PriceIcon>
                <PriceBadge>{record?.unitPrice}</PriceBadge>
              </ActionItem>
              {hoveredId === record.id && (
                <>
                  <ActionButtons>
                    <ActionButton onClick={() => handleEdit(record)}>
                      <EditIcon />
                    </ActionButton>
                    <ActionButton onClick={() => handleDelete(record?.id, record?.name)}>
                      <DeleteIconStyled />
                    </ActionButton>
                  </ActionButtons>
                </>
              )}
            </TableActionContainer>
            <HorizontalLine />
            <ProductTable>
              <tbody>
                <TableRow>
                  <TableHeader>Manufacturer</TableHeader>
                  <TableCell>{record?.manufacturer}</TableCell>
                </TableRow>
                <TableRow>
                  <TableHeader>Series</TableHeader>
                  <TableCell>{record?.series}</TableCell>
                </TableRow>
                <TableRow>
                  <TableHeader>Inner Model</TableHeader>
                  <TableCell>{record?.indoorModelNumber}</TableCell>
                </TableRow>
                <TableRow>
                  <TableHeader>Outer Model</TableHeader>
                  <TableCell>{record?.outdoorModelNumbers}</TableCell>
                </TableRow>
                <TableRow>
                  <TableHeader>Book</TableHeader>
                  <TableCell>{record?.name}</TableCell>
                </TableRow>
                <TableRow>
                  <TableHeader>System Size</TableHeader>
                  <TableCell>{record?.systemSize}</TableCell>
                </TableRow>
              </tbody>
            </ProductTable>
          </>
        </ProductCard>
      ))}
    </>
  );
};

HvacProductList.defaultProps = {
  list: '',
  handleEdit: () => {},
  handleDelete: () => {},
};

HvacProductList.propTypes = {
  list: PropTypes.arrayOf(
    PropTypes.shape({
      record: PropTypes.shape({
        id: PropTypes.string,
        product2: PropTypes.shape({
          name: PropTypes.string,
        }),
        unitPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        manufacturer: PropTypes.string,
        series: PropTypes.string,
        indoorModelNumber: PropTypes.string,
        outdoorModelNumbers: PropTypes.string,
        name: PropTypes.string,
        systemSize: PropTypes.string,
      }),
    }),
  ),

  handleEdit: PropTypes.func,
  handleDelete: PropTypes.func,
};
