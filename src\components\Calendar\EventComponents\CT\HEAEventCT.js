import React from 'react';
import { useSetRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useTheme } from 'styled-components';

import { getCityAndZipcodeFromAddress, chopString } from '@utils/functions';
import SalesforceManager from '@utils/APIManager/SalesforceManager';

import { selectedEventState } from '@recoil/eventSidebar';

import Event from '@components/Calendar/EventComponents/Event';

const HEAEventCT = ({ event, ...otherProps }) => {
  const theme = useTheme();
  const setSelectedEvent = useSetRecoilState(selectedEventState);

  const {
    type,
    startTime,
    endTime,
    address,
    notes,
    lock,
    squareFeet,
    houseBuilt,
    numUnit,
    sfIds: { accountId, workVisitId },
  } = event;

  // TODO: do we need the customer names on the calendar events at all?
  let { customerName } = event;
  if (!customerName) customerName = 'No customer found';

  const displayTime = `${moment(startTime, 'hh:mm:ss').format('h:mm a')} - ${moment(
    endTime,
    'hh:mm:ss',
  ).format('h:mm a')}`;

  const cityAndZipcode = getCityAndZipcodeFromAddress(address) || 'No Address Found';

  const customerNameString = customerName ? chopString(customerName, 30) : '';

  const squareFeetElement = (
    <>
      {squareFeet || '?'} ft<sup>2</sup>
    </>
  );

  const bodyHeader = (
    <>
      {customerNameString}
      <br />
      {squareFeetElement}, built {houseBuilt || '?'}
    </>
  );

  const bodyText = (
    <>
      {cityAndZipcode}
      {numUnit && numUnit > 1 ? (
        <>
          <br />
          {`${numUnit} units`}
        </>
      ) : (
        ''
      )}
    </>
  );

  const eventColorMap = {
    '010000': theme.colors.eventA, // HEA Visit
    '010001': theme.colors.eventB, // Insulation Quote Visit
    '010003': theme.colors.eventL, // Sealing Service Revisit
    '010005': theme.colors.eventC, // Sealing Services Visit
    '010006': theme.colors.eventG, // HEA + Sealing Services Visit
  };

  const showIncomeEligibleIcon = [
    'Application Sent To Customer',
    'Income Eligible Waiting On Application',
  ].includes(event?.incomeEligibleOrMarketRate);

  const eventColor = eventColorMap[type];

  const syncFromSalesforce = async () => {
    if (!accountId) return false;
    const salesforceInfo = await SalesforceManager.getCTHEAEventInfoWithAccountId(
      [accountId],
      [workVisitId],
    );
    if (!salesforceInfo) return false;

    return setSelectedEvent({ ...event, ...salesforceInfo });
  };

  return (
    <Event
      event={event}
      backgroundColor={eventColor}
      tooltip={notes?.fieldNotes}
      headerText={displayTime}
      bodyHeader={bodyHeader}
      bodyText={bodyText}
      showIncomeEligibleIcon={showIncomeEligibleIcon}
      pinnable
      lockable
      lock={lock}
      onClick={syncFromSalesforce}
      {...otherProps}
    />
  );
};

HEAEventCT.propTypes = {
  event: PropTypes.shape({
    address: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        city: PropTypes.string,
        postalCode: PropTypes.string,
      }),
    ]),
    attributes: PropTypes.arrayOf(PropTypes.string),
    customerName: PropTypes.string,
    date: PropTypes.string,
    endTime: PropTypes.string,
    id: PropTypes.string,
    leadVendor: PropTypes.string,
    lock: PropTypes.bool,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
      fieldNotes: PropTypes.string,
    }),
    sfIds: PropTypes.shape({
      accountId: PropTypes.string,
      workVisitId: PropTypes.string,
    }),
    numUnit: PropTypes.number,
    type: PropTypes.string,
    startTime: PropTypes.string,
    program: PropTypes.string,
    fuelType: PropTypes.string,
    squareFeet: PropTypes.string,
    houseBuilt: PropTypes.string,
    heaConfirmationStatus: PropTypes.string,
    sealingServiceResult: PropTypes.string,
    heaResult: PropTypes.string,
    incomeEligibleOrMarketRate: PropTypes.string,
  }).isRequired,
};

export default HEAEventCT;
