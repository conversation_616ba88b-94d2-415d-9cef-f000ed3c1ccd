/* eslint-disable indent */
import React from 'react';
import PropTypes from 'prop-types';
import { useTheme } from 'styled-components';
import {
  formatCurrency,
  chopString,
  renderStartAndEndTimes,
  getEventColor,
  getCityAndZipcodeFromAddress,
} from '@utils/functions';

import Event from '@components/Calendar/EventComponents/Event';

import { useRecoilValue } from 'recoil';
import { attributesAtom } from '@recoil/admin/agents';

const InsulationInstallEvent = ({ event, ...otherProps }) => {
  const theme = useTheme();
  const {
    address,
    eventTypeName,
    amount,
    notes,
    lock,
    confirmationStatus: confirmation,
    leadVendor,
    wxVisitResult,
    type,
    eventName,
    attributes,
  } = event;

  let { customerName } = event;
  if (!customerName) customerName = 'No customer found';
  const cityAndZipcode = getCityAndZipcodeFromAddress(address);

  const attributesOptions = useRecoilValue(attributesAtom);

  const greenEnergyAttributeId = attributesOptions.find((attr) => attr.key === 'GEP Installs')
    ?.value;
  const homeDepotAttributeId = attributesOptions.find((attr) => attr.key === 'Home Depot')?.value;
  const isGreenEnergyProJob = attributes.includes(greenEnergyAttributeId);
  const isHomeDepotJob = attributes.includes(homeDepotAttributeId);
  const isIncomeEligible = leadVendor === 'CAP';

  let classification = null;
  const isInsulationInstall = type === '000500';
  if (isIncomeEligible && isInsulationInstall) classification = 'Income Eligible';
  if ((isGreenEnergyProJob || isHomeDepotJob) && isInsulationInstall) classification = 'Partner';
  const color = getEventColor(eventTypeName, wxVisitResult, classification, theme);

  const isTruckService = type === '000503';

  return (
    <Event
      event={event}
      backgroundColor={color}
      tooltip={notes.officeNotes}
      headerText={cityAndZipcode || 'No Address Found'}
      bodyHeader={isTruckService ? eventName : chopString(customerName, 30)}
      bodyText={
        isTruckService
          ? [renderStartAndEndTimes(event)]
          : [formatCurrency(amount), renderStartAndEndTimes(event), leadVendor]
      }
      pinnable
      lockable
      confirmation={confirmation}
      lock={lock}
      {...otherProps}
    />
  );
};

InsulationInstallEvent.propTypes = {
  event: PropTypes.shape({
    address: PropTypes.shape({
      city: PropTypes.string,
      postalCode: PropTypes.string,
    }),
    amount: PropTypes.string,
    scheduledBy: PropTypes.string,
    scheduledDate: PropTypes.string,
    customerName: PropTypes.string,
    eventTypeName: PropTypes.string,
    id: PropTypes.string,
    associatedEventsId: PropTypes.string,
    oid: PropTypes.string,
    date: PropTypes.string,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
    }),
    lock: PropTypes.bool,
    confirmationStatus: PropTypes.string,
    leadVendor: PropTypes.string,
    jobStatus: PropTypes.string,
    wxVisitResult: PropTypes.string,
    type: PropTypes.string,
    eventName: PropTypes.string,
    attributes: PropTypes.arrayOf(PropTypes.string),
  }),
};

InsulationInstallEvent.defaultProps = {
  event: {
    address: null,
    customerName: 'No customer name found',
    confirmation: null,
    lock: false,
    leadVendor: 'No Lead Vendor Found',
    attributes: [],
  },
};

export default InsulationInstallEvent;
