import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { BoxArrowUpRight as BoxArrowUpRightIcon } from '@styled-icons/bootstrap/BoxArrowUpRight';
import { FileDoc as FileDocIcon } from '@styled-icons/boxicons-solid/FileDoc';
import { ReceiptCutoff as ReceiptCutoffIcon } from '@styled-icons/bootstrap/ReceiptCutoff';
import { Row, Col } from '@components/global/Form';

import { UtilityManager } from '@utils/APIManager';
import { throwError } from '@utils/EventEmitter';

const BoxArrowUpRightIconStyle = styled(BoxArrowUpRightIcon)`
  height: 20px;
  margin-bottom: 2px;
  padding: 4px;
  border-radius: 2px;
`;

const FileDocIconStyle = styled(FileDocIcon)`
  height: 30px;
  margin-bottom: 5px;
  padding: 4px;
`;

const ReceiptCutoffIconStyle = styled(ReceiptCutoffIcon)`
  height: 30px;
  margin-bottom: 5px;
  padding: 4px;
`;

const Button = styled.button`
  border: none;
  background-color: ${({ theme }) => theme.secondary[100]};
  font-size: 18px;
`;

const InteractiveButtons = (props) => {
  const { dealId, title, isCap } = props;
  const isDocRepo = title === 'Doc Repo';

  const getDocRepoLink = async () => {
    const docRepoDetails = await UtilityManager.getEventInfoUsingDealId(dealId);
    if (Object.keys(docRepoDetails)?.length > 0) {
      const { numUnit, leadVendor, customerName, accountId } = docRepoDetails;
      if (isCap) window.open(`/view-doc-repo/MA/HEA-CAP/${accountId}}`, '_blank');
      else UtilityManager.openDocRepo(dealId, customerName, leadVendor, numUnit);
    } else throwError('Event Not Present on Scheduler.');
  };

  const openWorkReceipt = async () => {
    const docRepoDetails = await UtilityManager.getEventInfoUsingDealId(dealId);
    if (Object.keys(docRepoDetails)?.length > 0) {
      const { type } = docRepoDetails;
      UtilityManager.openOnlineWorkReceipt(dealId, type === '000006');
    } else UtilityManager.openOnlineWorkReceipt(dealId);
  };

  return (
    <Row>
      <Col size={1}>
        <div>
          <Button onClick={isDocRepo ? getDocRepoLink : openWorkReceipt}>
            {isDocRepo ? <FileDocIconStyle /> : <ReceiptCutoffIconStyle />}
            {title} <BoxArrowUpRightIconStyle />
          </Button>
        </div>
      </Col>
    </Row>
  );
};

InteractiveButtons.propTypes = {
  dealId: PropTypes.string,
  title: PropTypes.string,
  isCap: PropTypes.bool,
};

InteractiveButtons.defaultProps = {
  dealId: '',
  title: '',
  isCap: false,
};

export default InteractiveButtons;
