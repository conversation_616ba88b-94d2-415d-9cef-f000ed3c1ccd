import React, { useState, useEffect, Suspense } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue, useRecoilState, useSetRecoilState } from 'recoil';
import styled, { css } from 'styled-components';
import moment from 'moment';

import { PageHeader } from '@pages/Components';
import { RefreshButton } from '@components/global';
import { FormSelect, FormDatePicker } from '@components/global/Form';
import { BookAppointmentButton } from '@components/global/InstallationScheduledCalendar/components';
import Legend from '@components/Calendar/CalendarComponents/Legend';
import FilterToggle from '@components/Calendar/FilterToggle';
import { calendarTypeAtom, activeTabIndexAtom } from '@recoil/app';
import { showFilterBarState } from '@recoil/calendar';
import { allPartnerEventsAtom } from '@recoil/event';
import { selectedPartnerEventState, showSidebarState } from '@recoil/eventSidebar';
import { isAuthorized } from '@utils/AuthUtils';
import { allAgentsFormOptionsState } from '@recoil/agents';
import {
  issStatusOptions,
  columnHeader,
  barrierTypeOptions,
} from '@utils/businessLogic/partnersBusinessLogic';
import { EventsManager } from '@utils/APIManager';
import {
  partnerVisitStatusIcon,
  urlParamsToJson,
  csvDownload,
  formatCsvData,
} from '@utils/functions';
import { throwError } from '@utils/EventEmitter';
import Table from '../../components/global/Table/index';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;
`;

const PartnerContainer = styled.div`
  margin: 0 auto;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  overflow: auto;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const StyledPartnerInteractionRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 10px 0px 0px 10px;
`;

const FlexRowContainer = styled.div`
  display: flex;
  flex-direction: row;
  padding-bottom: 10px;
`;

const FilterContainer = styled(FlexRowContainer)`
  justify-content: space-around;
`;

const FlexFix = styled.div`
  width: 130px;
  overflow: visible;
`;

const Button = styled.button`
  background: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors.eventGreen};
  white-space: nowrap;
  padding: 0px 20px;
  height: 30px;
  margin-right: 10px;
`;

const ClearButton = styled(Button)`
  margin-top: 20px;
`;

const ArchiveExportContainer = styled.div`
  display: flex;
  flex-direction: row;
  margin-left: 10px;
`;

const actionButtonStyle = css`
  background: ${({ isActive, theme }) => {
    return isActive ? theme.colors.eventGreen : theme.secondary[100];
  }};
  color: ${({ isActive, theme }) => {
    return isActive ? theme.secondary[100] : theme.colors.eventGreen;
  }};
`;

const ShowArchiveButtonStyle = styled(Button)`
  ${actionButtonStyle}
`;

const ShowCAPButtonStyle = styled(Button)`
  ${actionButtonStyle}
  margin-top: 20px;
`;

const Partner = (props) => {
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [selectedMinDate, setSelectedMinDate] = useState(null);
  const [selectedMaxDate, setSelectedMaxDate] = useState(null);
  const [selectedPartner, setSelectedPartner] = useState(null);
  const [selectedType, setSelectedType] = useState(null);
  const [showArchiveButtonActive, setShowArchiveButtonActive] = useState(false);
  const [showCAPButtonActive, setShowCAPButtonActive] = useState(false);

  const setCalendarType = useSetRecoilState(calendarTypeAtom);
  const setActiveTab = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const showFilterBar = useRecoilValue(showFilterBarState);
  const [showSidebar, setShowSidebar] = useRecoilState(showSidebarState);
  const setSelectedPartnerEvent = useSetRecoilState(selectedPartnerEventState);
  const [allPartnerEvent, setAllPartnerEvent] = useRecoilState(allPartnerEventsAtom);
  let partnerOptions = useRecoilValue(allAgentsFormOptionsState);
  // The recoil value is oid and the table data contains agent's display name
  // To filter out the value we need to have same value type
  partnerOptions = partnerOptions?.map(({ key }) => {
    return { key, value: key };
  });
  partnerOptions = partnerOptions?.sort((a, b) => {
    return a.key.toLowerCase() < b.key.toLowerCase() ? -1 : 1;
  });

  const canPerformAction = isAuthorized('Scheduler', 'Partners');

  const showClearFilterButton =
    selectedPartner || selectedStatus || selectedMinDate || selectedMaxDate || selectedType;

  useEffect(() => {
    setCalendarType('0088');
    const getEvents = async () => {
      const allPartnerEvent = await loadEvents();
      setPartnerEventByUrlSfId(allPartnerEvent);
    };
    getEvents();
  }, []);

  useEffect(() => {
    const getEvents = async () => {
      const allPartnerEvent = await loadEvents({ archive: showArchiveButtonActive });
      setPartnerEventByUrlSfId(allPartnerEvent);
    };
    if (!showSidebar) getEvents();
  }, [showSidebar]);

  const setPartnerEventByUrlSfId = (allPartnerEvent) => {
    const { location } = props;
    const queryParams = location.search;
    if (!queryParams) return false;

    const { barrierId } = urlParamsToJson(queryParams);
    const events = Object.values(allPartnerEvent);
    const filterEvent = events.filter(({ sfIds }) => {
      return barrierId === sfIds.barrierId;
    });
    if (filterEvent.length > 0) {
      setSelectedPartnerEvent(filterEvent[0]);
      setShowSidebar(true);
      setActiveTab(1);
    }
    return true;
  };

  const loadEvents = async (params = {}) => {
    try {
      const { rows } = await EventsManager.loadPartnerEvents(params);
      if (!params.storageClass) {
        Object.values(rows).forEach(
          ({
            id,
            isCap,
            status,
            partner,
            barrierTypeName,
            accountName,
            siteId,
            dateSentToPartnerFormatted,
            inspectionScheduledDateFormatted,
            notes: { hweNotes, partnerNotes },
            remediationStartDateFormatted,
            remediationEndDateFormatted,
          }) => {
            const iconKey = `${isCap ? 'CAP - ' : ''}${status}`;
            rows[id].tableData = [
              partnerVisitStatusIcon(iconKey),
              partner,
              status,
              barrierTypeName,
              accountName,
              siteId,
              dateSentToPartnerFormatted,
              inspectionScheduledDateFormatted,
              hweNotes,
              partnerNotes,
              remediationStartDateFormatted,
              remediationEndDateFormatted,
            ];
          },
        );
        setAllPartnerEvent(rows);
      }
      return rows;
    } catch (error) {
      throwError('Failed to get Events. Error: Network Error.');
    }
    return [];
  };

  const topButtons = canPerformAction
    ? [<BookAppointmentButton key="BookAppointmentButton" />]
    : [];

  const clearFilter = () => {
    setSelectedStatus(null);
    setSelectedMinDate(null);
    setSelectedMaxDate(null);
    setSelectedPartner(null);
    setSelectedType(null);
    setShowCAPButtonActive(false);
  };

  const handleShowArchiveClick = async () => {
    await loadEvents({ archive: !showArchiveButtonActive });
    setShowArchiveButtonActive(!showArchiveButtonActive);
  };

  const renderTable = () => {
    if (selectedMinDate !== null && !moment(selectedMinDate)?.isValid()) setSelectedMinDate(null);
    if (selectedMaxDate !== null && !moment(selectedMaxDate)?.isValid()) setSelectedMaxDate(null);
    const allRows = Object.values(allPartnerEvent);

    let filteredRows = allRows;

    if (selectedStatus)
      filteredRows = filteredRows.filter(({ status }) => {
        return status === selectedStatus;
      });
    if (selectedMinDate)
      filteredRows = filteredRows.filter(({ dateSentToPartner }) => {
        return moment(dateSentToPartner).isSameOrAfter(moment(selectedMinDate));
      });
    if (selectedMaxDate)
      filteredRows = filteredRows.filter(({ dateSentToPartner }) => {
        return moment(dateSentToPartner).isSameOrBefore(moment(selectedMaxDate));
      });
    if (selectedPartner)
      filteredRows = filteredRows.filter(({ partner }) => {
        return partner === selectedPartner;
      });
    if (selectedType)
      filteredRows = filteredRows.filter(({ type }) => {
        return type === selectedType;
      });
    if (showCAPButtonActive) {
      filteredRows = filteredRows.filter(({ siteId }) => {
        return siteId?.toLowerCase().includes('cap');
      });
    }

    return (
      <Table header={columnHeader} rows={filteredRows} editable onRowClick={onTableRowClick} />
    );
  };

  const onTableRowClick = (id) => {
    const event = allPartnerEvent[id];
    setSelectedPartnerEvent(event);
    setShowSidebar(true);
  };

  const handleExportDataClick = async () => {
    const rows = await loadEvents({ storageClass: 'all' });
    let content = Object.values(rows);
    content = content.sort((a, b) => {
      return new Date(b.scheduledDate) - new Date(a.scheduledDate);
    });
    content = content.map(
      ({
        accountId,
        sfIds: { siteId },
        address: { address = '' },
        amount,
        archive,
        customerName,
        phoneNumber,
        dateSentToPartner,
        displayName: partnerName,
        eventTypeName: type,
        inspectionScheduledDate,
        notes: { hweNotes, partnerNotes },
        paperWorkStatus,
        scheduledBy,
        scheduledDate,
        status,
        remediationStartDate,
        remediationEndDate,
        remediationEstimation,
      }) => {
        return [
          accountId,
          siteId,
          address,
          customerName,
          phoneNumber,
          type,
          status,
          partnerName,
          amount,
          paperWorkStatus,
          archive,
          formatCsvData(hweNotes),
          formatCsvData(partnerNotes),
          dateSentToPartner,
          inspectionScheduledDate,
          remediationStartDate,
          remediationEndDate,
          remediationEstimation,
          scheduledBy,
          scheduledDate,
        ];
      },
    );
    const columnHeader = [
      'Account Id',
      'Site Id',
      'Customer Address',
      'Customer Name',
      'Customer Phone Number',
      'Job Type',
      'Job Status',
      'Partner',
      'Amount',
      'Paper Work Status',
      'Archive',
      'HWE Notes',
      'Partner Notes',
      'Date Sent to Partner',
      'Inspection Schedule Date',
      'Remediation Start Date',
      'Remediation End Date',
      'Remediation Estimation',
      'Scheduled By',
      'scheduled Date',
    ];
    csvDownload(columnHeader, content, 'partners-data');
  };

  return (
    <Suspense fallback={<div>loading...</div>}>
      <Wrapper>
        <PageHeader buttons={topButtons}>Sub Hub</PageHeader>
        <PartnerContainer>
          <StyledPartnerInteractionRow>
            <FlexFix>
              <FlexRowContainer>
                <FilterToggle />
                <Legend />
                {canPerformAction && (
                  <ArchiveExportContainer>
                    <ShowArchiveButtonStyle
                      isActive={showArchiveButtonActive}
                      onClick={handleShowArchiveClick}
                    >
                      Show Archive
                    </ShowArchiveButtonStyle>
                    <Button onClick={handleExportDataClick}>Export Data</Button>
                    <RefreshButton
                      onClick={() => loadEvents({ archive: showArchiveButtonActive })}
                    />
                  </ArchiveExportContainer>
                )}
              </FlexRowContainer>
            </FlexFix>
          </StyledPartnerInteractionRow>
          {showFilterBar && (
            <FilterContainer>
              {canPerformAction && (
                <FormSelect
                  title="Partner:"
                  placeholder="Select Partner"
                  name="selectedPartner"
                  value={selectedPartner}
                  onChange={(e) => setSelectedPartner(e.target.value)}
                  options={partnerOptions}
                />
              )}
              <FormSelect
                title="Status:"
                placeholder="Select Status"
                name="selectedStatus"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                options={issStatusOptions}
              />
              <FormSelect
                title="Type:"
                placeholder="Select Type"
                name="selectedType"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                options={barrierTypeOptions}
              />
              {canPerformAction && (
                <>
                  <FormDatePicker
                    title="Min Date Sent:"
                    name="minDateSentToPartner"
                    value={moment(selectedMinDate)?.isValid() ? selectedMinDate : null}
                    onChange={(date) => setSelectedMinDate(moment(date).startOf('day'))}
                  />
                  <FormDatePicker
                    title="Max Date Sent:"
                    name="maxDateSentToPartner"
                    value={moment(selectedMaxDate)?.isValid() ? selectedMaxDate : null}
                    onChange={(date) => setSelectedMaxDate(moment(date).startOf('day'))}
                  />
                  <ShowCAPButtonStyle
                    isActive={showCAPButtonActive}
                    onClick={() => setShowCAPButtonActive(!showCAPButtonActive)}
                  >
                    Show CAP Jobs
                  </ShowCAPButtonStyle>
                </>
              )}

              {showClearFilterButton && (
                <ClearButton onClick={clearFilter}>Clear Filter</ClearButton>
              )}
            </FilterContainer>
          )}
          {renderTable()}
        </PartnerContainer>
      </Wrapper>
    </Suspense>
  );
};

Partner.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
};

Partner.defaultProps = {
  location: { search: null },
};

export default Partner;
