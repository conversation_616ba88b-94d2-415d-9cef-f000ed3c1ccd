import React from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';
import { isFunctionEmpty } from '@utils/functions';

const StyledCellCornerIndicator = styled.div(
  ({ color, cursorPointer, theme }) => `
    position: absolute;
    bottom: 0;
    right: 0;
    width: 28px;
    height: 20px;
    text-align: center;
    color: black;
    background-color: ${color};
    border: 1px solid ${theme.secondary[400]};
    border-radius: 4px;
    font-size: 12px;
    ${cursorPointer ? 'cursor: pointer;' : ''}
  `,
);

const CellCornerIndicator = ({ value, status, onDoubleClick }) => {
  const theme = useTheme();
  const cursorPointer = !isFunctionEmpty(onDoubleClick);

  const colorMap = {
    pass: theme.colors.dayFilled,
    fail: theme.secondary[300],
  };

  const backgroundColor = colorMap[status] || theme.secondary[300];

  return (
    <StyledCellCornerIndicator
      onDoubleClick={onDoubleClick}
      color={backgroundColor}
      cursorPointer={cursorPointer}
      theme={theme}
    >
      {value}
    </StyledCellCornerIndicator>
  );
};

CellCornerIndicator.propTypes = {
  value: PropTypes.number.isRequired,
  status: PropTypes.oneOf(['pass', 'neutral', 'fail']),
  onDoubleClick: PropTypes.func,
};

CellCornerIndicator.defaultProps = {
  status: 'pass',
  onDoubleClick: () => {},
};

export default CellCornerIndicator;
