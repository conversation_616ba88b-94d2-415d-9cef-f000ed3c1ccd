import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';

import { RightArrow as RightArrowIcon } from '@styled-icons/boxicons-solid/RightArrow';
import { DownArrow as DownArrowIcon } from '@styled-icons/boxicons-solid/DownArrow';
import Clickable from '@components/global/Clickable';
import ThreeVerticalDots from '@components/global/Icons/ThreeVerticalDots';

const ButtonDropDownContainer = styled.div`
  position: relative;
  &:hover {
    background-color: ${({ theme }) => theme.secondary[600]};
    border-radius: 5%;
    color: ${({ theme }) => theme.secondary[100]};
  }
`;
const StyledDropDownButton = styled.div``;
const ButtonContainer = styled.div`
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptopM)} {
    height: 50px;
  }
`;

const Button = styled.div`
  padding: 0px 5px 0px 5px;
  width: 220px;
  height: 32px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.245px;
  color: ${({ theme }) => theme.secondary[500]};
  background-color: ${({ theme }) => theme.secondary[100]};
  :hover {
    background: ${({ theme }) => theme.primary[100]};
    border-radius: 4px;
    color: ${({ theme }) => theme.primary[500]};
  }
`;

const StyledDropDownList = styled.div`
  position: absolute;
  margin-top: ${({ marginTopVal }) => marginTopVal};
  width: 230px;
  background-color: ${({ theme }) => theme.secondary[100]};
  position: absolute;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  right: 20px;
  padding: 5px 3px 5px 3px;
  z-index: 1000;
  box-shadow: 0px 0px 0px ${({ theme }) => theme.colors.greyShadow};, 0px 8px 16px ${({ theme }) =>
  theme.colors.greyShadow};
  ${({ displayListAtBottom }) => {
    return displayListAtBottom ? 'height: 80px; bottom: 50px;' : null;
  }}
`;

const Text = styled.div`
  width: 230px;
  padding: 6px 18px 6px 10px;
  ${({ active }) => {
    return active ? 'font-weight: 700' : null;
  }}
`;

const arrowIconStyle = css`
  height: 12px;
  margin-bottom: 2px;
  margin-left: 5px;
`;

const ItemCloseIcon = styled(RightArrowIcon)`
  ${arrowIconStyle}
`;
const ItemOpenIcon = styled(DownArrowIcon)`
  ${arrowIconStyle}
`;

const DropDownMenu = (props) => {
  const { DropDownIcon, listItems, marginTopVal, displayListAtBottom } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [activeItem, setActiveItem] = useState(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const itemClicked = (item) => {
    return item?.childrens?.length > 0
      ? () =>
          setActiveItem((prevState) => {
            return prevState === item.text ? null : item.text;
          })
      : () => handleOnclickAction(item);
  };

  const handleOnclickAction = (item) => {
    item.onClick(item.value);
    if (item.key === 'deleteDoc') toggleDropdown();
  };

  const dropDownListRender = (
    <StyledDropDownList marginTopVal={marginTopVal} displayListAtBottom={displayListAtBottom}>
      {listItems.map((item) => {
        const isItemActive = activeItem === item.text;
        const haveChildrens = item?.childrens?.length > 0;
        return (
          <ButtonContainer key={item.text}>
            <Button>
              <Clickable onClick={itemClicked(item)}>
                <Text active={isItemActive}>
                  {item.text}
                  {haveChildrens && (isItemActive ? <ItemOpenIcon /> : <ItemCloseIcon />)}
                </Text>
              </Clickable>
            </Button>
            {haveChildrens &&
              item?.childrens?.map(({ text, onClick }) => {
                return isItemActive ? (
                  <Button key={text}>
                    <Clickable onClick={onClick}>
                      <Text>{`  - ${text}`}</Text>
                    </Clickable>
                  </Button>
                ) : null;
              })}
          </ButtonContainer>
        );
      })}
    </StyledDropDownList>
  );

  return (
    <ButtonDropDownContainer>
      <StyledDropDownButton>
        <Clickable className="" onClick={toggleDropdown}>
          {DropDownIcon}
        </Clickable>
      </StyledDropDownButton>
      {isOpen && dropDownListRender}
    </ButtonDropDownContainer>
  );
};

DropDownMenu.propTypes = {
  listItems: PropTypes.arrayOf(PropTypes.shape({})),
  DropDownIcon: PropTypes.node,
  marginTopVal: PropTypes.string,
  displayListAtBottom: PropTypes.bool,
};

DropDownMenu.defaultProps = {
  listItems: [],
  DropDownIcon: <ThreeVerticalDots />,
  marginTopVal: '7px',
  displayListAtBottom: false,
};

export default DropDownMenu;
