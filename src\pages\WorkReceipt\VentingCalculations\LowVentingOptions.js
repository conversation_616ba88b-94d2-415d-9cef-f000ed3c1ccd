import React, { memo } from 'react';
import { uniqueId } from 'lodash';
import PropTypes from 'prop-types';
import { TableCell, TableHeader, TableRow, Table } from './TableCellsColors';
import { ventingOptions } from '../consts';

export const LowVentingOptions = memo(({ atticSqFt, lowVentingTotal }) => {
  const calculateLowVenting = ({ ratio, value, price }) =>
    value || Math.max(Math.ceil(((atticSqFt / 300) * ratio - lowVentingTotal) / price), 0);
  return (
    <Table>
      <thead>
        <TableRow>
          <TableHeader colSpan="6">Low Venting Options</TableHeader>
        </TableRow>
      </thead>
      <thead>
        {/* eslint-disable-next-line react/self-closing-comp */}
        <TableHeader></TableHeader>
        <TableHeader>4 x 16 Soffit</TableHeader>
        <TableHeader>6 x 16 Soffit</TableHeader>
        <TableHeader>8 x 16 Soffit</TableHeader>
        <TableHeader>Low 12 x 18 Gable</TableHeader>
        {/* eslint-disable-next-line react/self-closing-comp */}
        <TableHeader></TableHeader>
      </thead>
      <tbody>
        {ventingOptions.lowVentingOptions.map((option, idx) => (
          <TableRow key={uniqueId()}>
            <TableCell
              whiteSpace="nowrap"
              backgroundColor={option.color}
              fontWeight={idx < 3 ? 900 : 300}
            >
              {option.name}
            </TableCell>
            {option.calcs.map((item) => (
              <TableCell
                whiteSpace="nowrap"
                backgroundColor={item.color}
                fontWeight={idx < 3 ? 900 : 300}
                key={uniqueId()}
              >
                {calculateLowVenting(item)}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </tbody>
    </Table>
  );
});

LowVentingOptions.propTypes = {
  atticSqFt: PropTypes.number,
  lowVentingTotal: PropTypes.number,
};

LowVentingOptions.defaultProps = {
  atticSqFt: 0,
  lowVentingTotal: 0,
};
