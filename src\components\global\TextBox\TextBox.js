import React from 'react';

import PropTypes from 'prop-types';
import styled from 'styled-components';

const StyledInput = styled.input`
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  width: 100%;
  font-size: 1rem;

  transition-property: border-color, box-shadow;
  transition-duration: 0.15s, 0.15s;
  transition-timing-function: ease-in-out, ease-in-out;
  transition-delay: 0s, 0s;
`;

const TextBox = (props) => {
  const { testid } = props;
  return <StyledInput data-testid={testid} {...props} />;
};

export default TextBox;

TextBox.propTypes = {
  testid: PropTypes.string,
};

TextBox.defaultProps = {
  testid: '',
};
