import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

import { useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';

import { PageContainer, PageHeader } from '@pages/Components';

import ScreenPartitionView from '@components/global/ScreenPartitionView/ScreenPartitionView';

import { Filters } from '@components/global/Filters';
import { urlParamsToJson } from '@utils/functions';
import { authorizedDepartmentsSelector, statesSelector } from '@recoil/app';
import { allAuthorizedUsersForUserSelector, selectedUserState } from '@recoil/admin/users/';
import { selectedFiltersState, adminInfoChangesState } from '@recoil/admin/';

import UserList from './UserList';
import UserListItemDetail from './UserListItemDetail';

const EditUserInfo = (props) => {
  const {
    location: { search: urlQueryParams },
  } = props;

  const allUsers = useRecoilValue(allAuthorizedUsersForUserSelector);
  const [filteredUsers, setFilteredUsers] = useState(allUsers);
  const allStates = useRecoilValue(statesSelector);
  const departments = useRecoilValue(authorizedDepartmentsSelector);
  const selectedFilters = useRecoilValue(selectedFiltersState);
  const setSelectedUser = useSetRecoilState(selectedUserState);
  const resetAdminInfoChanges = useResetRecoilState(adminInfoChangesState);

  useEffect(() => {
    resetAdminInfoChanges();
    setUserByUrlOid();
  }, [resetAdminInfoChanges, setUserByUrlOid]);

  useEffect(() => {
    const newFilteredUsers = allUsers.filter((user) => {
      let userMatchesAllFilters = true;

      const filterNames = Object.keys(selectedFilters);

      filterNames.forEach((filterName) => {
        // Handle arrays (user can have multiple departments)
        if (Array.isArray(user[filterName])) {
          if (!user[filterName].includes(selectedFilters[filterName].key))
            userMatchesAllFilters = false;
        }
        // Handle normal filter
        else if (user[filterName] !== selectedFilters[filterName].key)
          userMatchesAllFilters = false;
      });

      return userMatchesAllFilters;
    });

    setFilteredUsers(newFilteredUsers);
  }, [selectedFilters, allUsers]);

  const setUserByUrlOid = useCallback(() => {
    // TODO: this will crash if there are no users. We might want to handle this
    if (!urlQueryParams) return setSelectedUser(allUsers[0]);

    const { oid } = urlParamsToJson(urlQueryParams);
    const newSelectedUser = allUsers.find((user) => (user.oid === oid ? user : false));
    return setSelectedUser(newSelectedUser);
  }, [urlQueryParams, allUsers, setSelectedUser]);

  const filterOptions = [
    // { name: 'Company', options: companies },
    // { name: 'Region', options: regions },
    { displayName: 'Department', name: 'departments', options: departments },
    { displayName: 'State', name: 'state', options: allStates },
  ];

  return (
    <PageContainer>
      <PageHeader filters={<Filters filters={filterOptions} />}>Edit User Info</PageHeader>
      <ScreenPartitionView ratio={[3, 7]}>
        <UserList users={filteredUsers} />
        <UserListItemDetail />
      </ScreenPartitionView>
    </PageContainer>
  );
};

EditUserInfo.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
};

EditUserInfo.defaultProps = {
  location: { search: null },
};

export default EditUserInfo;
