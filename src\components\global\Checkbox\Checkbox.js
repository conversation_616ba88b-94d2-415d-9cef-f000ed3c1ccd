import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import FormFieldContainer from '@components/global/Form/FormFieldContainer';
import { FormFieldLabel } from '../Form';

const StyledCheckbox = styled.input`
  width: 16px;
  height: 16px;
  border: 1px solid ${({ theme }) => theme.secondary[200]};
  border-radius: 2px;
  margin-right: 8px;
  ${({ readOnly }) =>
    readOnly
      ? `
      opacity: 0.5;
      pointer-events: none;
    `
      : ''}
`;

const Checkbox = (props) => {
  const { name, label, onChange, value, required, ...otherProps } = props;

  // JSON.parse will turn string 'true' or 'false' into bool
  // However, it will throw an error if undefined, so we check truthiness here
  const val = value ? JSON.parse(value) : false;

  // Use e.target.value for consistency with other form components
  const handleCheckboxChange = (e) => {
    const synthEvent = {
      target: {
        name,
        value: e.target.checked,
      },
    };
    return onChange(synthEvent);
  };

  return (
    <FormFieldContainer horizontal compact required={required} fieldName={name}>
      <StyledCheckbox
        type="checkbox"
        onChange={handleCheckboxChange}
        checked={val}
        value={val}
        {...otherProps}
      />
      <FormFieldLabel>{label}</FormFieldLabel>
    </FormFieldContainer>
  );
};

Checkbox.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  value: PropTypes.bool,
};

Checkbox.defaultProps = {
  required: false,
  value: false,
};

export default Checkbox;
