import React from 'react';
import PropTypes from 'prop-types';
import Tabs from '@components/global/Tabs/Tabs';
import { activeTabState } from '@recoil/dataIntakeForm';
import { useSetRecoilState } from 'recoil';

const DataIntakeFormNavigation = ({ tabs = [], stepValidation }) => {
  const setActiveTab = useSetRecoilState(activeTabState);
  return (
    <Tabs tabs={tabs} onTabChange={(name) => setActiveTab(name)} stepValidation={stepValidation} />
  );
};

DataIntakeFormNavigation.propTypes = {
  tabs: PropTypes.arrayOf(PropTypes.shape({ title: PropTypes.string })).isRequired,
  stepValidation: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.bool), PropTypes.bool]),
};
DataIntakeFormNavigation.defaultProps = {
  stepValidation: false,
};

export default DataIntakeFormNavigation;
