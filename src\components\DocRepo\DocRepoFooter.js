import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDownload,
  faEye,
  faCheckDouble,
  faExchangeAlt,
  faUpload,
  faLink,
} from '@fortawesome/free-solid-svg-icons';

const StyledFooterDiv = styled.div`
  align-items: center;
  background-color: ${({ theme }) => theme.secondary[200]};
  z-index: 9999;
  position: sticky;
  bottom: 0;
  display: flex;
  margin: 5px -20px -20px 5px;
  background-color: ${({ theme }) => theme.primary[100]};
  flex-wrap: wrap;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    margin: 5px -10px -20px 5px;
  }
`;
const StyledUploadWrapper = styled.div`
  background-color: transparent;
  border: none;
  font-size: 20px;
  font-weight: bold;
  padding-left: 18px;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    padding: 0px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    padding: 0px;
  }
`;
const StyledUploadButtonDiv = styled.div`
  background-color: transparent;
  border: none;
  font-size: 20px;
  font-weight: bold;
  padding-left: 18px;
  position: relative;
  overflow: hidden;
  display: inline-block;
  &:hover {
    cursor: pointer;
  }
  input[type='file'] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;

    &:hover {
      cursor: pointer;
    }
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    padding: 0px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    padding: 0px;
  }
`;
const StyledButton = styled.button`
  border: 2px solid ${({ theme }) => theme.primary[400]};
  color: $primary-400;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 10px;
  margin: 10px 20px 10px 0;
  &:hover {
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 11px;
    width: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 58px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 16px;
    padding: 0px;
    height: 50px;
  }
`;
const StyledInput = styled.input`
  width: -webkit-fill-available;
`;
const StyledUploadButton = styled.button`
  border: ${({ theme }) => theme.primary[400]};
  color: $primary-400;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 10px;
  margin: 10px 20px 10px 0;
  position: relative;
  overflow: hidden;
  display: inline-block;
  &:hover {
    cursor: pointer;
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
  }
  input[type='file'] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    &:hover {
      cursor: pointer;
    }
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 11px;
    width: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 58px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 16px;
    padding: 0px;
    height: 50px;
  }
`;

const DocRepoFooterForButtons = (props) => {
  const {
    handleUpload,
    handleCheckAllButton,
    handleViewAllButton,
    handleDownloadAllButton,
    updateRenderStatus,
    openSfLink,
    showResultingButton,
  } = props;

  return (
    <StyledFooterDiv>
      <StyledUploadWrapper>
        <StyledUploadButton type="button" onClick={handleUpload}>
          <FontAwesomeIcon icon={faUpload} />
          &nbsp;Upload Files
          <StyledInput type="file" name="file" id="file" onChange={handleUpload} multiple />
        </StyledUploadButton>
      </StyledUploadWrapper>
      <StyledUploadButtonDiv>
        {showResultingButton && (
          <StyledButton type="button" onClick={() => updateRenderStatus('firstTimeHere')}>
            <FontAwesomeIcon icon={faExchangeAlt} />
            &nbsp;Resulting
          </StyledButton>
        )}
      </StyledUploadButtonDiv>
      <StyledUploadButtonDiv>
        <StyledButton type="button" onClick={handleCheckAllButton}>
          <FontAwesomeIcon icon={faCheckDouble} />
          &nbsp;Check All
        </StyledButton>
      </StyledUploadButtonDiv>
      <StyledUploadButtonDiv>
        <StyledButton type="button" onClick={handleViewAllButton}>
          <FontAwesomeIcon icon={faEye} />
          &nbsp;View All
        </StyledButton>
      </StyledUploadButtonDiv>
      <StyledUploadButtonDiv>
        <StyledButton type="button" onClick={handleDownloadAllButton}>
          <FontAwesomeIcon icon={faDownload} />
          &nbsp;Download All
        </StyledButton>
      </StyledUploadButtonDiv>
      <StyledUploadButtonDiv>
        <StyledButton type="button" onClick={openSfLink}>
          <FontAwesomeIcon icon={faLink} />
          &nbsp;Salesforce
        </StyledButton>
      </StyledUploadButtonDiv>
    </StyledFooterDiv>
  );
};

DocRepoFooterForButtons.propTypes = {
  handleUpload: PropTypes.func.isRequired,
  handleCheckAllButton: PropTypes.func.isRequired,
  handleViewAllButton: PropTypes.func.isRequired,
  handleDownloadAllButton: PropTypes.func.isRequired,
  updateRenderStatus: PropTypes.func.isRequired,
  openSfLink: PropTypes.func.isRequired,
  showResultingButton: PropTypes.bool.isRequired,
};

export default DocRepoFooterForButtons;
