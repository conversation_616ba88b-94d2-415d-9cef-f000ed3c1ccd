import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { formatPhoneNumber, isFunctionEmpty } from '@utils/functions';

import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import Clickable from '../Clickable';

const FormInputPhoneNumberContainer = styled.div`
  display: flex;
  align-items: center;
  height: 32px;
`;

const FormInputPhoneNumberContent = styled.input`
  cursor: ${({ onClick }) => {
    return isFunctionEmpty(onClick) ? '' : 'pointer';
  }};
  width: 100%;
  min-height: 32px;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  ${({ allowCopy }) => (allowCopy ? 'border-right: none;' : '')}
  box-sizing: border-box;
  border-radius: 4px ${({ allowCopy }) => (allowCopy ? '0px 0px' : '4px 4px')} 4px;
`;

const StyledBottomActionButton = styled(Clickable)`
  color: blue;
  text-decoration: underline;
`;

const FormInputPhoneNumber = (props) => {
  const {
    title,
    titleAction,
    bottomAction,
    placeholder,
    readOnly,
    required,
    name,
    onChange,
    type,
    onClick,
    testid,
    compact,
  } = props;

  let { value } = props;
  if (value == null) value = '';

  const removeAllSymbols = /^\+1|[ \-()+]/g;
  const onlyNumericalValues = /^\d*$/;
  let BottomActionButton = null;
  if (bottomAction) {
    const { label, action } = bottomAction;
    BottomActionButton = (
      <StyledBottomActionButton onClick={() => action()}>{label}</StyledBottomActionButton>
    );
  }

  const handleChange = (event) => {
    const inputPhoneNumber = event.target.value;
    if (inputPhoneNumber.length > 19) return;
    // FormatPhoneNumber is responsible to add +1 infront of number so users can add phonenumber without adding +1.
    // +1 will be only rendered onscreen, which setting value on setter it will be removed.
    // for example +1 (444)- 555 7777
    // setter will store it as 4445557777
    const formattedPhoneNumber = formatPhoneNumber(inputPhoneNumber);
    const cleanedPhoneNumber = formattedPhoneNumber.replace(removeAllSymbols, '');
    if (!onlyNumericalValues.test(cleanedPhoneNumber)) {
      return;
    }
    onChange({ target: { value: cleanedPhoneNumber, name } });
  };

  return (
    <FormFieldContainer required={required} fieldName={name} compact={compact}>
      <FormFieldLabel titleAction={titleAction}>{title}</FormFieldLabel>
      <FormInputPhoneNumberContainer readOnly={readOnly}>
        <FormInputPhoneNumberContent
          data-testid={testid}
          readOnly={readOnly || false}
          placeholder={placeholder}
          name={name}
          value={formatPhoneNumber(value)}
          onChange={handleChange}
          type={type}
          onClick={onClick}
        />
      </FormInputPhoneNumberContainer>
      {BottomActionButton}
    </FormFieldContainer>
  );
};

FormInputPhoneNumber.propTypes = {
  title: PropTypes.string.isRequired,
  titleAction: PropTypes.shape({
    action: PropTypes.func,
    label: PropTypes.string,
  }),
  bottomAction: PropTypes.shape({
    action: PropTypes.func,
    label: PropTypes.string,
  }),
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  type: PropTypes.string,
  onClick: PropTypes.func,
  testid: PropTypes.string,
  compact: PropTypes.bool,
};

FormInputPhoneNumber.defaultProps = {
  placeholder: '',
  readOnly: false,
  required: false,
  onChange: () => {},
  onClick: () => {},
  type: 'text',
  value: '',
  compact: false,
  testid: '',
  titleAction: null,
  bottomAction: null,
};

export default FormInputPhoneNumber;
