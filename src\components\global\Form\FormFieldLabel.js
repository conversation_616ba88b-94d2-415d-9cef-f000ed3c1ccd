import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { QuestionCircleFill } from '@styled-icons/bootstrap/QuestionCircleFill';
import Clickable from '../Clickable';

const FormFieldLabelContainer = styled.div`
  display: flex;
  justify-content: space-between;
  ${({ horizontal }) => {
    return horizontal ? 'width: 50%' : '';
  }};
`;

const StyledFormFieldLabel = styled.label`
  font-style: normal;
  font-weight: 500;
  font-size: ${({ lgFont }) => (lgFont ? '16px' : '12px')};
  line-height: ${({ lineHeight }) => (lineHeight ? '20px' : '16px')};
  margin-bottom: ${({ fieldType }) => (fieldType === 'readonly' ? '0px' : '5px')};
  text-transform: ${({ theme }) => theme.textTransform};
  color: ${({ theme }) => theme.color};
`;

const StyledTitleActionButton = styled(Clickable)`
  color: red;
  text-decoration: underline;
`;

const QuestionIconStyled = styled(QuestionCircleFill)`
  height: 12px;
  width: 20px;
  cursor: pointer;
`;

const FormFieldLabelSubContainer = styled.div``;
const TooltipContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const TooltipText = styled.div`
  position: absolute;
  color: white;
  padding: 6px;
  background-color: black;
  width: 250px;
  border-radius: 4px;
  font-size: 14px;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  top: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;

  ${TooltipContainer}:hover & {
    visibility: visible;
    opacity: 1;
  }
`;

const FormFieldLabel = (props) => {
  const { children, titleAction, fieldType, lgFont, lineHeight, description, horizontal } = props;

  let TitleActionButton = null;
  if (titleAction) {
    const { label, action } = titleAction;
    TitleActionButton = (
      <StyledTitleActionButton onClick={() => action()}>{label}</StyledTitleActionButton>
    );
  }

  return (
    <FormFieldLabelContainer horizontal={horizontal}>
      <FormFieldLabelSubContainer>
        <StyledFormFieldLabel fieldType={fieldType} lgFont={lgFont} lineHeight={lineHeight}>
          {children}
        </StyledFormFieldLabel>
        {description && (
          <TooltipContainer>
            <QuestionIconStyled />
            <TooltipText>{description}</TooltipText>
          </TooltipContainer>
        )}
      </FormFieldLabelSubContainer>
      {TitleActionButton}
    </FormFieldLabelContainer>
  );
};

FormFieldLabel.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]).isRequired,
  titleAction: PropTypes.oneOfType([
    PropTypes.shape({
      action: PropTypes.func,
      label: PropTypes.string,
    }),
    PropTypes.bool,
  ]),
  fieldType: PropTypes.string,
  lgFont: PropTypes.bool,
  lineHeight: PropTypes.bool,
  description: PropTypes.string,
  horizontal: PropTypes.bool,
};
FormFieldLabel.defaultProps = {
  titleAction: null,
  fieldType: null,
  lgFont: false,
  lineHeight: false,
  description: '',
  horizontal: false,
};

export default FormFieldLabel;
