import React, { useEffect, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';
import { isFunctionEmpty } from '@utils/functions';
import CopyText from '@components/global/Icons/CopyText';
import { Lock as LockIcon } from '@styled-icons/boxicons-solid/Lock';
import { LockOpen as LockOpenIcon } from '@styled-icons/boxicons-solid/LockOpen';
import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';

const lockIconStyle = `
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
`;

const FormCampaignIDContainer = styled.div`
  display: flex;
  align-items: center;
  height: 32px;
`;

const FormCampaignIDContent = styled.input`
  cursor: ${({ onClick }) => {
    return isFunctionEmpty(onClick) ? '' : 'pointer';
  }};
  width: 100%;
  min-height: 32px;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  ${({ allowCopy }) => (allowCopy ? 'border-right: none;' : '')}
  box-sizing: border-box;
  border-radius: 4px ${({ allowCopy }) => (allowCopy ? '0px 0px' : '4px 4px')} 4px;
`;

const LockedIcon = styled(LockIcon)`
  ${lockIconStyle}
  color: ${({ theme }) => theme.colors.red};
  width: 24px;
  height: 24px;

`;
const UnlockedIcon = styled(LockOpenIcon)`
  ${lockIconStyle}
  color: ${({ theme }) => theme.colors.eventGreen};
  width: 24px;
  height: 24px;
`;

const FormCampaignID = (props) => {
  const {
    title,
    placeholder,
    readOnly,
    required,
    name,
    onChange,
    type,
    onClick,
    testId,
    allowCopy,
    copyTextValue,
    compact,
  } = props;
  const theme = useTheme();
  const [isFieldLocked, setIsFieldLocked] = useState(true);
  let { value } = props;
  if (value == null) value = '';

  const handleChange = async (e) => {
    // Remove any non-numeric characters from the input value
    e.target.value = e.target.value.replace(/\D/g, '');
    // Add dashes at the desired positions like 4213-12345
    if (e.target.value.length > 4) {
      e.target.value = `${e.target.value.slice(0, 4)}-${e.target.value.slice(4)}`;
    }
    if (e.target.value.length === 10) setIsFieldLocked(true);

    onChange(e);
  };

  const handleLock = () => setIsFieldLocked(!isFieldLocked);

  useEffect(() => {
    const isValueEmpty = value === '';
    if (isValueEmpty) {
      setIsFieldLocked(false);
    }
  }, [value]);

  return (
    <FormFieldContainer required={required} fieldName={name} compact={compact}>
      <FormFieldLabel>{title}</FormFieldLabel>
      <FormCampaignIDContainer readOnly={readOnly}>
        <FormCampaignIDContent
          data-testid={testId}
          readOnly={readOnly || isFieldLocked}
          placeholder={placeholder}
          name={name}
          value={value}
          onChange={handleChange}
          type={type}
          onClick={onClick}
          maxLength={10}
          allowCopy={allowCopy}
        />
        {allowCopy && (
          <CopyText
            isOnInput
            backgroundColor={readOnly ? theme.secondary[200] : theme.secondary[100]}
            copyTextValue={copyTextValue || value || ''}
          />
        )}
        {isFieldLocked ? (
          <LockedIcon onClick={handleLock} />
        ) : (
          <UnlockedIcon onClick={handleLock} />
        )}
      </FormCampaignIDContainer>
    </FormFieldContainer>
  );
};

FormCampaignID.propTypes = {
  title: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  type: PropTypes.string,
  onClick: PropTypes.func,
  testId: PropTypes.string,
  compact: PropTypes.bool,
  allowCopy: PropTypes.bool,
  copyTextValue: PropTypes.string,
};

FormCampaignID.defaultProps = {
  placeholder: '',
  readOnly: false,
  required: false,
  onChange: () => {},
  onClick: () => {},
  type: 'text',
  value: '',
  compact: false,
  testId: '',
  allowCopy: true,
  copyTextValue: null,
};

export default FormCampaignID;
