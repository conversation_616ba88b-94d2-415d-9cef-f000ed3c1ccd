import React from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';

import { Row } from '@components/global/Form';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import { Header, Checkbox, DropDownMenu } from '@components/global';
import DocRepoIcon from '@components/global/Icons/DocRepoIcon';

import {
  UploadIconStyled,
  DownloadIconStyled,
  ViewIconStyled,
  UploadButtonStyle,
  DownloadButtonStyle,
  ViewAllButtonStyle,
  UploadFileInputStyle,
  EmailIconStyled,
  MergeFileIconStyled,
  ActionsIconStyled,
  renderDocumentActionContainer,
} from './DocRepoButtons';

const DocumentContainer = styled.div`
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptopM)} {
    height: 100px;
  }
`;
const Divider = styled.hr``;
const DocDetails = styled.div`
  font-size: 12px;
  color: ${({ docUploaded, theme }) => {
    return docUploaded ? theme.secondary[400] : theme.colors.red;
  }};
  padding: 4px 10px;
`;
const SelectAllDocumentsCheckboxContainer = styled.div`
  margin-bottom: 15px;
  font-weight: 600;
`;

const DocRepoBody = ({
  documents,
  s3Params,
  handleDocsSelection,
  viewDocument,
  handleUploadAllButton,
  handleDownloadAllButton,
  handleViewAllButton,
  handleEmailDocsButton,
  handleMergeSelectedDocssButton,
  setDocuments,
  docsSelected,
  resulting,
  setResulting,
  canMergePdfFiles,
  allDocsSelected,
  handleSelectAll,
}) => {
  const theme = useTheme();
  const dropDownList = [
    {
      text: (
        <>
          <EmailIconStyled /> Email Docs to Customer
        </>
      ),
      onClick: () => handleEmailDocsButton(),
    },
  ];
  if (canMergePdfFiles)
    dropDownList.push({
      text: (
        <>
          <MergeFileIconStyled /> Merge Selected Docs
        </>
      ),
      onClick: () => handleMergeSelectedDocssButton(),
    });
  const documentsArr = Array.isArray(documents)
    ? documents
    : Object.keys(documents).map((doc) => {
        return documents[doc];
      });
  const isDocumentUploaded = documentsArr.find(({ uploaded }) => {
    return uploaded;
  });
  return (
    <>
      <EventSidebarBody>
        {isDocumentUploaded && (
          <SelectAllDocumentsCheckboxContainer>
            <Checkbox
              label="Select All Documents"
              name="allDocsSelected"
              value={allDocsSelected}
              onChange={handleSelectAll}
            />
          </SelectAllDocumentsCheckboxContainer>
        )}
        {documentsArr
          .sort((a, b) => {
            return a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1;
          })
          .map((document) => {
            const docUploaded = document.uploadDate && document.uploadedBy;
            return (
              <DocumentContainer>
                <Row>
                  {document.uploaded ? (
                    <Checkbox
                      label={document.name}
                      name={document.fileName}
                      value={document.checked}
                      onChange={handleDocsSelection}
                      style={document.sensitive ? { display: 'none' } : null}
                    />
                  ) : (
                    <Header h4 color={theme.colors.red}>
                      {document.name}
                    </Header>
                  )}
                  <DocRepoIcon
                    doc={document}
                    openFile={() => {
                      viewDocument(document);
                    }}
                  />

                  <DocDetails docUploaded={docUploaded}>
                    {docUploaded
                      ? `Uploaded by ${document.uploadedBy} on ${document.uploadDate}.`
                      : 'Missing Document'}
                  </DocDetails>

                  {renderDocumentActionContainer(
                    { ...s3Params, document },
                    setDocuments,
                    resulting,
                    setResulting,
                  )}
                </Row>
                <Divider />
              </DocumentContainer>
            );
          })}
      </EventSidebarBody>
      <EventSidebarFooter
        rightButtons={
          docsSelected ? (
            <DropDownMenu
              listItems={dropDownList}
              DropDownIcon={<ActionsIconStyled />}
              displayListAtBottom
            />
          ) : null
        }
        $width="82%"
      >
        <UploadButtonStyle right onClick={handleUploadAllButton}>
          <UploadIconStyled />
          Upload Documents
          <UploadFileInputStyle
            type="file"
            name="file"
            id="fileInput"
            onChange={handleUploadAllButton}
            multiple
          />
        </UploadButtonStyle>
        <DownloadButtonStyle right onClick={handleDownloadAllButton} disabled={!docsSelected}>
          <DownloadIconStyled />
          Download Documents
        </DownloadButtonStyle>
        <ViewAllButtonStyle right onClick={handleViewAllButton} disabled={!docsSelected}>
          <ViewIconStyled />
          View Documents
        </ViewAllButtonStyle>
      </EventSidebarFooter>
    </>
  );
};

DocRepoBody.propTypes = {
  documents: PropTypes.oneOfType([
    PropTypes.objectOf(PropTypes.string),
    PropTypes.arrayOf(PropTypes.objectOf(PropTypes.string)),
  ]).isRequired,
  s3Params: PropTypes.objectOf(PropTypes.string).isRequired,
  handleDocsSelection: PropTypes.func.isRequired,
  viewDocument: PropTypes.func.isRequired,
  handleUploadAllButton: PropTypes.func.isRequired,
  handleDownloadAllButton: PropTypes.func.isRequired,
  handleViewAllButton: PropTypes.func.isRequired,
  handleEmailDocsButton: PropTypes.func.isRequired,
  handleMergeSelectedDocssButton: PropTypes.func.isRequired,
  setDocuments: PropTypes.func.isRequired,
  docsSelected: PropTypes.bool.isRequired,
  resulting: PropTypes.objectOf(PropTypes.string),
  setResulting: PropTypes.func,
  canMergePdfFiles: PropTypes.bool,
  handleSelectAll: PropTypes.func,
  allDocsSelected: PropTypes.bool,
};

DocRepoBody.defaultProps = {
  resulting: null,
  setResulting: null,
  canMergePdfFiles: false,
  handleSelectAll: () => {},
  allDocsSelected: false,
};

export default DocRepoBody;
