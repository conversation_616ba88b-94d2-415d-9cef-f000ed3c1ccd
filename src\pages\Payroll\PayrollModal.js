import React from 'react';
import PropTypes from 'prop-types';
import Modal from 'react-bootstrap/Modal';
import styled from 'styled-components';
import { InfoCircle } from '@styled-icons/boxicons-solid/InfoCircle';

const InfoCircleStyled = styled(InfoCircle)``;

const styleDict = {
  Error: 'error',
  Pass: 'pass',
  Warning: 'warning',
};

const StyledDivContainer = styled.div``;
const StyledULContainer = styled.ul``;
const StyledLIContainer = styled.li``;
const StyledTRContainer = styled.tr``;
const StyledTDContainer = styled.td``;
const StyledTHContainer = styled.th``;
const StyledTableContainer = styled.table``;
const StyledTHeadContainer = styled.thead``;
const StyledTBodyContainer = styled.tbody``;
const InfoSpan = styled.div`
  position: relative;
  display: inline-block;
  padding-left: 5px;
`;
const InfoModalTextSpan = styled.span`
  visibility: hidden;
  width: 350px;
  background-color: #fff;
  color: black;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;
  filter: drop-shadow(0px 4px 12px rgba(0, 0, 0, 0.08));

  /* Position the tooltip text */
  position: absolute;
  z-index: 1;
  left: 50%;
  margin-left: -174px;
  &::after {
    content: '';
    position: absolute;
    left: 50%;
    margin-left: -5px;
    top: 100%;
    border-width: 5px;
    border-style: solid;
    border-color: white transparent transparent transparent;
  }
  &.top-view {
    bottom: 125%;
  }
  &.top-view::after {
    margin-left: -5px;
    top: 100%;
    bottom: auto;
    border-color: white transparent transparent transparent;
  }
  &.bottom-view {
    top: 125%;
  }
  &.bottom-view::after {
    margin-left: -4px;
    bottom: 100%;
    top: auto;
    border-color: transparent transparent white transparent;
  }
`;
const InfoModel = styled.div`
  position: relative;
  display: inline-block;
  padding-left: 5px;
  &:hover ${InfoModalTextSpan} {
    visibility: visible;
    opacity: 1;
  }
`;
const ModalSpan = styled.span`
  font-weight: bold;
  padding-left: 20px;
`;
const SpanUnderline = styled.span`
  font-weight: bold;
  text-decoration: underline;
`;
const AlignLiTextLeft = styled.li`
  text-align: left;
`;
const StyledUl = styled.ul`
  font-weight: bold;
  padding-left: 60px;
`;
const StlyedSpanWithClass = styled.span`
  &.error {
    color: #f24537;
  }
  &.pass {
    color: #26c281;
  }
  &.warning {
    color: #a39a21;
  }
`;
const StyledHeadIcon = styled(InfoCircleStyled)`
  color: #212529;
`;
const StyledModal = styled(Modal)`
  overflow: visible !important;
`;
const StyleModalBody = styled(Modal.Body)`
  background-color: #ebebeb !important;
`;

const PayrollModal = (props) => {
  const {
    show,
    header,
    tableHeaders,
    duplicateIdStatus,
    currentStatus,
    employeesWithErrors,
    handleModalOpenClose,
    headerInfo,
  } = props;

  const {
    missing_headers: missingHeaders,
    status: headerStatus,
    wrong_headers: wrongHeaders,
  } = headerInfo;

  // Render QC information for employees returned
  const renderQcInfo = () => {
    const empInfo = [];
    const sortedEmpInfo = employeesWithErrors.sort(
      (a, b) => a.similarity_ratio - b.similarity_ratio,
    );
    for (let k = 0; k < sortedEmpInfo.length; k++) {
      const emp = employeesWithErrors[k];
      const {
        employee_id: empId,
        paychex_name: paychexName,
        similarity_ratio: simRatio,
        name,
        warning_details: warningDetails,
        warning_codes: warningCodes,
        error_details: errorDetails,
        error_codes: errorCodes,
      } = emp;
      empInfo.push(
        <StyledTRContainer key={`${header}_${name}`}>
          <StyledTDContainer>{empId}</StyledTDContainer>
          <StyledTDContainer>{paychexName}</StyledTDContainer>
          <StyledTDContainer>{simRatio}</StyledTDContainer>
          <StyledTDContainer>{name}</StyledTDContainer>
          <StyledTDContainer>
            {errorCodes.join(', ')}
            {errorDetails.length > 0 && (
              <InfoModel>
                <InfoCircleStyled height={16} width={16} />
                <InfoModalTextSpan className="top-view">
                  <SpanUnderline>Errors</SpanUnderline>
                  <StyledULContainer>
                    {errorDetails.map((detail) => {
                      const detailSplit = detail.split(':');
                      return (
                        <AlignLiTextLeft key={detail}>
                          <SpanUnderline>{detailSplit[0]}</SpanUnderline>
                          {`: ${detailSplit[1]}`}
                        </AlignLiTextLeft>
                      );
                    })}
                  </StyledULContainer>
                </InfoModalTextSpan>
              </InfoModel>
            )}
          </StyledTDContainer>
          <StyledTDContainer>
            {warningCodes.join(', ')}
            {warningDetails.length > 0 && (
              <InfoSpan>
                <InfoCircleStyled height={16} width={16} />
                <InfoModalTextSpan className="top-view">
                  <SpanUnderline>Warnings</SpanUnderline>
                  <StyledULContainer>
                    {warningDetails.map((detail) => {
                      const detailSplit = detail.split(':');
                      return (
                        <AlignLiTextLeft key={detail}>
                          <SpanUnderline>{detailSplit[0]}</SpanUnderline>
                          {`: ${detailSplit[1]}`}
                        </AlignLiTextLeft>
                      );
                    })}
                  </StyledULContainer>
                </InfoModalTextSpan>
              </InfoSpan>
            )}
          </StyledTDContainer>
        </StyledTRContainer>,
      );
    }
    return empInfo;
  };

  return (
    <StyledModal
      show={show}
      onHide={() => handleModalOpenClose(null, false)}
      size="xl"
      contentClassName="payroll-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title>
          <StyledDivContainer>{header}</StyledDivContainer>
        </Modal.Title>
      </Modal.Header>
      <StyleModalBody>
        <StyledDivContainer>
          <ModalSpan>
            Please review and correct the following errors and warnings then remove and re-upload
            the file.
          </ModalSpan>
          <br />
          <ModalSpan>Errors must be fixed before files can be combined.</ModalSpan>
          <br />
          <ModalSpan>
            Warnings do not need to be fixed but it is recommended that they are double checked
            before combining.
          </ModalSpan>
        </StyledDivContainer>
        <StyledUl>
          <StyledLIContainer>
            Status:{' '}
            <StlyedSpanWithClass className={styleDict[currentStatus]}>
              {currentStatus}
            </StlyedSpanWithClass>
          </StyledLIContainer>
          <StyledULContainer>
            <StyledLIContainer>
              Headers:{' '}
              <StlyedSpanWithClass className={styleDict[headerStatus]}>
                {headerStatus}
                {headerStatus && headerStatus !== 'Pass' && (
                  <InfoModel>
                    <StyledHeadIcon height={16} width={16} />
                    <InfoModalTextSpan className="bottom-view">
                      {missingHeaders.length > 0 && (
                        <StyledDivContainer>
                          <SpanUnderline>Missing Headers</SpanUnderline>
                          <StyledULContainer>
                            {missingHeaders.map((header) => {
                              return <AlignLiTextLeft key={header}>{header}</AlignLiTextLeft>;
                            })}
                          </StyledULContainer>
                        </StyledDivContainer>
                      )}
                      {wrongHeaders.length > 0 && (
                        <StyledDivContainer>
                          <SpanUnderline>Wrong Headers</SpanUnderline>
                          <StyledULContainer>
                            {wrongHeaders.map((header) => {
                              return <AlignLiTextLeft key={header}>{header}</AlignLiTextLeft>;
                            })}
                          </StyledULContainer>
                        </StyledDivContainer>
                      )}
                    </InfoModalTextSpan>
                  </InfoModel>
                )}
              </StlyedSpanWithClass>
            </StyledLIContainer>
            <StyledLIContainer>
              Duplicate Ids:
              <StlyedSpanWithClass className={styleDict[duplicateIdStatus]}>
                {duplicateIdStatus}
              </StlyedSpanWithClass>
            </StyledLIContainer>
          </StyledULContainer>
        </StyledUl>
        {employeesWithErrors.length > 0 && (
          <StyledTableContainer className="table mt-5">
            <StyledTHeadContainer>
              <StyledTRContainer>
                {tableHeaders.map((header) => {
                  return <StyledTHContainer key={header}>{header}</StyledTHContainer>;
                })}
              </StyledTRContainer>
            </StyledTHeadContainer>
            <StyledTBodyContainer>{renderQcInfo()}</StyledTBodyContainer>
          </StyledTableContainer>
        )}
      </StyleModalBody>
    </StyledModal>
  );
};

PayrollModal.propTypes = {
  show: PropTypes.bool.isRequired,
  header: PropTypes.string,
  tableHeaders: PropTypes.arrayOf(PropTypes.string).isRequired,
  duplicateIdStatus: PropTypes.string.isRequired,
  currentStatus: PropTypes.string.isRequired,
  employeesWithErrors: PropTypes.arrayOf(
    PropTypes.shape({
      employee_id: PropTypes.number,
      paychex_name: PropTypes.string,
      name: PropTypes.string,
      similarity_ratio: PropTypes.number,
      formattedNameOnDb: PropTypes.string,
      warning_details: PropTypes.arrayOf(PropTypes.string),
      warning_codes: PropTypes.arrayOf(PropTypes.string),
      error_details: PropTypes.arrayOf(PropTypes.string),
      error_codes: PropTypes.arrayOf(PropTypes.string),
    }),
  ).isRequired,
  handleModalOpenClose: PropTypes.func.isRequired,
  headerInfo: PropTypes.shape({
    status: PropTypes.string,
    missing_headers: PropTypes.arrayOf(PropTypes.string),
    wrong_headers: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};

PayrollModal.defaultProps = {
  header: '',
};

export default PayrollModal;
