import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState } from 'recoil';
import { selectedFiltersState } from '@recoil/admin';

// TODO: should this be global?
const FilterDropdownContainer = styled.div`
  display: flex;
  align-items: center;
`;
const Option = styled.div`
  margin: 5px 10px;
  cursor: pointer;
`;
const SelectedOption = styled.div``;
const OptionsContainer = styled.div`
  width: 100%;
  padding: 5px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  flex-direction: column;
  display: ${({ isOpen }) => (isOpen ? 'flex' : 'none')};
  background-color: ${({ theme }) => theme.secondary[100]};
  border-radius: 6px;
`;

const UserFilterDropdown = (props) => {
  const [isOpen, setIsOpen] = useState(false);
  const { filter } = props;
  const { displayName: filterDisplayName, name: filterName, options } = filter;
  const [selectedFilters, setSelectedFilters] = useRecoilState(selectedFiltersState);

  const selectedFilter = selectedFilters[filterName] || { name: 'All' };

  const toggleIsOpen = () => setIsOpen(!isOpen);

  const handleOptionSelect = (optionValue) => {
    setSelectedFilters({ ...selectedFilters, [filterName]: optionValue });
  };

  return (
    <FilterDropdownContainer onClick={toggleIsOpen}>
      <SelectedOption>
        {filterDisplayName}: {selectedFilter.name}
      </SelectedOption>

      <OptionsContainer isOpen={isOpen}>
        {options.map((option) => {
          const { name, value } = option;
          return (
            <Option onClick={() => handleOptionSelect(option)} key={`${name}-${value}`}>
              {name}
            </Option>
          );
        })}
      </OptionsContainer>
    </FilterDropdownContainer>
  );
};

UserFilterDropdown.propTypes = {
  filter: PropTypes.shape({
    displayName: PropTypes.string,
    name: PropTypes.string,
    key: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.shape({})),
  }),
};
UserFilterDropdown.defaultProps = {
  filter: {
    options: [],
  },
};

export default UserFilterDropdown;
