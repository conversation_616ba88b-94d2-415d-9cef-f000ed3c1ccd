import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAWXVolumeBonus = ({ record }) => {
  const { dealId, finalContractAmount, bonusTier } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="bonusTier"
            value={bonusTier}
            title="Bonus Tier"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HEAWXVolumeBonus.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    finalContractAmount: PropTypes.string,
    bonusTier: PropTypes.string,
  }),
};

HEAWXVolumeBonus.defaultProps = {
  record: {},
};

export default HEAWXVolumeBonus;
