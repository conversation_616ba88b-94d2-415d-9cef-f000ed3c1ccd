import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import styled from 'styled-components';

import { Row, Col, FormInput } from '@components/global/Form';
import { openPhoneCall, displayPhoneNumber } from '@utils/functions';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const UnresultedVisitsForm = ({ record }) => {
  const {
    dealId,
    subject,
    leadVendor,
    heaVisitResult,
    siteid,
    date,
    endDate,
    phoneNumber,
    isCap,
  } = record;
  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
          <FormInput
            readOnly
            name="heaVisitResult"
            value={heaVisitResult}
            title="HEA Visit Result"
            placeholder=""
          />
          <FormInput readOnly name="date" value={date} title="Date" placeholder="" />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="leadVendor"
            value={leadVendor}
            title="Lead Vendor"
            placeholder=""
          />
          <FormInput readOnly name="siteid" value={siteid} title="Site ID" placeholder="" />
          <FormInput
            readOnly
            name="endDate"
            value={moment(endDate).format('MM-DD-YYYY  h:mm A')}
            title="End"
            placeholder=""
          />
          <FormInput
            readOnly
            name="phoneNumber"
            value={displayPhoneNumber(phoneNumber)}
            title="Phone Number"
            onClick={() => openPhoneCall(phoneNumber)}
            placeholder="Phone Number"
          />
        </Col>
      </Row>
    </>
  );
};

UnresultedVisitsForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    subject: PropTypes.string,
    leadVendor: PropTypes.string,
    heaVisitResult: PropTypes.string,
    siteid: PropTypes.string,
    date: PropTypes.string,
    endDate: PropTypes.string,
    phoneNumber: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

UnresultedVisitsForm.defaultProps = {
  record: {},
};

export default UnresultedVisitsForm;
