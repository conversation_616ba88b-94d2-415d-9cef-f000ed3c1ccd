import React, { useEffect, useState, Suspense } from 'react';
import { useRecoilValue } from 'recoil';
import { BrowserRouter, Route, Redirect } from 'react-router-dom';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import styled, { ThemeProvider } from 'styled-components';
import { Normalize } from 'styled-normalize';

import {
  SideBar,
  LoadingIndicator,
  ErrorMessage,
  SuccessMessage,
  MenuBar,
  AllEventsListener,
} from '@components/global';

import EventSidebar from '@components/EventSidebar';

import DocRepoMain from '@components/DocRepo/DocRepoMain';
import PartnersDocRepo from '@components/DocRepo/PartnersDocRepo';
import PartnersAccountList from '@components/DocRepo/PartnersAccountList';
import { WorkReceiptAdmin } from '@components/workReceipt';
import FragmentSwitch from '@utils/FragmentSupportingSwitch';
import { isAuthorized, hasRole, setUserCookie } from '@utils/AuthUtils';
import { statesSelector, setStates } from '@recoil/app';

import {
  Home,
  LeadIntakeDev,
  LeadIntakeCT,
  ViewHEASchedule,
  ViewHEAScheduleCT,
  ViewHVACSalesSchedule,
  ViewHVACInstallSchedule,
  ViewInsulationInstallSchedule,
  RowDetails,
  EditAgentInfo,
  EditUserInfo,
  NotFound,
  TestingPage,
  WxPayroll,
  FinancePayroll,
  EventPatterns,
  MoneyCollection,
  ReportingPage,
  LeadIntake,
  Partners,
  WorkReceipt,
  HEAPayDashboard,
  Customers,
  CustomerDetails,
  ApplicationSettings,
} from '@pages';
import GlobalStyle from './style/GlobalStyle';
import ReactDatePickerStyles from './style/ReactDatePickerStyles';
import { mainTheme } from './style/variables';

import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import 'sweetalert2/dist/sweetalert2.min.css';

const {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textAndColor: {
    main: { textTransform, color },
  },
} = mainTheme;
const theme = {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textTransform,
  color,
};

const PageWrapper = styled.div`
  display: flex;
  justify-content: start;
  height: 100%;
`;

const ContentWithMenuBarWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: ${primary[100]} url('../assets/house-background.svg');
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: 45%;
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;
  width: 100%;
  padding: 0 18px 18px 18px;
`;

const App = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isPartnerAgent, setIsPartnerAgent] = useState(false);
  const usaStates = useRecoilValue(statesSelector);
  useEffect(() => {
    setUserCookie().then(() => setIsInitialized(true));
    // Partner Agents are of Two Types i.e NON HWE Agent and HWE Agent
    // Non HWE Agent must view only Sub Hub Page
    const isPartnerAgent = hasRole('Agent', 'Partners') || hasRole('Agent', 'Insulation Partners');
    setIsPartnerAgent(isPartnerAgent);
    setStates(usaStates);
  }, []);

  if (!isInitialized) return null;
  const homePageRedirectPath = !isPartnerAgent ? '/home' : '/view-partners-schedule';
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      <ReactDatePickerStyles />
      <DndProvider backend={HTML5Backend}>
        <BrowserRouter>
          <Suspense fallback={<div>Loading...</div>}>
            <PageWrapper id="app-content">
              <Route component={SideBar} />
              <ContentWithMenuBarWrapper>
                <Route component={MenuBar} />
                <ContentWrapper>
                  <FragmentSwitch>
                    <Redirect exact from="/" to={homePageRedirectPath} />
                    {isPartnerAgent && <Redirect exact from="/home" to={homePageRedirectPath} />}
                    {!isPartnerAgent && <Route path="/home" component={Home} />}
                    {isAuthorized('Agent', 'Partners', true) && (
                      <Route path="/view-partners-schedule" component={Partners} />
                    )}
                    {isAuthorized('Agent', 'HEA', false, 'CT') && (
                      <Route path="/view-hea-schedule-CT" component={ViewHEAScheduleCT} />
                    )}
                    {/* TEMPORARY REDIRECT FOR CT */}
                    {[
                      isAuthorized('Scheduler', 'HEA', false, 'MA') ||
                        isAuthorized('Agent', 'HEA', false, 'CT'),
                    ] && (
                      <Route
                        render={(renderProps) => {
                          const isOldCTUrl = renderProps.location.search.includes('?state=CT');
                          if (isOldCTUrl)
                            return <Redirect to="/view-hea-schedule-CT" {...renderProps} />;
                          return <ViewHEASchedule {...renderProps} />;
                        }}
                        path="/view-hea-schedule"
                      />
                    )}
                    {isAuthorized('Scheduler', 'HVAC-Sales') && (
                      <Route path="/view-hvac-sales-schedule" component={ViewHVACSalesSchedule} />
                    )}
                    {(isAuthorized('Agent', 'Marketing', true) || isAuthorized('Agent', 'CIA'),
                    true) && <Route path="/lead-intake-dev/:leadId?" component={LeadIntakeDev} />}
                    {(isAuthorized('Agent', 'Marketing', true) || isAuthorized('Agent', 'CIA'),
                    true) && <Route path="/lead-intake-CT/:leadId?" component={LeadIntakeCT} />}
                    {isAuthorized('Basic', 'HVAC-Install', true) && (
                      <>
                        <Route
                          path="/view-hvac-install-schedule"
                          component={ViewHVACInstallSchedule}
                        />
                        <Route
                          path="/doc-repo/:state/:department/:uniqueId/:company"
                          component={DocRepoMain}
                        />
                      </>
                    )}
                    {(isAuthorized('Agent', 'HVAC-Sales') ||
                      !isAuthorized('Agent') ||
                      isAuthorized('Agent', 'HEA', false, 'CT') ||
                      isAuthorized('Agent', 'CT')) && (
                      <Route
                        path="/doc-repo/:state/:department/:uniqueId/"
                        component={DocRepoMain}
                      />
                    )}
                    <Route
                      path="/view-doc-repo/:state/:department/:uniqueId/:dealId"
                      component={PartnersDocRepo}
                    />
                    <Route
                      path="/view-doc-repo/:state/:department/:accountId"
                      component={PartnersAccountList}
                    />
                    {isAuthorized('External Scheduler', 'Insulation', true) && (
                      <Route
                        path="/view-insulation-install-schedule"
                        component={ViewInsulationInstallSchedule}
                      />
                    )}
                    {isAuthorized('Scheduler', 'All') && (
                      <Route path="/lead-intake" component={LeadIntake} />
                    )}
                    {(isAuthorized('Scheduler', 'All', true) || hasRole('Agent')) && (
                      <Route path="/row-details/:oid" component={RowDetails} />
                    )}
                    {isAuthorized('Manager', 'All') && (
                      <>
                        <Route path="/edit-agent-info" component={EditAgentInfo} />
                        <Route path="/edit-user-info" component={EditUserInfo} />
                      </>
                    )}
                    {isAuthorized('Manager', 'HEA') && (
                      <Route path="/event-patterns" component={EventPatterns} />
                    )}
                    {isAuthorized('Basic', 'Payroll') && (
                      <Route path="/wx-payroll" component={WxPayroll} />
                    )}
                    {isAuthorized('Scheduler', 'Payroll') && (
                      <>
                        <Route path="/finance-payroll" component={FinancePayroll} />
                        <Route path="/money-collection" component={MoneyCollection} />
                      </>
                    )}
                    {isAuthorized('Super User', 'All') && (
                      <>
                        <Route path="/application-settings" component={ApplicationSettings} />
                        <Route path="/dev-testing-page" component={TestingPage} />
                        <Route path="/reporting" component={ReportingPage} />
                      </>
                    )}
                    {isAuthorized('Agent', 'HEA') && (
                      <Route path="/pay-dashboard" component={HEAPayDashboard} />
                    )}
                    {isAuthorized('Super User', 'HEA') && (
                      <Route path="/work-receipt-admin" component={WorkReceiptAdmin} />
                    )}

                    {isAuthorized('Agent', 'HEA') && (
                      <Route path="/work-receipt-dev" component={WorkReceipt} />
                    )}
                    {(isAuthorized('Agent', 'Marketing', true) ||
                      isAuthorized('Agent', 'CIA') ||
                      isAuthorized('Manager', 'All')) && (
                      <>
                        <Route path="/customers" component={Customers} />
                        <Route path="/customer-details/:customerId" component={CustomerDetails} />
                      </>
                    )}
                    <Route path="*" component={NotFound} />
                  </FragmentSwitch>
                </ContentWrapper>
              </ContentWithMenuBarWrapper>
              <Route component={EventSidebar} />
            </PageWrapper>
          </Suspense>
        </BrowserRouter>
        <Normalize />
        <LoadingIndicator />
        <ErrorMessage />
        <SuccessMessage />
        <AllEventsListener />
      </DndProvider>
    </ThemeProvider>
  );
};

export default App;
