import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { NameInitialsImage } from '@components/global';

import DeleteListItemButton from './DeleteListItemButton';

const StyledListCard = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 5px;
  flex-grow: 2;
  word-break: break-word;
  background-color: ${({ active, theme }) => {
    return active ? theme.secondary[300] : theme.secondary[100];
  }};
  border: solid 0.5px ${({ theme }) => theme.secondary[300]};
  border-style: solid none none none;

  &:hover {
    background-color: ${({ theme }) => theme.colors.lightYellow};
    box-shadow: inset 0px -1px 0px ${({ theme }) => theme.colors.shadowBlock};
    cursor: pointer;
  }
`;

const DetailsContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding: 10px;
  width: 100%;
`;

const Details = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-left: ${({ hasIcon }) => (hasIcon ? '10px' : '0px')};
`;

const Title = styled.div`
  color: ${({ theme }) => theme.primary[500]};
`;

const ListCard = ({ onClick, active, iconText, title, detailsNode, deleteItem }) => {
  return (
    <StyledListCard onClick={onClick} active={active}>
      <DetailsContainer>
        {iconText && <NameInitialsImage text={iconText} />}
        <Details hasIcon={!!iconText}>
          <Title>{title}</Title>
          {detailsNode}
        </Details>
      </DetailsContainer>
      {deleteItem ? <DeleteListItemButton deleteItem={deleteItem} /> : null}
    </StyledListCard>
  );
};

ListCard.propTypes = {
  onClick: PropTypes.func,
  active: PropTypes.bool,
  iconText: PropTypes.string,
  title: PropTypes.string,
  detailsNode: PropTypes.node,
  deleteItem: PropTypes.func,
};

ListCard.defaultProps = {
  onClick: () => {},
  active: false,
  iconText: null,
  title: null,
  detailsNode: null,
  deleteItem: null,
};

export default ListCard;
