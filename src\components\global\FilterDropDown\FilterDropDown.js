import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';

const StyledFontAwesomeIcon = styled(FontAwesomeIcon)`
  margin: 1px 0px 0px 10px;
`;
const StyledSortButton = styled.button`
  display: inline-flex;
  padding: 7px 10px;
  margin: 0 10px 0 10px;
  border-radius: 20px;
  background-color: transparent;
  color: ${({ theme }) => theme.colors.darkBlue};
  border-color: black;
  font-weight: 400;
  font-size: 16px;
  border-width: thin;
`;
const StyledSortButtons = styled.button`
  color: ${({ theme }) => theme.colors.darkBlue};
  &.active-sort {
    background: ${({ theme }) => theme.colors.darkBlue};
    color: white;
  }
`;
const StyledDropdown = styled.div`
  display: inline-block;
  width: auto;
  position: relative;

  &:hover .dropdown-content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
`;
const StyledDropdownContent = styled.div`
  position: absolute;
  display: none;
  background-color: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
`;

const FilterDropDown = ({ title, filterData, filterFunction, clearFilter }) => {
  const [sort, setSort] = useState('All');
  const [dropDownData, setDropDownData] = useState(filterData);

  // display the filtered text on the dropdown button
  const handleSort = (sortBy) => {
    const data = dropDownData.filter((data) => {
      return data.key === sortBy;
    });
    setSort(data[0].name);
    filterFunction((prevState) => {
      const key = title?.toLowerCase();
      return { ...prevState, [key]: sortBy };
    });
  };

  useEffect(() => {
    if (clearFilter) setSort('All');
  }, [clearFilter]);

  useEffect(() => {
    setDropDownData(filterData);
  }, [filterData]);

  return (
    <StyledDropdown className="dropdown">
      <StyledSortButton type="button">
        {`${title} : ${sort}`}
        <StyledFontAwesomeIcon icon={faChevronDown} />
      </StyledSortButton>
      <StyledDropdownContent className="sortOptions dropdown-content">
        <StyledSortButtons
          type="button"
          className={sort === 'All' ? 'active-sort' : ''}
          key="All"
          onClick={() => {
            setSort('All');
            filterFunction((prevState) => {
              const key = title?.toLowerCase();
              return { ...prevState, [key]: 'All' };
            });
          }}
        >
          All
        </StyledSortButtons>
        {dropDownData.map((data) => {
          return (
            <StyledSortButtons
              type="button"
              className={sort === data.name ? 'active-sort' : ''}
              key={data.key}
              onClick={() => handleSort(data.key)}
            >
              {data.name}
            </StyledSortButtons>
          );
        })}
      </StyledDropdownContent>
    </StyledDropdown>
  );
};

FilterDropDown.propTypes = {
  title: PropTypes.string.isRequired,
  filterData: PropTypes.arrayOf(PropTypes.shape({})),
  filterFunction: PropTypes.func.isRequired,
  clearFilter: PropTypes.bool.isRequired,
};

FilterDropDown.defaultProps = {
  filterData: [{ name: '', key: '' }],
};

export default FilterDropDown;
