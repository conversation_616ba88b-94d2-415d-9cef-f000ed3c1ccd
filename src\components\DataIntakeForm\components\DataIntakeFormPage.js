import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { activeTabIndexAtom } from '@recoil/app';
import { useRecoilState } from 'recoil';

import DataIntakeFormFields from './DataIntakeFormFields';

const DataIntakeFormPageContainer = styled.div`
  padding: 24px;
  height: calc(100% - 44px);
  width: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const DataIntakeFormPageHeader = styled.div`
  font-size: 22px;
`;

const DataIntakeFormPageContentsContainer = styled.div`
  width: 45%;

  @media (max-width: 768px) {
    width: 80%;
  }

  @media (max-width: 468px) {
    width: 100%;
  }
`;

const DataIntakeFormPage = ({ map, actions, readOnlyForReview }) => {
  const scrollContainerRef = useRef(null);
  const [activeTabIndex] = useRecoilState(activeTabIndexAtom(['tabs']));
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.scrollTop = 0;
    }
  }, [activeTabIndex]);
  if (!map) return null;
  const { text, fields } = map;
  return (
    <DataIntakeFormPageContainer ref={scrollContainerRef}>
      <DataIntakeFormPageHeader>{text}</DataIntakeFormPageHeader>
      <DataIntakeFormPageContentsContainer>
        {fields && <DataIntakeFormFields fields={fields} readOnlyForReview={readOnlyForReview} />}
        {actions}
      </DataIntakeFormPageContentsContainer>
    </DataIntakeFormPageContainer>
  );
};

DataIntakeFormPage.propTypes = {
  map: PropTypes.shape({
    text: PropTypes.string,
    name: PropTypes.string,
    fields: PropTypes.arrayOf(PropTypes.shape({})),
  }),
  actions: PropTypes.node,
  readOnlyForReview: PropTypes.bool,
};

DataIntakeFormPage.defaultProps = {
  readOnlyForReview: false,
  actions: null,
  map: null,
};

export default DataIntakeFormPage;
