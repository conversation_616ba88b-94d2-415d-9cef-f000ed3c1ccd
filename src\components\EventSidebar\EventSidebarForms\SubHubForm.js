import React, { useState, useEffect, Suspense } from 'react';
import { useLocation } from 'react-router-dom';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import moment from 'moment';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { throwError } from '@utils/EventEmitter';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormSelect,
  FormTextBox,
  FormInput,
  FormDatePicker,
  FormInfoField,
  FormInfo,
} from '@components/global/Form';
import SalesforceIcon from '@components/global/Icons/SalesforceIcon';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, {
  <PERSON>er<PERSON><PERSON><PERSON>,
  Header<PERSON>abel,
} from '@components/EventSidebar/EventSidebarHeader';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import { LoadingIndicator } from '@components/global';
import Tabs from '@components/global/Tabs/Tabs';
import PartnersDocRepo from '@components/DocRepo/PartnersDocRepo';
import ToggleOnOffIcon from '@components/global/Icons/ToggleOnOffIcon';
import ToolTip from '@components/global/Tooltip/Tooltip';
import { DollarCircle } from '@styled-icons/boxicons-regular/DollarCircle';
import TownWarnings from '@components/townWarnings/TownWarnings';

import { activeTabIndexAtom } from '@recoil/app';
import { selectedPartnerEventState, showSidebarState } from '@recoil/eventSidebar';
import { UsersManager, EventsManager } from '@utils/APIManager';
import {
  issStatusOptions,
  partnerStatusOptions,
  barrierTypeOptions,
} from '@utils/businessLogic/partnersBusinessLogic';
import { isAuthorized, hasRole } from '@utils/AuthUtils';

const HeaderActionContainer = styled.div`
  display: flex;
  flex-direction: row;
  background: ${({ theme }) => theme.secondary[200]};
`;

const CapIcon = styled(DollarCircle)`
  height: 30px;
  width: 30px;
  color: ${({ theme }) => theme.colors.capJob};
  margin-left: 0px;
  margin-top: 5px;
`;

const HeaderLabelContainer = styled(HeaderLabel)`
  display: flex;
`;

const HeaderButtonsContainer = styled.div`
  display: flex;
`;

const SubHubForm = ({ handleCancelClick, handleSaveClick }) => {
  const [hovering, setHovering] = useState(false);
  const [loading, setLoading] = useState(false);
  const [typeName, setTypeName] = useState('');
  const [partnerSelectOptions, setPartnerSelectOptions] = useState([{ key: 'All', value: '' }]);
  const [selectedPartnerEvent, setSelectedPartnerEvent] = useRecoilState(selectedPartnerEventState);
  const activeTab = useRecoilValue(activeTabIndexAtom(['tabs']));
  const setShowSidebar = useSetRecoilState(showSidebarState);

  const {
    id,
    type,
    oid,
    accountName,
    status,
    paperWorkStatus,
    remediationEstimation,
    customerName,
    address,
    phoneNumber,
    secondaryPhoneNumber,
    email,
    inspectionScheduledDate,
    remediationStartDate,
    remediationEndDate,
    notes: { hweNotes, partnerNotes },
    scheduledDate,
    scheduledBy,
    numUnit,
    sfIds: { accountId },
    archive,
    isCap,
    sfIds,
    halted,
  } = selectedPartnerEvent;

  // Barrier types gets set from the salesforce fetch function, which only happens for new jobs
  let { barrierTypes } = selectedPartnerEvent;

  const isCreate = !id;

  // If the job is already scheduled, use the event type instead
  if (!isCreate) barrierTypes = [{ eventType: type }];
  const typeOptions = barrierTypes.map(({ eventType }) => {
    return barrierTypeOptions.find(({ value }) => value === eventType);
  });

  const restrictEditFields = !isAuthorized('Manager', 'Partners');

  const tabs = [
    {
      name: 'details',
      title: 'Details',
    },
    {
      name: 'docRepo',
      title: 'Doc Repo',
      component: <PartnersDocRepo />,
    },
  ];

  const statusOptions = hasRole('Agent', 'Partners') ? partnerStatusOptions : issStatusOptions;

  const showOnlyDocRepo = useLocation().pathname.includes('/view-doc-repo/');

  const haltedIEWarning = halted
    ? [
        {
          townWarningName: 'HALTED INCOME ELIGIBLE JOB',
          townWarningDescription: 'All work on this job must be temporarily halted',
        },
      ]
    : null;

  useEffect(() => {
    if (type) {
      if (showOnlyDocRepo) {
        const { key } = barrierTypeOptions.find(({ value }) => {
          return value === type;
        });
        setTypeName(key);
      } else getPartners(type);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type]);

  const getPartners = async (type) => {
    const { users } = await UsersManager.getUsersForEventType(type);

    const partnerSelectOptions = users?.map(({ displayName, oid }) => {
      return { key: displayName, value: oid };
    });
    partnerSelectOptions.sort((a, b) => {
      return a.key.toLowerCase() < b.key.toLowerCase() ? -1 : 1;
    });
    setPartnerSelectOptions(partnerSelectOptions);
  };

  // TODO: Refactor these functions, there should be no job type change if there are no barriers as options
  const setBarrier = (type) => {
    if (barrierTypes.length > 0) {
      const selectedBarrierType = barrierTypes?.find(({ eventType }) => {
        return eventType === type;
      });
      if (selectedBarrierType?.barrierId) {
        const { barrierId } = selectedBarrierType;
        setSelectedPartnerEvent({
          ...selectedPartnerEvent,
          sfIds: { ...sfIds, barrierId },
          type,
        });
        return true;
      }
    }
    return false;
  };

  const handleJobTypeChange = (e) => {
    const type = e.target.value;
    const barrier = setBarrier(type);
    if (!barrier)
      return throwError(
        accountId ? 'Barrier Not Created for Selected Job Type' : 'Incorrect Account Id',
      );
    return false;
  };

  const handleDateChange = (newDate, name) => {
    const event = { target: { name, value: moment(newDate).startOf('day') } };
    handleFieldChange(event);
  };

  const handleFieldChange = (e) => {
    return handleFormFieldChange(e, selectedPartnerEvent, setSelectedPartnerEvent);
  };

  const handleOnClickArchiveButton = async () => {
    const success = await EventsManager.updatePartnerEvent({
      ...selectedPartnerEvent,
      archive: !archive,
    });
    handleFieldChange({ target: { name: 'archive', value: !archive } });
    if (success) setShowSidebar(false);
  };

  const renderForm = () => {
    return (
      <>
        <TownWarnings townWarnings={haltedIEWarning} />
        <EventSidebarBody>
          <Row>
            <Col size={1}>
              <SfIdInputs
                sfObjectType="account"
                setLoading={setLoading}
                readOnly={!isCreate || restrictEditFields}
                recoilState={selectedPartnerEventState}
                allowMultiUnit={false}
              />
            </Col>
            <Col size={1}>
              <SfIdInputs
                sfObjectType="site"
                setLoading={setLoading}
                readOnly
                recoilState={selectedPartnerEventState}
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormSelect
                required
                title="Job Type"
                placeholder="Select Job Type"
                name="type"
                readOnly={!isCreate || restrictEditFields}
                value={type}
                onChange={handleJobTypeChange}
                options={typeOptions}
                disabled={false}
              />
            </Col>
            <Col size={1}>
              {type === '008800' && (
                <FormSelect
                  required
                  title="Number of Units:"
                  placeholder="Select Units"
                  name="numUnit"
                  readOnly={!isCreate || restrictEditFields}
                  value={numUnit}
                  onChange={handleFieldChange}
                  options={[
                    { key: '1', value: 1 },
                    { key: '2', value: 2 },
                    { key: '3', value: 3 },
                    { key: '4', value: 4 },
                  ]}
                />
              )}
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormSelect
                required
                title="Partner"
                placeholder="Select Partner"
                name="oid"
                readOnly={restrictEditFields}
                value={oid}
                onChange={handleFieldChange}
                options={partnerSelectOptions}
              />
            </Col>
            <Col size={1}>
              <FormSelect
                required
                title="Status"
                placeholder="Select Status"
                name="status"
                value={status}
                onChange={handleFieldChange}
                options={statusOptions}
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormDatePicker
                title="Inspection Date:"
                name="inspectionScheduledDate"
                value={
                  moment(inspectionScheduledDate).isValid() ? moment(inspectionScheduledDate) : null
                }
                onChange={handleDateChange}
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormDatePicker
                title="Remediation Start Date:"
                name="remediationStartDate"
                value={moment(remediationStartDate).isValid() ? moment(remediationStartDate) : null}
                onChange={handleDateChange}
              />
            </Col>
            <Col size={1}>
              <FormDatePicker
                title="Remediation End Date:"
                name="remediationEndDate"
                value={moment(remediationEndDate).isValid() ? moment(remediationEndDate) : null}
                onChange={handleDateChange}
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormSelect
                title="Paper Work Status"
                placeholder="Select Paper Work Status"
                name="paperWorkStatus"
                value={paperWorkStatus}
                onChange={handleFieldChange}
                options={[
                  { key: 'Awaiting Payment', value: 'Awaiting Payment' },
                  { key: 'Paperwork/Estimate Sent', value: 'Paperwork/Estimate Sent' },
                ]}
              />
            </Col>
            <Col size={1}>
              <FormInput
                name="remediationEstimation"
                title="Remediation Estimate:"
                value={remediationEstimation}
                type="number"
                min={1}
                onChange={handleFieldChange}
                placeholder="Select a Remediation Estimate:"
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="address"
                value={address?.displayAddress || ''}
                title="location"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <Col size={1}>
                <FormInput
                  readOnly
                  name="customerName"
                  value={customerName}
                  title="Customer NAme"
                  placeholder=""
                />
              </Col>
            </Col>
            <Col size={1}>
              <Col size={1}>
                <FormInput
                  readOnly
                  name="email"
                  value={email}
                  title="Customer Email"
                  placeholder=""
                />
              </Col>
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="phoneNumber"
                value={phoneNumber}
                title="Customer Phone Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="secondaryPhoneNumber"
                value={secondaryPhoneNumber}
                title="Secondary Phone Number"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormTextBox
                name="notes.hweNotes"
                value={hweNotes}
                title="Notes from HomeWorks:"
                placeholder=""
                readOnly={restrictEditFields}
                onChange={handleFieldChange}
              />
            </Col>
            <Col size={1}>
              <FormTextBox
                name="notes.partnerNotes"
                value={partnerNotes}
                title="Notes from Partner:"
                placeholder=""
                onChange={handleFieldChange}
              />
            </Col>
          </Row>
          {!isCreate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(scheduledDate).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
        <EventSidebarFooter>
          <PrimaryButton right onClick={() => handleSaveClick(selectedPartnerEvent)}>
            Save
          </PrimaryButton>
          {!isCreate && !restrictEditFields && (
            <CancelButton left onClick={() => handleCancelClick(selectedPartnerEvent)}>
              Cancel Event
            </CancelButton>
          )}
        </EventSidebarFooter>
      </>
    );
  };

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        {showOnlyDocRepo ? (
          <>
            <EventSidebarHeader>
              <Row>
                <Col size={2}>
                  <HeaderTitle>{typeName}</HeaderTitle>
                </Col>
              </Row>
            </EventSidebarHeader>
            <EventSidebarBody>{type && <PartnersDocRepo />}</EventSidebarBody>
          </>
        ) : (
          <>
            <EventSidebarHeader>
              <Row>
                <Col size={2}>
                  <HeaderTitle>{!isCreate ? accountName : 'Add Job'}</HeaderTitle>
                  {!isCreate && isCap && (
                    <HeaderLabelContainer>
                      <CapIcon
                        onMouseEnter={() => setHovering(true)}
                        onMouseLeave={() => setHovering(false)}
                      />
                      {hovering && <ToolTip text="Income Eligible Job" />}
                    </HeaderLabelContainer>
                  )}
                </Col>
                {!isCreate && !restrictEditFields && (
                  <Col size={0} right>
                    <SalesforceIcon id={accountId} sfObject="Deal__c" />
                  </Col>
                )}
              </Row>
            </EventSidebarHeader>
            {isCreate ? (
              renderForm()
            ) : (
              <>
                <HeaderActionContainer>
                  <HeaderButtonsContainer>
                    <Tabs tabs={tabs} />
                    {!restrictEditFields && (
                      <ToggleOnOffIcon
                        toggleOn={archive}
                        hoverTextToOn="Click to Archive Job"
                        hoverTextToOff="Click to Remove From Archive"
                        onClick={handleOnClickArchiveButton}
                        text="Archive Job"
                      />
                    )}
                  </HeaderButtonsContainer>
                </HeaderActionContainer>
                {tabs[activeTab]?.name === 'details' ? renderForm() : tabs[activeTab].component}
              </>
            )}
          </>
        )}
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

SubHubForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default SubHubForm;
