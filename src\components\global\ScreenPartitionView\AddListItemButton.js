// External Dependencies
import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

// External Components
import { Plus } from '@styled-icons/boxicons-regular/Plus';

// Internal Components
import Clickable from '@components/global/Clickable';

// Styled Components
const StyledAddListItemButton = styled(Clickable)`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 15px;
  flex-grow: 2;
  word-break: break-word;
  background-color: ${({ theme }) => theme.secondary[100]};
  border: solid 0.5px ${({ theme }) => theme.secondary[300]};
  border-style: solid none none none;

  &:hover {
    background-color: ${({ theme }) => theme.colors.lightYellow};
    box-shadow: inset 0px -1px 0px ${({ theme }) => theme.colors.shadowBlock};
    cursor: pointer;
  }
`;

const StyledPlusIcon = styled(Plus)`
  height: 25px;
`;

// Main Component
const AddListItemButton = ({ onClick, text }) => {
  return (
    <StyledAddListItemButton onClick={onClick}>
      <StyledPlusIcon />
      {text}
    </StyledAddListItemButton>
  );
};

// Prop Types
AddListItemButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
};

AddListItemButton.defaultProps = {};

export default AddListItemButton;
