import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { isNumeric } from '@utils/functions';
import moment from 'moment';
import { Clickable, AgentImage } from '@components/global';
import { selectedEventState } from '@recoil/eventSidebar';

const StyledAgentSlot = styled(Clickable)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 20%;
  padding: 10px 0;
  border-radius: 4px;
  margin-right: 10px;
  overflow: hidden;
  color: ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.primary[500])};
  border: 1px solid
    ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
  font-weight: ${({ selected }) => (selected ? 'bold' : 'normal')};
`;

const AgentName = styled.div`
  margin-top: 10px;
`;

const DriveTimes = styled.div``;

const AgentSlot = ({ slot, singleAgent }) => {
  const [selectedSlot, setSelectedSlot] = useRecoilState(selectedEventState);

  const { date, startTime, openEnd, driveTimeTo, driveTimeFrom, oid, displayName } = slot;

  const handleSelectAgent = () => {
    let newOids = [];
    // If selected agent/crew/truck exisits, remove them
    if (selectedSlot.oids.includes(oid)) {
      newOids = selectedSlot.oids.filter((o) => o !== oid);
    } else if (singleAgent) {
      // Only one crew/truck/agent allow to do visit
      newOids = [oid];
    } else {
      // Multiple crews/trucks/agents allowed to do visit
      newOids = [...selectedSlot.oids, oid];
    }
    // Have to reset everything after the time selection (oids)

    const startEndTimes = [
      {
        start: moment(`${date} ${startTime}`),
        end: moment(`${date} ${openEnd}`),
      },
    ];

    setSelectedSlot({
      ...selectedSlot,
      oids: newOids,
      startTime,
      startEndTimes,
      endTime: openEnd,
      agentName: displayName,
    });
  };

  const selected =
    selectedSlot.date === date &&
    selectedSlot.startTime === startTime &&
    selectedSlot.oids.includes(oid);

  const hasDriveTimes = isNumeric(driveTimeTo) && isNumeric(driveTimeFrom);

  return (
    <StyledAgentSlot onClick={handleSelectAgent} selected={selected}>
      <AgentImage imageUrl={null} />
      <AgentName>{displayName}</AgentName>
      {hasDriveTimes && <DriveTimes>{driveTimeTo + driveTimeFrom} minute drive</DriveTimes>}
    </StyledAgentSlot>
  );
};

AgentSlot.propTypes = {
  slot: PropTypes.shape({
    date: PropTypes.string,
    startTime: PropTypes.string,
    oid: PropTypes.string,
    displayName: PropTypes.string,
    driveTimeTo: PropTypes.number,
    driveTimeFrom: PropTypes.number,
    openEnd: PropTypes.string,
  }).isRequired,
  singleAgent: PropTypes.bool,
};

AgentSlot.defaultProps = {
  singleAgent: false,
};

export default AgentSlot;
