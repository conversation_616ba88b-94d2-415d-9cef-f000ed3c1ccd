import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue } from 'recoil';
import Swal from 'sweetalert2/dist/sweetalert2';

import { UsersManager } from '@utils/APIManager';

import { useInvalidateSelector } from '@recoil/hooks';
import { selectedUserState, selectedUserRoleState } from '@recoil/admin/users';
import { refreshAdminListState } from '@recoil/admin';

import { ListCard } from '@components/global/ScreenPartitionView';

const RoleText = styled.span`
  font-weight: 500;
  margin-right: 5px;
`;

const DetailsContainer = styled.p`
  display: flex;
  justify-content: space-between;
  width: 100%;
  color: ${({ theme }) => theme.secondary[500]};
  margin-bottom: 0rem;
`;

const RolesListCard = (props) => {
  const { roleObject } = props;
  const refreshUsers = useInvalidateSelector(refreshAdminListState);
  const selectedUser = useRecoilValue(selectedUserState);
  const [selectedRole, setSelectedRole] = useRecoilState(selectedUserRoleState);
  const { department, departmentId, role, stateFullname, state } = roleObject;
  const { oid } = selectedUser;

  const handleSelectRole = () => {
    setSelectedRole(roleObject);
  };

  const deleteRole = async () => {
    // TODO: can these swals be added to the APIManager file?
    const alert = await Swal.fire({
      title: `Do you want to remove the ${department}: ${role} permission for the user?`,
      showCloseButton: true,
      showCancelButton: true,
      showConfirmButton: true,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
    });
    if (!alert?.value) return;

    const response = await UsersManager.deleteUserRoles(oid, departmentId, state);

    if (response) {
      await Swal.fire({
        title: 'Successfully updated roles.',
        confirmButtonText: 'OK',
      });
    }

    refreshUsers();
  };

  const DetailsNode = (
    <DetailsContainer>
      <RoleText>
        {stateFullname} - {role}
      </RoleText>
    </DetailsContainer>
  );

  return (
    <ListCard
      onClick={handleSelectRole}
      active={department === selectedRole.department && state === selectedRole.state}
      title={department}
      detailsNode={DetailsNode}
      deleteItem={deleteRole}
    />
  );
};

RolesListCard.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
  roleObject: PropTypes.shape({
    role: PropTypes.string,
    department: PropTypes.string,
    departmentId: PropTypes.number,
    stateFullname: PropTypes.string,
    state: PropTypes.string,
  }),
};

RolesListCard.defaultProps = {
  location: { search: null },
  roleObject: null,
};

export default RolesListCard;
