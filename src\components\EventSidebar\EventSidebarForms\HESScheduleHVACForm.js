import React, { Suspense, useEffect, useState } from 'react';

import PropTypes from 'prop-types';
import { useRecoilState } from 'recoil';
import _ from 'lodash';

import { selectedEventState } from '@recoil/eventSidebar';

import { SecondaryButton } from '@components/global/Buttons';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormMultiselect,
  FormRadioButtons,
  FormTextBox,
} from '@components/global/Form';
import FormCheckbox from '@components/global/Form/FormCheckboxes';
import FormFieldContainer from '@components/global/Form/FormFieldContainer';
import Header from '@components/global/Header';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';

import { LoadingIndicator } from '@components/global';
import { SalesforceManager } from '@utils/APIManager';
import { isAuthorized } from '@utils/AuthUtils';

const HESScheduleHVACForm = ({ handleFindSlotsClick, handleSaveClick }) => {
  const [event, setEvent] = useRecoilState(selectedEventState);
  const [isExpectationSet, setIsExpectationSet] = useState(null);
  const [signedHcaSheet, setSignedHcaSheet] = useState(null);
  const [unitOptions, setUnitOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const isHeaAgent = isAuthorized('Agent', 'HEA');
  if (isHeaAgent) setEvent({ ...event, leadSource: 'HEA Visit' });

  const {
    customerInterests = [],
    sfIds: { dealId, dealId2, dealId3, dealId4 },
    unitsSelected = [],
    formFieldErrors,
    notes,
    notes: { fieldNotes },
    timeFrameForProject,
    monthlyPaymentExpectations,
    primaryHeatingFuel,
    centralAc,
  } = event;

  const customerInterestsOptions = [
    { key: 'Combination Boiler', value: 'Combination Boiler' },
    { key: 'Forced Hot Water Boiler', value: 'Forced Hot Water Boiler' },
    { key: 'Steam Boiler', value: 'Steam Boiler' },
    { key: 'Furnace', value: 'Furnace' },
    { key: 'Ductless Mini-Splits', value: 'Ductless Mini-Splits' },
    { key: 'Central Air', value: 'Central Air' },
    { key: 'Heat Pump', value: 'Heat Pump' },
    { key: 'Hydro Air', value: 'Hydro Air' },
    { key: 'High Velocity', value: 'High Velocity' },
    { key: 'Geothermal', value: 'Geothermal' },
  ];

  useEffect(() => {
    getDealsInfo();
  }, []);

  useEffect(() => {
    if (isExpectationSet || signedHcaSheet) {
      const displayErrors = {
        ...formFieldErrors,
        isExpectationSet:
          isExpectationSet === 'no'
            ? '*You must set expectations before scheduling H&C visits.'
            : null,
        signedHcaSheet: signedHcaSheet === 'no' ? '*The customer must sign the HCA sheet' : null,
      };
      handleFieldChange({
        target: {
          name: 'formFieldErrors',
          value: displayErrors,
        },
      });
    }
  }, [isExpectationSet, signedHcaSheet]);

  const getDealsInfo = async () => {
    setLoading(true);
    const dealIdsArr = [dealId, dealId2, dealId3, dealId4];
    const validDealIds = dealIdsArr.filter((deal) => deal);

    const salesforceInfo = await SalesforceManager.getHEAEventInfoWithDealIds(validDealIds);
    setLoading(false);

    if (!salesforceInfo) return;

    const { accounts = {}, sfIds } = salesforceInfo;
    const unitOptions = [];

    if (Object.keys(accounts).length > 0) {
      for (let i = 0; i < validDealIds.length; i++) {
        const unitNumString = i === 0 ? '' : `${i + 1}`;
        const { id, name } = accounts[`account${unitNumString}`];
        unitOptions.push({ key: name, value: { unitNum: i, accountId: id } });
      }
      setUnitOptions(unitOptions);
    } else {
      const { accountId } = sfIds;
      handleFieldChange({ target: { name: 'unitsSelected', value: [{ unitNum: 0, accountId }] } });
    }
  };

  const handleOnClickCustomerInterests = (e) => {
    const { value } = e.target;
    const customerInterests = value.map(({ value }) => {
      return value;
    });
    const error = _.intersection(customerInterests, ['High Velocity', 'Geothermal']).length > 0;
    if (error) {
      const displayErrors = {
        ...formFieldErrors,
        customerInterests:
          "You can't select 'High Velocity,' or 'Geothermal' because HomeWorks Energy does not offer these solutions",
      };
      handleFieldChange({
        target: {
          name: 'formFieldErrors',
          value: displayErrors,
        },
      });
    } else handleFormFieldChange(e, event, setEvent);
  };

  const onViewSlotsButtonClick = async () => {
    handleFieldChange({ target: { name: 'id', value: null } });
    await handleFindSlotsClick();
  };
  const handleFieldChange = (e) => {
    return handleFormFieldChange(e, event, setEvent);
  };
  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderTitle>Book Appointment</HeaderTitle>
            </Col>
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          {unitOptions.length > 0 && (
            <FormCheckbox
              required
              title="Select Unit"
              name="unitsSelected"
              options={unitOptions}
              onChange={(e) => {
                handleFieldChange(e);
              }}
              value={unitsSelected}
            />
          )}
          {unitsSelected?.length > 0 && (
            <>
              <Header h2 weight={600} marginBottom="12px">
                At this time hand your phone or computer to the customer to answer the following 3
                questions.
              </Header>
              <FormFieldContainer fieldName="isExpectationSet">
                <FormRadioButtons
                  name="isExpectationSet"
                  required
                  title="Has the expectation been set with you that you will receive replacement options with potential Masssave incentives at the H&C visit?"
                  options={[
                    { key: 'Yes', value: 'yes' },
                    { key: 'No', value: 'no' },
                  ]}
                  value={isExpectationSet}
                  onChange={(e) => setIsExpectationSet(e.target.value)}
                  maxGap
                  weight={650}
                />
              </FormFieldContainer>
            </>
          )}

          {isExpectationSet === 'yes' && (
            <FormFieldContainer fieldName="timeFrameForProject">
              <FormRadioButtons
                name="timeFrameForProject"
                required
                title="What is your time frame for moving forward with a project?"
                options={[
                  {
                    key: 'I am actively looking to purchase a new system within the month',
                    value: 'within month',
                  },
                  {
                    key: 'I am interested in purchasing a new system within the next six months',
                    value: 'within next 6 months',
                  },
                  {
                    key: 'I am not sure and interested in learning about my options',
                    value: 'learning about my options',
                  },
                ]}
                value={timeFrameForProject}
                onChange={handleFieldChange}
                maxGap
                weight={650}
              />
            </FormFieldContainer>
          )}
          {timeFrameForProject && (
            <FormFieldContainer fieldName="monthlyPaymentExpectations">
              <FormRadioButtons
                name="monthlyPaymentExpectations"
                required
                title="Monthly payments for whole home heating and cooling projects are typically between $200 and $350 a month on the Heat Loan. Is that in line with your expectations?"
                options={[
                  { key: 'Yes', value: 'yes' },
                  { key: 'No', value: 'no' },
                ]}
                value={monthlyPaymentExpectations}
                onChange={handleFieldChange}
                weight={650}
                maxGap
              />
            </FormFieldContainer>
          )}
          {monthlyPaymentExpectations && (
            <>
              <Header h2 weight={600} marginBottom="12px">
                Please hand the phone/computer back to your Home Energy Specialist to finish
                scheduling.{' '}
              </Header>
              <FormMultiselect
                required
                title="What type of system(s) is the customer interested in?"
                name="customerInterests"
                options={customerInterestsOptions}
                onChange={handleOnClickCustomerInterests}
                value={customerInterests}
                weight={650}
              />
            </>
          )}
          {customerInterests?.length > 0 && (
            <FormFieldContainer fieldName="centralAc">
              <FormRadioButtons
                name="centralAc"
                required
                title="Does the customer currently have central A/C?"
                options={[
                  { key: 'Yes', value: 'yes' },
                  { key: 'No', value: 'no' },
                ]}
                value={centralAc}
                onChange={handleFieldChange}
                maxGap
                weight={650}
              />
            </FormFieldContainer>
          )}
          {centralAc && (
            <FormFieldContainer fieldName="primaryHeatingFuel">
              <FormRadioButtons
                name="primaryHeatingFuel"
                required
                title="What is their primary heating fuel?"
                options={[
                  { key: 'Natural Gas', value: 'Gas' },
                  { key: 'Oil', value: 'Oil' },
                  { key: 'Propane', value: 'Propane' },
                  { key: 'Electric', value: 'Electric' },
                  { key: 'Other', value: 'Other' },
                ]}
                value={primaryHeatingFuel}
                onChange={handleFieldChange}
                maxGap
                weight={650}
              />
            </FormFieldContainer>
          )}
          {isExpectationSet === 'yes' && primaryHeatingFuel && customerInterests?.length > 0 && (
            <FormFieldContainer fieldName="signedHcaSheet">
              <FormRadioButtons
                name="signedHcaSheet"
                required
                title="Did the customer sign the HCA sheet?"
                options={[
                  { key: 'Yes', value: 'yes' },
                  { key: 'No', value: 'no' },
                ]}
                value={signedHcaSheet}
                onChange={(e) => setSignedHcaSheet(e.target.value)}
                maxGap
                weight={650}
              />
            </FormFieldContainer>
          )}
          {signedHcaSheet === 'yes' && (
            <FormTextBox
              name="notes.fieldNotes"
              required
              value={notes.fieldNotes}
              title="Notes"
              placeholder=""
              onChange={handleFieldChange}
              weight={650}
            />
          )}
          <AvailableSlots allowAgentSelect={false} />
          <BookSlotsButton handleBookSlots={() => handleSaveClick()} />
        </EventSidebarBody>

        <EventSidebarFooter>
          {fieldNotes?.length > 0 && (
            <SecondaryButton left onClick={onViewSlotsButtonClick}>
              View Available Slots
            </SecondaryButton>
          )}
        </EventSidebarFooter>
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

HESScheduleHVACForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default HESScheduleHVACForm;
