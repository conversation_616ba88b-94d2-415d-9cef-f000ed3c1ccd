import { startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import axios from 'axios';

const getCustomersList = async ({ limit = 20 }) => {
  try {
    startLoading('Fetching Recent Customers List...');
    const response = await axios.get(`/api/customers/getCustomersList?limit=${limit}`);
    stopLoading();
    return response?.data;
  } catch (error) {
    stopLoading();
    console.log(error);
    return throwError({
      message: 'Failed to get Customer List',
    });
  }
};

const searchCustomer = async ({ name }) => {
  try {
    startLoading('Searching Customer...');
    const response = await axios.get(`/api/customers/searchCustomer?name=${name}`);
    stopLoading();
    return response?.data;
  } catch (error) {
    stopLoading();
    console.log(error);
    return throwError({
      message: 'Failed to Search For Customer',
    });
  }
};

const insertCustomer = async ({ accountId, dealId, contractId = null, operationId }) => {
  try {
    const contractIdQueryParam = contractId ? `&contractId=${contractId}` : '';
    startLoading('Completing the final touches... Just a moment.');
    const response = await axios.get(
      `/api/customers/insertCustomer?accountId=${accountId}&dealId=${dealId}&operationId=${operationId}${contractIdQueryParam}`,
    );
    stopLoading();
    return response?.data;
  } catch (error) {
    stopLoading();
    console.log(error);
    return throwError({
      message: 'Failed to insert Customer',
    });
  }
};

const getCustomerTimeline = async ({ customerId }) => {
  try {
    startLoading('Fetching Customer Details...');
    const response = await axios.get(`/api/customers/${customerId}/details`);
    stopLoading();
    return response?.data;
  } catch (error) {
    stopLoading();
    console.log(error);
    return throwError({
      message: 'Failed to Fetch Customer Details',
    });
  }
};

export default { insertCustomer, getCustomersList, searchCustomer, getCustomerTimeline };
