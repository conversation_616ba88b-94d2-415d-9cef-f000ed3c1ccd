import moment from 'moment';
import {
  hesAgentFormOptionsSelector,
  hesHcsAgentFormAutoCompleteOptionsSelector,
} from '@recoil/agents';
import { isEmpty } from 'lodash';
import {
  yesNoOptions,
  kAndTLocationsOptions,
  kAndTSignOptions,
  failedSpillageDetailsOptions,
  failedCSTHighCoDetailOptions,
  systemsFailingCoOptions,
  aboveAmbientCo35Options,
  confirmSystemTypeOptions,
  existingDHWoptions,
  hvacAgeOptions,
  flooringNailedOrUnsecuredOptions,
  flooringTypeOptions,
  gasAvailableOptions,
  contractorChoiceOptions,
  ktRemediationContractorChoiceOptions,
  ktDetailOptions,
  hvacInterestOptions,
  hvacStatusOptions,
  accuracyIssuesOptions,
  hvacQualityOptions,
  electricityProviderOptions,
  workScopeOptions,
  heatingSystemTypeOptions,
  returnVisitOptions,
  returnVisitCstStatusOptions,
  returnVisitCstDetailOptions,
  hesVisitResultDetailsOptions,
  paperworkSentEmailOptions,
} from '../consts';

const customerAuditorInfo = {
  customerName: {
    text: 'Customer Name',
    type: 'input',
    default: '',
    required: true,
  },
  customerAddress: {
    text: 'Address',
    type: 'address',
    default: '',
    required: true,
  },
  cityStateZip: {
    text: 'City, State, Zip',
    type: 'input',
    default: '',
    required: true,
  },
  customerPhone: {
    text: 'Phone',
    type: 'phone',
    default: '',
    required: true,
  },
  customerEmail: {
    text: 'Email',
    type: 'input',
    default: '',
    required: true,
  },
  siteId: {
    text: 'Site ID',
    type: 'input',
    default: '',
  },
  electricAccountNumber: {
    text: 'Electric Account Number',
    type: 'input',
    default: '',
  },
  gasAccountNumber: {
    text: 'Gas Account Number',
    type: 'input',
    default: '',
  },
  whatRegionIsThis: {
    text: 'Region',
    type: 'select',
    default: '',
    options: ['North Shore', 'South Shore', 'Metro West', 'Western MA', 'Cape Cod'],
  },
  auditDate: {
    text: 'Audit Date',
    type: 'date',
    default: undefined,
    useSyntheticEvent: true,
  },
  auditorName: {
    text: 'Auditor Name',
    type: 'input',
    default: '',
  },
  auditorEmail: {
    text: 'Auditor Email',
    type: 'input',
    default: '',
  },
  auditorCell: {
    text: 'Auditor Cell',
    type: 'phone',
    default: '',
  },
  leadVendor: {
    text: 'Lead Vendor',
    type: 'select',
    default: '',
    options: ['Abode', 'CleaResult', 'Rise', 'Rise on Cape'],
  },
  heaNotes: {
    text: 'HEA Notes',
    type: 'textarea',
    default: '',
  },
  intakeIssue: {
    text: 'Intake Accuracy Issue',
    type: 'select',
    default: '',
    options: accuracyIssuesOptions,
  },
  isCondo: {
    text: 'Condo ?',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  areasOnWorkscope: {
    default: [],
    text: 'Area on Workscope',
    type: 'multiselect',
    options: workScopeOptions,
  },
};

const customerAuditorInfoOfflineWr = {
  ...customerAuditorInfo,
  notesForCrew: {
    text: 'Notes For Crew',
    type: 'textarea',
    default: '',
  },
};

const hvacInfo = {
  interestedInHvac: {
    text: 'Interested in HVAC',
    type: 'select',
    default: '',
    options: hvacStatusOptions,
  },
  hvacQuality: {
    text: 'HES Ranking Quality of HVAC Appointment',
    type: 'select',
    default: '',
    options: hvacQualityOptions,
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  heaHvacNotes: {
    text: 'HEA HVAC Notes',
    type: 'textarea',
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  electricProvider: {
    text: 'Electricity Provider',
    type: 'select',
    options: electricityProviderOptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  hvacInterest: {
    text: 'HVAC Interest',
    type: 'select',
    options: hvacInterestOptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  gasAvailable: {
    text: 'Gas Available',
    type: 'select',
    options: gasAvailableOptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  hvacAge: {
    text: 'HVAC Age',
    type: 'select',
    options: hvacAgeOptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  existingDhw: {
    text: 'Existing DHW',
    type: 'select',
    options: existingDHWoptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
  confirmSystemType: {
    text: 'Confirm System Type:',
    type: 'multiselect',
    options: confirmSystemTypeOptions,
    default: '',
    conditional: (values) => {
      return (
        !isEmpty(values.interestedInHvac) && values?.interestedInHvac !== 'No - Out of Territory'
      );
    },
  },
};

const roadBlocksInfo = {
  yearHouseBuilt: {
    text: 'Year House Built',
    type: 'input',
    default: '',
  },
  disclosureWallsTempAccess: {
    text: 'Temp Access',
    type: 'select',
    default: '',
    options: yesNoOptions,
    required: true,
  },
};

const offlineRoadBlocksInfo = {
  yearHouseBuilt: {
    text: 'Year House Built',
    type: 'input',
    default: '',
  },
};

const asbestosInfo = {
  disclosureAsbestosShingles: {
    text: 'Shingles',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureAsbestosVermiculite: {
    text: 'Vermiculite',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  disclosureAsbestosWrappedPipes: {
    text: 'Wrapped Pipes/Ductwork',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureAsbestosInWorkArea: {
    text: 'Asbestos in work area',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const cstFailure = {
  disclosureCSTAmbientCO935: {
    text: 'Ambient CO 9-35ppm',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCSTAmbientCoAbove35: {
    text: 'Ambient CO above 35 ppm',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCSTAmbientCoDescription: {
    text: 'Enter Ambient CO Description Here:',
    type: 'select',
    default: '',
    options: aboveAmbientCo35Options,
    conditional: ({ disclosureCSTAmbientCoAbove35 }) => disclosureCSTAmbientCoAbove35 === 'Yes',
  },
  disclosureCstBrokenCorrodedVentPipes: {
    text: 'Broken, Corroded, or Detached Vent Pipes',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCSTChimneyHole: {
    text: 'Chimney (Hole)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstChimneyNotVented: {
    text: 'Chimney (Not Vented)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  cstFailedStatus: {
    text: 'Failed CST Status',
    type: 'select',
    default: '',
    options: [
      'System Replacement',
      'Internal Service Fix',
      'External Fix',
      'Retest',
      'Referred to Local CAP',
    ],
  },
  disclosureCstFailedCoAtmostphericSystem: {
    text: 'Failed CO Atmospheric System',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstFailedCoSealedSystem: {
    text: 'Failed CO Sealed System',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstHighCoDetail: {
    text: 'Failed CST High CO Detail (For Salesforce)',
    type: 'select',
    default: '',
    options: failedCSTHighCoDetailOptions,
    conditional: (values) =>
      values?.disclosureCstFailedCoAtmostphericSystem === 'Yes' ||
      values?.disclosureCstFailedCoSealedSystem === 'Yes',
  },
  systemsFailingCoMultiselect: {
    text: 'Systems failing CO?',
    type: 'multiselect',
    default: [],
    options: systemsFailingCoOptions,
    conditional: (values) =>
      values?.disclosureCstFailedCoAtmostphericSystem === 'Yes' ||
      values?.disclosureCstFailedCoSealedSystem === 'Yes',
  },
  heatingSystemCO: {
    text: 'Heating System CO Level',
    type: 'input',
    default: '',
    conditional: (values) => values?.systemsFailingCoMultiselect?.includes('Heating System'),
  },
  domesticHotWaterCarbonMonoxide: {
    text: 'DHW CO Level',
    type: 'input',
    default: '',
    conditional: (values) => values?.systemsFailingCoMultiselect?.includes('DHW'),
  },
  otherCombustionAppliance: {
    text: 'Other System CO Level',
    type: 'input',
    default: '',
    conditional: (values) => values?.systemsFailingCoMultiselect?.includes('Other'),
  },
  disclosureCstFailedCoGasOvenLevel1: {
    text: 'Failed CO Gas Oven (Level 1)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstFailedCoGasOvenLevel2: {
    text: 'Failed CO Gas Oven (Level 2)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  otherOvenCombustionAppliance: {
    text: 'Oven CO',
    type: 'input',
    default: '',
    conditional: (values) => values?.disclosureCstFailedCoGasOvenLevel2 === 'Yes',
  },
  disclosureCstFaileddraftAtmosphericNaturalConditions: {
    text: 'Failed Draft/Spillage Atmospheric System (Worst and Natural conditions)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCSTFailedSpillageSealedSystem: {
    text: 'Failed Spillage Sealed System',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstDraftSpillageDetail: {
    text: 'Failed CST Spillage Sealed System',
    type: 'select',
    default: '',
    options: aboveAmbientCo35Options,
    conditional: (values) => values?.disclosureCSTFailedSpillageSealedSystem === 'Yes',
  },
  failedDraftDetailMulti: {
    text: 'Failed Draft Detail:',
    type: 'multiselect',
    default: [],
    options: failedSpillageDetailsOptions,
    conditional: (values) => values?.disclosureCSTFailedSpillageSealedSystem === 'Yes',
  },
  failedSpillageDetailMulti: {
    text: 'Failed Spillage Detail:',
    type: 'multiselect',
    default: '',
    options: failedSpillageDetailsOptions,
    conditional: (values) => values?.disclosureCSTFailedSpillageSealedSystem === 'Yes',
  },
  failedCstOtherDraftSpillage: {
    text: 'What System Failed Draft/Spillage?',
    type: 'input',
    default: '',
    conditional: (values) => {
      const {
        failedSpillageDetailMulti,
        failedDraftDetailMulti,
        systemsFailingCoMultiselect,
      } = values;
      if (failedSpillageDetailMulti && failedSpillageDetailMulti.includes('Other')) return true;
      if (failedDraftDetailMulti && failedDraftDetailMulti.includes('Other')) return true;
      if (systemsFailingCoMultiselect && systemsFailingCoMultiselect.includes('Other')) return true;
      return false;
    },
  },
  disclosureCstFailedDraftAtmosphericSystemWorstCondition: {
    text: 'Failed Draft/Spillage Atmospheric System (Worst condition only)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstFailedNonOperationalCombustionGas: {
    text: 'Failed Non-Operational Combustion Gas System',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstHighCoSealedSystem100500ppm: {
    text: 'High CO Sealed System (100ppm-500ppm)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstMissingCODetector: {
    text: 'Missing CO Detector',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstOpenReturnInCombustionApplianceZone: {
    text: 'Open Return in the Combustion Appliance Zone',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCstGasOdor: {
    text: 'Significant Unburned Gas Odor',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const electrical = {
  disclosureElecKnobAntTubeActive: {
    text: 'Knob and Tube Wiring: Visible',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureElecKnobAndTubePossible: {
    text: 'Knob and Tube Wiring: Possible',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureElecOpenJunctionBoxes: {
    text: 'Open Junction Boxes',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  signsOfKTMultiselect: {
    text: 'If any K&T, must specify signs',
    type: 'multiselect',
    default: '',
    options: kAndTSignOptions,
  },
  locationsOfKTMultiselect: {
    text: 'If any K&T, must specify locations',
    type: 'multiselect',
    default: '',
    options: kAndTLocationsOptions,
  },
};

const moisture = {
  disclosureMoistureUnventedDryer: {
    text: 'Unvented Dryer',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoistureUnventedKitchenFan: {
    text: 'Unvented Kitchen Fan',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoistureUnventedStackPipe: {
    text: 'Unvented Stack Pipe',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoistureBulkMoisture: {
    text: 'Bulk Moisture',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoistureLedge: {
    text: 'Ledge',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const crawlSpace = {
  disclosureCrawlspaceLowHeadroom: {
    text: 'Low Headroom',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureCrawlspaceNoAccess: {
    text: 'No Access',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const mold = {
  isThereMold: {
    text: 'Any Mold?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoldAtticOver100sqFt: {
    text: 'Mold: Attic (Over 100 sq.ft.)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoldAtticUnder100sqFt: {
    text: 'Mold: Attic (Under 100 sq.ft.)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureMoldLivingSpace: {
    text: 'Mold: Living Space',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const other = {
  disclosureOtherAtticAirSealingRoadblock: {
    text: 'Attic Air Sealing Roadblock (60% rule)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureOtherInputDescription: {
    text: 'Other: Input Description',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureOtherDescription: {
    text: 'Yes, enter description here:',
    type: 'textarea',
    default: '',
  },
  disclosureOtherPestInfestation: {
    text: 'Other: Pest Infestation',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureOtherPipesInAttic: {
    text: 'Other: Pipes in Attic',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureHeInAttic: {
    text: 'Other: High Efficiency Heating System',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const pestInfestationRoofLeak = {
  pestInfestation: {
    text: 'Pest Infestation',
    type: 'checkbox',
    default: false,
  },
  roofLeak: {
    text: 'Roof Leak',
    type: 'checkbox',
    default: false,
  },
};

const structural = {
  disclosureStructuralBasementConcerns: {
    text: 'Basement Concerns',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralCeilingTilesDroppedCeiling: {
    text: 'Ceiling Tiles/Dropped Ceiling',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralFireDamage: {
    text: 'Fire Damage',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralGarageExposedToLivingSpace: {
    text: 'Garage exposed to Living Space',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralItegrityOfJoiseConstruction: {
    text: 'Integrity of Joist Construction',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralLargerThan24onCenterJoists: {
    text: 'Larger than 24” on Center Joists with No Supports',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralNailedInSheetrock: {
    text: 'Nailed in Sheetrock',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralNoSheathingWithBrickWalls: {
    text: 'No sheathing with Brick Walls',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  dislcosureStructuralSaggyRoof: {
    text: 'Saggy Roof',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralWebbingCracking: {
    text: 'Spider Webbing/Cracking',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  dislcosureStucturalWoodPainting: {
    text: 'Wood Paneling',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureStructuralWoodRot: {
    text: 'Wood Rot',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const wallsWorkDisclosures = {
  disclosureWallsShingles: {
    text: 'Shingles',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsWoodClapboards: {
    text: 'Wood Clapboards',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsVinylSiding: {
    text: 'Vinyl Siding',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsSquareChannelVinylSiding: {
    text: 'Square Channel Vinyl Siding',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsAluminumSiding: {
    text: 'Aluminum Siding',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsAsbestos: {
    text: 'Asbestos',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsAsphaltShingles: {
    text: 'Asphalt shingles',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsNeighborApproval: {
    text: 'Neighbor Approval',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsInteriorDrillAndBlow: {
    text: 'Interior Drill and Blow - Drywall',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsInteriorDrillAndBlowLateAndPlaster: {
    text: 'Interior Drill and Blow - Lathe and Plaster',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWallsGarageCeiling: {
    text: 'Garage Ceiling',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureLowHeadroom: {
    text: 'Low Headroom',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureWoodOverhang: {
    text: 'Wood Overhang',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  disclosureRidgeVent: {
    text: 'Ridge Vent',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const incentiveEligibilityAndAmounts = {
  incentiveWholeHouseIncentive: {
    text: 'Whole Building Incentive:',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  incentiveRenters: {
    text: 'Renters Incentive',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  incentiveAmtCST: {
    text: 'CST ($)',
    type: 'number',
    default: '0',
  },
  incentiveAmtKnobAndTube: {
    text: 'K&T Amount ($)',
    type: 'number',
    default: '0',
  },
  incentiveAmtICRated: {
    text: 'IC Rated Amount ($)',
    type: 'number',
    default: '0',
  },
  incentiveAmtDryerVent: {
    text: 'Dryer Vent Amount ($)',
    type: 'number',
    default: '0',
  },
  incentiveAmtAtticFloor: {
    text: 'Attic Floor Removal ($)',
    type: 'number',
    default: '0',
  },
};

const offlineWRIncentiveEligibilityAndAmounts = {
  incentiveWholeHouseIncentive: {
    text: 'Whole Building Incentive:',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  incentiveRenters: {
    text: 'Renters Incentive',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const massSaveWorkAmounts = {
  massSaveWorkAmtAirSealingNoSync: {
    text: 'Air Sealing ($)',
    type: 'number',
    default: '',
  },
  massSaveWorkAmtWeatherizationNoSync: {
    text: 'Weatherization ($)',
    type: 'number',
    default: '',
  },
  massSaveWorkAmtDuctSealingNoSync: {
    text: 'Duct Sealing ($)',
    type: 'number',
    default: '',
  },
  massSaveWorkAmtDuctInsulationNoSync: {
    text: 'Duct Insulation ($)',
    type: 'number',
    default: '',
  },
  massSaveWorkAmtDuctInsulationRemoval: {
    text: 'Duct Insulation Removal ($)',
    type: 'number',
    default: '',
  },
  massSaveWorkAmtInsulationRemoval: {
    text: 'Insulation Removal ($)',
    type: 'number',
    default: '',
  },
  massSaveProgramIncentive: {
    text: 'Mass Save Program Incentive ($)',
    type: 'number',
    default: '',
  },
  notesForCrew: {
    text: 'Notes For Crew',
    type: 'textarea',
    default: '',
  },
};

const flooringRemovalIncentive = {
  wxbiFloorRemovalSqFt: {
    text: 'Floor removal SqFt',
    type: 'input',
    default: '',
    required: true,
  },
  wxbiFlooringType: {
    text: 'Floor Typing',
    type: 'select',
    default: '',
    options: flooringTypeOptions,
    required: true,
  },
  wxbiNailedUnsecured: {
    text: 'Flooring Nailed or Unsecured',
    type: 'select',
    default: '',
    options: flooringNailedOrUnsecuredOptions,
    required: true,
  },
  wxbiCanBeDensePacked: {
    text: 'Can be dense packed?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
    required: true,
  },
};

const proposalTerms = {
  proposalTermsInsulationRemovalFrom: {
    text: 'Insulation Removal From:',
    type: 'input',
    default: '',
  },
  proposalTermsNotes: {
    text: 'Proposal Terms Notes:',
    type: 'textarea',
    default: '',
  },
};

const ventingCalculationFields = {
  atticSqFt: {
    text: '',
    type: 'input',
    default: 0,
  },
  ridgeVent: {
    text: '',
    type: 'input',
    default: 0,
  },
  eightRv: {
    text: '',
    type: 'input',
    default: 0,
  },
  twelveRv: {
    text: '',
    type: 'input',
    default: 0,
  },
  twelveTurbineVent: {
    text: '',
    type: 'input',
    default: 0,
  },
  customGvSqft: {
    text: '',
    type: 'input',
    default: 0,
  },
  twelveByTwelve: {
    text: '',
    type: 'input',
    default: 0,
  },
  twelveByEighteen: {
    text: '',
    type: 'input',
    default: 0,
  },
  twelveByTwentyfour: {
    text: '',
    type: 'input',
    default: 0,
  },
  fourBySixteen: {
    text: '',
    type: 'input',
    default: 0,
  },
  sixBySixteen: {
    text: '',
    type: 'input',
    default: 0,
  },
  eightBySixteen: {
    text: '',
    type: 'input',
    default: 0,
  },
  contSv: {
    text: '',
    type: 'input',
    default: 0,
  },
  dripedgeSv: {
    text: '',
    type: 'input',
    default: 0,
  },
  svPucks: {
    text: '',
    type: 'input',
    default: 0,
  },
  lowGvSqft: {
    text: '',
    type: 'input',
    default: 0,
  },
};

const basInformation = {
  heatingSystemType: {
    text: 'Heating System Type',
    type: 'select',
    default: '',
    options: heatingSystemTypeOptions,
  },
  houseAirConditioned: {
    text: 'Is House Air Conditioned?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  howManyStoriesIsHouse: {
    text: 'How Many Stories is the House?',
    type: 'select',
    default: '',
    options: ['1', '1.5', '2', '2.5', '3'],
  },
  squareFootage: {
    text: 'Square Footage',
    type: 'number',
    default: 0,
  },
  heightPerStory: {
    text: 'Average Height per Story',
    type: 'number',
    default: 0,
  },
  estimatedHouseVolume: {
    text: 'Estimated Volume',
    type: 'number',
    default: 0,
  },
  correctedHouseVolume: {
    text: 'Calculate the total correct volume',
    type: 'number',
    default: 0,
  },
  numberOfOccupants: {
    text: 'Number of Occupants',
    type: 'number',
    default: 0,
  },
  numberOfBedrooms: {
    text: 'Number of Bedrooms',
    type: 'number',
    default: 0,
  },
};

const inHouseChecks = {
  isMultifamily: {
    text: 'Multi-family house?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  asbestosWallWorkFromExterior: {
    text: 'Asbestos wall work from exterior?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  thirdFloorWallWork: {
    text: 'Third floor exterior wall work?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const dhwQuoting = {
  hesDhwVisitResult: {
    text: 'HES DHW Visit Result',
    type: 'select',
    default: '',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    options: [
      '',
      'Closed Won',
      'High Probability',
      'Closed Lost',
      'Incorrectly Closed Won',
      'ICW Fixed - Pending Review',
    ],
  },
  dhwHesEmp: {
    text: 'HES',
    type: 'select',
    recoilOptions: hesAgentFormOptionsSelector,
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    default: [],
  },
  hesDhwQuoteAmount: {
    text: 'HES DHW Quote Amount',
    type: 'input',
    default: '',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
  },
  hesDhwProductQuoted: {
    text: 'HES DHW Product Quoted',
    type: 'input',
    default: '',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    options: [
      '',
      'Navien On Demand',
      'Bradford White Hybrid Heat Pump 50 Gal',
      'Bradford White Hybrid Heat Pump 80 Gal',
      'AO Smith Direct Vent',
    ],
  },
  hesDhwFollowUpDate: {
    text: 'HES DHW Follow Up Date',
    type: 'date',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    default: undefined,
    useSyntheticEvent: true,
  },
  hesDhwOfficeInstallNotes: {
    text: 'HES DHW Office/Install Notes',
    type: 'textarea',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    default: '',
  },
  hesDhwIncorrectlyClosedwonNotes: {
    text: 'HES DHW Incorrectly Closed Won Notes',
    type: 'textarea',
    conditional: (values) => values.wasDhwQuoted === 'Yes',
    default: '',
  },
};

const goodSpecBadSpec = {
  anyPrewxSpec: {
    text: 'Any Pre-Wxs',
    type: 'input',
    default: '',
    readOnly: true,
  },
  atticSpec: {
    text: 'Attic Spec',
    type: 'input',
    default: '',
    readOnly: true,
  },
  crawlSpaceSpec: {
    text: 'Crawl Space Spec',
    type: 'input',
    default: '',
    readOnly: true,
  },
  kwSpec: {
    text: 'KW Spec',
    type: 'input',
    default: '',
    readOnly: true,
  },
  miscVentSpec: {
    text: 'Miscellaneous/Ventilation Spec',
    type: 'input',
    default: '',
    readOnly: true,
  },
  wallSpec: {
    text: 'Wall Spec',
    type: 'input',
    default: '',
    readOnly: true,
  },
  hesContestedBadSpec: {
    text: 'HES Contested Bad Spec?',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  badSpecFeedbackNotes: {
    text: 'Bad Spec Feedback Notes',
    type: 'textarea',
    default: '',
  },
  badSpecOverturnedArea: {
    text: 'Bad Spec Overturned Area',
    type: 'multiselect',
    default: '',
    options: [
      'Pre-Wxs',
      'Miscellaneous/Ventilation Spec',
      'Crawlspace Spec',
      'Garage Spec',
      'Wall Spec',
      'KW Spec',
      'Attic Spec',
    ],
  },
};

const bms = {
  bms1Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms1Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms1UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms2Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms2Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms2UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms3Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms3Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms3UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms4Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms4Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms4UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms5Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms5Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms5UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms6Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms6Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms6UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms7Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms7Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms7UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms8Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms8Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms8UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms9Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms9Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms9UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms10Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms10Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms10UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms11Item: {
    text: '',
    type: 'input',
    default: 0,
  },
  bms11Qty: {
    text: 0,
    type: 'input',
    default: 0,
  },
  bms11UnitPrice: {
    text: 0,
    type: 'input',
    default: 0,
  },
};

const returnVisitResulting = {
  returnVisitIsmStatus: {
    text: 'Return Visit ISM Status',
    type: 'select',
    default: '',
    options: returnVisitOptions,
  },
  newIsmReceiptUploaded: {
    text: 'New ISM Receipt Uploaded?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  returnVisitIsmPerformedDate: {
    text: 'Return Visit ISM Performed Date',
    type: 'date',
    default: undefined,
    useSyntheticEvent: true,
  },
  returnVisitIsmPerformedBy: {
    text: 'Return Visit ISM Performed By',
    type: 'autocomplete',
    recoilOptions: hesHcsAgentFormAutoCompleteOptionsSelector,
    default: '',
  },
  returnVisitCstStatus: {
    text: 'Return Visit CST Status',
    type: 'select',
    default: 'No',
    options: returnVisitCstStatusOptions,
  },
  returnVisitCstDetail: {
    text: 'Return Visit CST Detail',
    type: 'select',
    default: 'No',
    options: returnVisitCstDetailOptions,
  },
  newCstUploaded: {
    text: 'New CST Uploaded?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  cstUploadedTimestamp: {
    text: 'CST Uploaded Date',
    type: 'date',
    default: undefined,
    useSyntheticEvent: true,
  },
  returnVisitCstPerformedDate: {
    text: 'Return Visit CST Performed Date',
    type: 'date',
    default: undefined,
    useSyntheticEvent: true,
  },
  returnVisitCstPerformedBy: {
    text: 'Return Visit CST Performed By',
    type: 'autocomplete',
    recoilOptions: hesHcsAgentFormAutoCompleteOptionsSelector,
    default: '',
  },
};

const icRatedLightLocation = {
  bedrooms: {
    text: 'Bedrooms?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  kitchen: {
    text: 'Kitchen?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  livingRoom: {
    text: 'Living Room?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  bathroom: {
    text: 'Bathroom?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
};

const weatherization = {
  referredByAuditor: {
    text: 'Referred By Auditor',
    type: 'input',
    default: '',
  },
  hamAssignedToWxLead: {
    text: 'HAM Assigned to Wx Lead',
    type: 'input',
    default: '',
  },
  hamNotes: {
    text: 'HAM Notes',
    type: 'textarea',
    default: '',
  },
  hamWxStage: {
    text: 'HAM Wx Stage',
    type: 'input',
    default: '',
  },
  cxSaveStage: {
    text: 'Cx/Save Stage',
    type: 'input',
    default: '',
  },
  cxSaveNote: {
    text: 'Cx/Save Note',
    type: 'input',
    default: '',
  },
  insulationApprovalProcess: {
    text: 'Insulation Approval Process',
    type: 'input',
    default: '',
  },
  jobStatus: {
    text: 'Job Status',
    type: 'input',
    default: '',
  },
  jobStatusSpecNotes: {
    text: 'Job Status/Spec Notes',
    type: 'textarea',
    default: '',
  },
  scheduledInsulationStartDate: {
    text: 'Scheduled Insulation Start Date',
    type: 'date',
    default: undefined,
  },
  estInsulationCompletionDate: {
    text: 'EST Insulation Complete Date',
    type: 'date',
    default: undefined,
  },
  completionWalkDate: {
    text: 'Completion/Walk Date',
    type: 'date',
    default: undefined,
  },
  finalContractAmount: {
    text: 'Final Contract Amount',
    type: 'input',
    default: '',
  },
  finalBMSPrice: {
    text: 'Final BMS Price',
    type: 'input',
    default: '',
  },
  reasonForWalkPartialComplete: {
    text: 'Reason For Walk/Partial Complete',
    type: 'input',
    default: '',
  },
  insulationInvoicingStatus: {
    text: 'Insulation Invoicing Status',
    type: 'input',
    default: '',
  },
  insulationInvoicingNotes: {
    text: 'Insulation Invoicing Notes',
    type: 'textarea',
    default: '',
  },
};

const weatherizationHvac = {
  scheduledHvacInHouseHes: {
    text: 'Scheduled HVAC in House HES',
    type: 'input',
    default: '',
  },
  hvacStart: {
    text: 'HVAC Start',
    type: 'date',
    default: undefined,
  },
  visitResult: {
    text: 'Visit Result',
    type: 'input',
    default: '',
  },
  resultReason: {
    text: 'Result Reason',
    type: 'input',
    default: '',
  },
  resultDescription: {
    text: 'Result Description',
    type: 'input',
    default: '',
  },
  notesForOffice: {
    text: 'Notes For Office',
    type: 'textarea',
    default: '',
  },
  hcs: {
    text: 'HCS',
    type: 'select',
    recoilOptions: hesAgentFormOptionsSelector,
    default: '',
  },
  installContractPrice: {
    text: 'Install Contract Price',
    type: 'input',
    default: '',
  },
  finalContractPrice: {
    text: 'Final Contract Price',
    type: 'input',
    default: '',
  },
  status: {
    text: 'Status',
    type: 'input',
    default: '',
  },
  financingStage: {
    text: 'Financing Stage',
    type: 'input',
    default: '',
  },
  cancellationReason: {
    text: 'Cancellation Reason',
    type: 'input',
    default: '',
  },
  installCompletionDate: {
    text: 'Install Completion Date',
    type: 'date',
    default: undefined,
  },
};

const postHea = {
  preferredLanguage: {
    text: 'Preferred Language',
    type: 'select',
    options: ['English', 'Spanish', 'Portuguese', 'Chinese', 'Haitian Creole', 'Japanese'],
    default: '',
  },
  heaVisitResult: {
    text: 'HEA Visit Result',
    type: 'select',
    default: '',
    options: [
      '',
      'HEA Performed',
      'SHV Performed',
      'Audit Scheduled',
      'Reschedule Audit',
      'Audit Canceled',
    ],
  },
  hvacSalesResultDetail: {
    text: 'HVAC Sales Result Detail',
    type: 'select',
    default: '',
    options: [
      '',
      'HCA: Market Rate Appt Set',
      'HCA: Turnkey Quote Needed',
      'HCA: Supplemental DMS Call',
      'Qualified: No Appt Set',
      'Not Qualified: Low Income',
      'Not Qualified: No Opportunity',
    ],
    conditional: (values) =>
      values.heaVisitResult === 'HEA Performed' && !values.leadVendor.includes('CAP'),
  },
  closedWonHesEmp: {
    text: 'Closed Won HES',
    type: 'select',
    default: '',
    recoilOptions: hesAgentFormOptionsSelector,
    conditional: (values) => values.heaVisitResult === 'HEA Performed',
  },
  heaVisitResultDetail: {
    text: 'HEA Visit Result Detail',
    type: 'select',
    default: '',
    options: hesVisitResultDetailsOptions,
  },
  selfAttestStatus: {
    text: 'Self Attest Status',
    type: 'select',
    default: '',
    conditionalRequire: (value) => value.heaVisitResult === 'HEA Performed',
    options: [
      'Self Attest - Moderate Income',
      'Self Attest - Low Income',
      'Self Attest - Above Moderate Income',
    ],
  },
  electricProviderPostHea: {
    text: 'Electric Provider',
    type: 'select',
    default: '',
    options: ['', 'National Grid', 'Eversource', 'Municipal'],
  },
  houseAgeNew: {
    text: 'House Age(New)',
    type: 'number',
    default: 0,
  },
  wasDhwQuoted: {
    text: 'Did you quote DHW?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  interestedInHvacPm: {
    text: 'HVAC Tuneup Closed Won?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  tuneUpReferralNotes: {
    text: 'Tune-Up Referral Notes',
    type: 'textarea',
    default: '',
    conditional: (value) => value.interestedInHvacPm === 'Yes',
  },
  tuneUpSystems: {
    text: 'Tune Up Systems',
    type: 'select',
    default: '',
    options: [
      'Gas Furnace',
      'Propane Furnace',
      'Gas Furnace & AC',
      'Propane Furnace & AC',
      'Gas Boiler',
      'Propane Boiler',
      'Gas Combination Boiler',
      'Propane Combination Boiler',
      'Gas On Demand DHW',
      'Propane On Demand DHW',
      'Air Conditioning (Not DMS)',
    ],
    conditional: (value) => value.interestedInHvacPm === 'Yes',
  },
  collectedMoneyForInsulation: {
    text: 'Collected Money For Insulation',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
    conditional: (value) => value.heaVisitResultDetail === 'Closed Won',
  },
  collectedMoneyForInsulationDetails: {
    text: 'Collected Money For Insulation Details',
    type: 'select',
    default: '',
    options: [
      '',
      'Credit Card for $150 amount',
      'Check for $150 amount',
      'Credit Card for variable amount for remediation',
      'Check for variable amount for remediation',
      'Credit Card for $250 electrician inspection',
      'Check for $250 electrician inspection',
      'Check collected for less than $150',
      'Credit Card collected for less than $150',
      'Emailed payment link for $150',
      'Emailed payment link for less than $150',
    ],
    conditional: (value) => value.heaVisitResultDetail === 'Closed Won',
  },
  depositAmount: {
    text: 'Deposit Amount ($)',
    type: 'input',
    default: '',
  },
  preferredDowForInstall: {
    text: 'Preferred Dow For Install',
    type: 'multiselect',
    default: '',
    options: [
      'No Preference',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Last Minute Scheduling',
    ],
    conditional: (value) => value.heaVisitResultDetail === 'Closed Won',
  },
  notesForIsr: {
    text: 'Notes for ISR',
    type: 'textarea',
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['PreWeatherization Barrier', 'High Probability'].includes(value.heaVisitResultDetail),
  },
  customerConcernsPainPoints: {
    text: 'Customer Concerns Pain Points',
    type: 'multiselect',
    default: '',
    options: [
      'Comfort',
      'Savings/ROI',
      'Health and Safety',
      'Green Friendly',
      'Structural Concerns (ice dams)',
      'No Concerns Identified',
      'Other (Please explain in notes)',
    ],
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['PreWeatherization Barrier', 'High Probability'].includes(value.heaVisitResultDetail),
  },
  customerObjections: {
    text: 'Customer Objections',
    type: 'multiselect',
    default: '',
    options: [
      'Talk to spouse',
      'Need time to think',
      'Cannot afford',
      'Selling home',
      'Customer says they are too old',
      'Bad experience with previous company/contractor',
      'Says can do the work themselves',
      'Concerned with disturbing roof/attic/walls',
      'No objections identified',
      'Other (Please explain in notes)',
    ],
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['PreWeatherization Barrier', 'High Probability'].includes(value.heaVisitResultDetail),
  },
  qualifiedOutReason: {
    text: 'Qualified Out Reason',
    type: 'select',
    default: '',
    options: [
      '',
      '2012 or Newer',
      'Under Job Minimum',
      'All Areas Treated by Mass Save',
      'All Areas Treated with Spray Foam',
      'Low Headroom/Inaccessible',
      'House Style (Mobile Home)',
    ],
    conditional: (value) => value.heaVisitResultDetail === 'Qualified Out',
  },
  qualifiedOutDetail: {
    text: 'Qualified Out Details',
    type: 'textarea',
    default: '',
    conditional: (value) => value.heaVisitResultDetail === 'Qualified Out',
  },
  icwBy: {
    text: 'ICW By',
    type: 'input',
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['Incorrectly Closed Won', 'ICW Fixed- Pending Review'].includes(value.heaVisitResultDetail),
  },
  reasonForIncorrectlyCwByIam: {
    text: ' Reason for Incorrectly CW:',
    type: 'multiselect',
    options: [
      'LTA/Wavier Status Incorrect',
      'ARI Incomplete',
      'ARI Missing',
      'CIF Incomplete',
      'CIF Missing',
      'Contract Denied by LV',
      'Contract Incomplete',
      'Contract Missing',
      'CST Incomplete',
      'CST Missing',
      'Deposit Missing',
      'Disclosure Issue',
      'Disclosure Missing',
      'DSAV Incomplete',
      'DSAV Missing',
      'EM/E+/Energy Savvy Missing Auditor Inputs',
      'HL Incomplete',
      'HL Missing',
      'Incomplete -> not signed',
      'ISM Issue',
      'ISM Missing',
      'LPS Missing',
      'Missing All Starting Documents',
      'Miss Spec',
      'PAF Incomplete',
      'PAF Missing',
      'Proposal Term Incomplete',
      'Proposal Terms Missing',
      'PV Incomplete',
      'PV Missing',
      'PWBI Incomplete',
      'PWBI Missing',
      'Roadblock Issue',
      'Signed Permit App Missing',
      'WBI Incomplete',
      'WBI Missing',
      'Work Receipt Issue',
      'WR Missing',
    ],
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['Incorrectly Closed Won', 'ICW Fixed- Pending Review'].includes(value.heaVisitResultDetail),
  },
  incorrectlyCwNotes: {
    text: 'Incorrectly CW Notes',
    type: 'textarea',
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['Incorrectly Closed Won', 'ICW Fixed- Pending Review'].includes(value.heaVisitResultDetail),
  },
  tsIncorrectlyCwByLv: {
    text: 'Timestamp Incorrectly Closed Won by Lead Vendor',
    type: 'input',
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['Incorrectly Closed Won', 'ICW Fixed- Pending Review'].includes(value.heaVisitResultDetail),
  },
  tsIncorrectlyCwByIam: {
    text: 'Timestamp Incorrectly Closed Won By IAM',
    type: 'input',
    default: '',
    conditional: (value) =>
      value.heaVisitResultDetail &&
      ['Incorrectly Closed Won', 'ICW Fixed- Pending Review'].includes(value.heaVisitResultDetail),
  },
  transferToHam: {
    text: 'Transfer To HAM',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  paperworkEmailSent: {
    text: 'Paperwork Email Sent?',
    type: 'select',
    default: '',
    options: paperworkSentEmailOptions,
  },
  heaInvoicingProcess: {
    text: 'HEA Invoicing Process',
    type: 'select',
    default: '',
    options: [
      '',
      'Invoice Submitted',
      'Incorrect Payment Received',
      'HEA Payment Received in Full',
      'Invoice Rejected',
      'Unable to Invoice',
      'Need to Confirm Payment',
      'HEA Fail',
      'HEA QC Failed Fixed Pending Review',
      'Appliance Visit Locked',
      'AMP Locked 1st Attempt',
      'AMP Locked 2nd Attempt',
      'AMP Locked 3rd Attempt',
      'Unable to Invoice - Hancock Issue (Temp)',
    ],
    conditional: (value) =>
      value.heaInvoicingProcess &&
      ['HEA Fail', 'HEA QC Failed Fixed Pending Review'].includes(value.heaInvoicingProcess),
  },
  heaQcFail: {
    text: 'HEA QC Fail:',
    type: 'multiselect',
    default: '',
    options: [
      'E+/Uplight Pass',
      'E+/Uplight Fail',
      'Missing Documents',
      'Incomplete/Not Signed',
      'ISM Issue',
      'Disclosure Issue',
      'Pictures',
      'API Fail',
      'API Fail - Ready for HES',
      'Return Visit Fail',
      'LTA/Waiver Missing',
    ],
    conditional: (value) =>
      value.heaInvoicingProcess &&
      ['HEA Fail', 'HEA QC Failed Fixed Pending Review'].includes(value.heaInvoicingProcess),
  },
  heaQcFailNotes: {
    text: 'HEA QC Fail Notes',
    type: 'textarea',
    default: '',
    conditional: (value) =>
      value.heaInvoicingProcess &&
      ['HEA Fail', 'HEA QC Failed Fixed Pending Review'].includes(value.heaInvoicingProcess),
  },
  singleOrMultiFamily: {
    text: 'Single-Family or Multi-Family?',
    type: 'select',
    default: '',
    options: ['', 'Condo 5+', 'Multi-Family', 'Single-Family'],
  },
  occupantType: {
    text: 'Occupant Type?',
    type: 'select',
    default: '',
    options: ['', 'Owner', 'Renter'],
    conditional: (value) => value.singleOrMultiFamily === 'Multi-Family',
  },
  unitsInBuilding: {
    text: 'Units In Building?',
    type: 'select',
    default: '',
    options: ['', '1', '2', '3', '4', '5+'],
    conditional: (value) => value.singleOrMultiFamily === 'Multi-Family',
  },
  allQualifiedUnitsPerformed: {
    text: 'All Qualified Units Performed?',
    type: 'select',
    default: '',
    options: [
      '',
      'No (Other Units Need Scheduling)',
      'Yes (All Units Performed)',
      'Yes (Other Unit(s) Not Qualified)',
    ],
    conditional: (value) => value.singleOrMultiFamily === 'Multi-Family',
  },
  landlordInvolved: {
    text: 'Landlord Involved?',
    type: 'select',
    default: '',
    options: ['', 'Yes', 'No', 'Undetermined'],
    conditional: (value) => value.singleOrMultiFamily === 'Multi-Family',
  },
  massSaveWorkAmtAirSealing: {
    text: 'Air Sealing ($)',
    type: 'number',
    default: 0,
  },
  massSaveWorkAmtWeatherization: {
    text: 'Weatherization ($)',
    type: 'number',
    default: 0,
  },
  massSaveWorkAmtDuctSealing: {
    text: 'Duct Sealing ($)',
    type: 'number',
    default: 0,
  },
  massSaveWorkAmtDuctInsulation: {
    text: 'Duct Insulation ($)',
    type: 'number',
    default: 0,
  },
  bmsTotal: {
    text: 'BMS Total',
    type: 'number',
    default: 0,
  },
  contractDoesntRequireBarrierCleared: {
    text: "Contract Doesn't Require Barrier Cleared",
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  prewxBarrierCleared: {
    text: 'PreWeatherization Barrier Cleared',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  VermiculiteRemContractorChoiceC: {
    text: 'If any vermiculite, must specify contractor choice',
    type: 'select',
    default: '',
    options: contractorChoiceOptions,
    conditional: (value) => value.disclosureAsbestosVermiculite === 'Yes',
  },
  MoldRemediationContractorChoiceC: {
    text: 'If any mold, must specify contractor choice',
    type: 'select',
    default: 'No',
    options: contractorChoiceOptions,
    conditional: (value) => value.isThereMold === 'Yes',
  },
  KTDetail: {
    text: 'HES K&T Detail',
    type: 'select',
    default: '',
    options: ktDetailOptions,
    conditional: (value) => value.disclosureElecKnobAntTubeActive === 'Yes',
  },
  KTRemediationContractorChoiceC: {
    text: 'If any K&T, must specify contractor choice',
    type: 'select',
    default: '',
    options: ktRemediationContractorChoiceOptions,
    conditional: (value) => value.disclosureElecKnobAntTubeActive === 'Yes',
  },
};

export const extraFields = {
  uplightDump: {
    text: '',
    default: false,
  },
  wxOpportunityId: {
    text: '',
    default: false,
  },
  hvacInstallOpportunityId: {
    text: '',
    default: false,
  },
  finalBmsPrice: {
    text: '',
    default: false,
  },
  unitNumber: {
    text: '',
    default: false,
  },
  sfDealId: {
    text: '',
    default: false,
  },
  accountId: {
    text: '',
    default: false,
  },
  isDealLoaded: {
    text: '',
    default: false,
  },
  hesEmployees: {
    text: '',
    default: false,
  },
  hasReturnVisit: {
    text: '',
    default: false,
  },
  allUnitsSameInfo: {
    text: '',
    default: false,
  },
  unit: {
    text: '',
    default: 1,
  },
  highVentingTotal: {
    text: '',
    default: 0,
  },
  lowVentingTotal: {
    text: '',
    default: 0,
  },
  balanced: {
    text: '',
    default: 0,
  },
  hiLo: {
    text: '',
    default: 0,
  },
  ridgeVent_rt: {
    text: '',
    default: 0,
  },
  eightRv_rt: {
    text: '',
    default: 0,
  },
  twelveRv_rt: {
    text: '',
    default: 0,
  },
  twelveTurbineVent_rt: {
    text: '',
    default: 0,
  },
  customGvSqft_rt: {
    text: '',
    default: 0,
  },
  twelveByTwelve_rt: {
    text: '',
    default: 0,
  },
  twelveByEighteen_rt: {
    text: '',
    default: 0,
  },
  twelveByTwentyfour_rt: {
    text: '',
    default: 0,
  },
  fourBySixteen_rt: {
    text: '',
    default: 0,
  },
  sixBySixteen_rt: {
    text: '',
    default: 0,
  },
  eightBySixteen_rt: {
    text: '',
    default: 0,
  },
  contSv_rt: {
    text: '',
    default: 0,
  },
  dripedgeSv_rt: {
    text: '',
    default: 0,
  },
  svPucks_rt: {
    text: '',
    default: 0,
  },
  lowGvSqft_rt: {
    text: '',
    default: 0,
  },
  leadSource: {
    text: '',
    default: '',
  },
  customBms1Description: {
    text: '',
    default: '',
  },
  customBms1Qty: {
    text: '',
    default: 0,
  },
  customBms1UnitPrice: {
    text: '',
    default: 0,
  },
  customBms2Description: {
    text: '',
    default: '',
  },
  customBms2Qty: {
    text: '',
    default: 0,
  },
  customBms2UnitPrice: {
    text: '',
    default: 0,
  },
  customBms1Description2: {
    text: '',
    default: '',
  },
  customBms1Qty2: {
    text: '',
    default: 0,
  },
  customBms1UnitPrice2: {
    text: '',
    default: 0,
  },
  customBms2Description2: {
    text: '',
    default: '',
  },
  customBms2Qty2: {
    text: '',
    default: 0,
  },
  customBms2UnitPrice2: {
    text: '',
    default: 0,
  },
  customBms1Description3: {
    text: '',
    default: '',
  },
  customBms1Qty3: {
    text: '',
    default: 0,
  },
  customBms1UnitPrice3: {
    text: '',
    default: 0,
  },
  customBms2Description3: {
    text: '',
    default: '',
  },
  customBms2Qty3: {
    text: '',
    default: 0,
  },
  customBms2UnitPrice3: {
    text: '',
    default: 0,
  },
  customBms1Description4: {
    text: '',
    default: '',
  },
  customBms1Qty4: {
    text: '',
    default: 0,
  },
  customBms1UnitPrice4: {
    text: '',
    default: 0,
  },
  customBms2Description4: {
    text: '',
    default: '',
  },
  customBms2Qty4: {
    text: '',
    default: 0,
  },
  customBms2UnitPrice4: {
    text: '',
    default: 0,
  },
  hesVisitResultDetailsOptions: {
    text: '',
    default: [],
  },
  paperworkSentEmailOptions: {
    text: '',
    default: [],
  },
  docRepoStatus: {
    text: '',
    default: 0,
  },
};

export const defaultPrintSection = {
  printHCReminder: 'No',
  hcaDate: moment().format('MM/DD/YYYY'),
  hcaTime: moment().format('hh:mm:ss A'),
  hcaDms: 'No',
  hcaBoiler: 'No',
  hcaFurnace: 'No',
  hcaAc: 'No',
  hcaHeatPump: 'No',
  printPermitDoc: 'Yes',
  printProposalTerms: 'Yes',
  printIsmDoc: 'No',
  printCifDoc: 'No',
  printModerateIncomeFlyer: 'No',
};

export const offlineWRFormSchema = {
  customerAuditorInfoOfflineWr,
  hvacInfo,
  offlineRoadBlocksInfo,
  asbestosInfo,
  cstFailure,
  electrical,
  moisture,
  crawlSpace,
  mold,
  other,
  pestInfestationRoofLeak,
  structural,
  proposalTerms,
  offlineWRIncentiveEligibilityAndAmounts,
  basInformation,
  postHea,
  extraFields,
  returnVisitResulting,
  goodSpecBadSpec,
  weatherization,
  weatherizationHvac,
};

export const formSchema = {
  customerAuditorInfo,
  hvacInfo,
  roadBlocksInfo,
  asbestosInfo,
  cstFailure,
  electrical,
  moisture,
  crawlSpace,
  mold,
  other,
  pestInfestationRoofLeak,
  structural,
  wallsWorkDisclosures,
  proposalTerms,
  incentiveEligibilityAndAmounts,
  massSaveWorkAmounts,
  flooringRemovalIncentive,
  icRatedLightLocation,
  ventingCalculationFields,
  basInformation,
  inHouseChecks,
  dhwQuoting,
  goodSpecBadSpec,
  bms,
  returnVisitResulting,
  weatherization,
  weatherizationHvac,
  postHea,
  defaultPrintSection,
  extraFields,
};

export const flatOfflineWRFormSchema = Object.keys(offlineWRFormSchema).reduce((acc, key) => {
  return { ...acc, ...offlineWRFormSchema[key] };
}, {});

export const flatFormSchema = {
  ...customerAuditorInfo,
  ...hvacInfo,
  ...roadBlocksInfo,
  ...asbestosInfo,
  ...cstFailure,
  ...electrical,
  ...moisture,
  ...crawlSpace,
  ...mold,
  ...other,
  ...pestInfestationRoofLeak,
  ...structural,
  ...wallsWorkDisclosures,
  ...proposalTerms,
  ...incentiveEligibilityAndAmounts,
  ...massSaveWorkAmounts,
  ...flooringRemovalIncentive,
  ...icRatedLightLocation,
  ...ventingCalculationFields,
  ...basInformation,
  ...inHouseChecks,
  ...dhwQuoting,
  ...bms,
  ...returnVisitResulting,
  ...goodSpecBadSpec,
  ...weatherization,
  ...weatherizationHvac,
  ...postHea,
  ...extraFields,
};

export const roadBlocksMap = {
  ...roadBlocksInfo,
  ...asbestosInfo,
  ...cstFailure,
  ...electrical,
  ...moisture,
  ...crawlSpace,
  ...mold,
  ...other,
  ...pestInfestationRoofLeak,
  ...structural,
  ...wallsWorkDisclosures,
};
