import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { Row, Col, FormRadioButtons } from '@components/global/Form';
import { Header } from '@components/global';

const Container = styled.div``;

const NoSlotsMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const RadioSlots = ({ title, name, slotsToDisplay, handleChange, selectedSlot }) => {
  return (
    <Container>
      <Row>
        <Col size={1}>
          {slotsToDisplay?.length === 0 ? (
            <Container>
              <Header h3>{title}</Header>
              <NoSlotsMessage>
                {title}: No slots available. Please reset your search query.
              </NoSlotsMessage>
            </Container>
          ) : (
            <FormRadioButtons
              type="object"
              name={name}
              title={title}
              value={selectedSlot}
              options={slotsToDisplay}
              onChange={handleChange}
            />
          )}
        </Col>
      </Row>
    </Container>
  );
};

RadioSlots.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  slotsToDisplay: PropTypes.arrayOf(PropTypes.shape({})),
  handleChange: PropTypes.func.isRequired,
  selectedSlot: PropTypes.oneOfType([PropTypes.shape({}), PropTypes.string]),
};

RadioSlots.defaultProps = {
  slotsToDisplay: [],
  selectedSlot: {},
};

export default RadioSlots;
