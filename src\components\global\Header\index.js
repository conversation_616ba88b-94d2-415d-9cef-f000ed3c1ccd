import React from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';

const StyledHeader = styled.div`
  color: ${({ color }) => {
    return color;
  }};
  padding: 8px 0;
  ${({ h1, h2, h3, h4, h5, weight, marginBottom, theme }) => {
    switch (true) {
      case h1:
        return `font-size: ${theme.fontSizes.h1}`;
      case h2:
        return `font-size: ${theme.fontSizes.h2}; font-weight: ${weight}; margin-bottom: ${marginBottom} `;
      case h3:
        return `font-size: ${theme.fontSizes.h3}; font-weight: ${weight};`;
      case h4:
        return `font-size: ${theme.fontSizes.h4}`;
      case h5:
        return `font-size: ${theme.fontSizes.h5}`;
      default:
        return '';
    }
  }};
`;

const Header = ({ h1, h2, h3, h4, h5, weight, marginBottom, children, color }) => {
  const theme = useTheme();
  const colorToUse = color || theme.primary[400];
  return (
    <StyledHeader
      h1={h1}
      h2={h2}
      h3={h3}
      h4={h4}
      h5={h5}
      weight={weight}
      marginBottom={marginBottom}
      color={colorToUse}
    >
      {children}
    </StyledHeader>
  );
};

Header.propTypes = {
  h1: PropTypes.bool,
  h2: PropTypes.bool,
  h3: PropTypes.bool,
  h4: PropTypes.bool,
  h5: PropTypes.bool,
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.any)]).isRequired,
  color: PropTypes.string,
  marginBottom: PropTypes.string,
  weight: PropTypes.number,
};
Header.defaultProps = {
  h1: false,
  h2: false,
  h3: false,
  h4: false,
  h5: false,
  marginBottom: 'unset',
  weight: 400,
  color: '',
};

export default Header;
