import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Refresh } from '@styled-icons/boxicons-regular';

import { eventChanged } from '@utils/EventEmitter';

const RefreshButtonWrapper = styled.button`
  width: 30px;
  height: 30px;
  background: ${({ theme }) => theme.secondary[100]};
  color: ${({ theme }) => theme.colors.eventGreen};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  padding: 0;
  margin-left: 10px;
  text-align: center;
`;

const RefreshButton = ({ onClick }) => (
  <RefreshButtonWrapper onClick={() => (onClick ? onClick() : eventChanged())}>
    <Refresh size={30} />
  </RefreshButtonWrapper>
);

RefreshButton.propTypes = {
  onClick: PropTypes.func,
};
RefreshButton.defaultProps = {
  onClick: null,
};

export default RefreshButton;
