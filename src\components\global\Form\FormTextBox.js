import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import Form<PERSON>ieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';

const FormTextBoxContent = styled.textarea`
  height: 96px;
  width: 100%;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  box-sizing: border-box;
  border-radius: 4px;
  padding: 2%;
`;

const FormTextBox = ({
  title,
  placeholder,
  readOnly,
  required,
  name,
  value,
  compact,
  onChange,
  weight,
}) => {
  return (
    <FormFieldContainer required={required} fieldName={name} compact={compact}>
      <FormFieldLabel weight={weight}>{title}</FormFieldLabel>
      <FormTextBoxContent
        readOnly={readOnly || false}
        placeholder={placeholder}
        onChange={onChange}
        name={name}
        value={value || ''} // '`value` prop on `textarea` should not be null. Consider using an empty string to clear the component'
      />
    </FormFieldContainer>
  );
};

FormTextBox.propTypes = {
  title: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  onChange: PropTypes.func,
  name: PropTypes.string,
  value: PropTypes.string,
  compact: PropTypes.bool,
  weight: PropTypes.number,
};

FormTextBox.defaultProps = {
  readOnly: false,
  required: false,
  onChange: () => {},
  name: '',
  value: '',
  placeholder: '',
  compact: false,
  weight: 500,
};

export default FormTextBox;
