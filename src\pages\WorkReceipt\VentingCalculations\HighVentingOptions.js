import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { uniqueId } from 'lodash';
import { Table, TableHeader, TableCell, TableRow } from './TableCellsColors';
import { ventingOptions } from '../consts';

export const HighVentingOptions = memo(({ atticSqFt, highVentingTotal }) => {
  const calculateCellValue = ({ ratio, value, price }) =>
    value || Math.max(Math.ceil(((atticSqFt / 300) * ratio - highVentingTotal) / price), 0);
  return (
    <Table>
      <thead>
        <TableRow>
          <TableHeader colSpan="7">High Venting Options</TableHeader>
        </TableRow>
      </thead>
      <thead>
        <TableRow>
          <TableHeader />
          <TableHeader>Ridge Vent</TableHeader>
          <TableHeader>12&apos;&apos; RV</TableHeader>
          <TableHeader>8&apos;&apos; RV</TableHeader>
          <TableHeader>High 12 x 18 Gable</TableHeader>
          <TableHeader>Turbine</TableHeader>
        </TableRow>
      </thead>
      <tbody>
        {ventingOptions.highVentingOptions.map((option, idx) => (
          <TableRow key={uniqueId()}>
            <TableCell
              whiteSpace="nowrap"
              backgroundColor={option.color}
              fontWeight={idx < 3 ? 900 : 300}
            >
              {option.name}
            </TableCell>
            {option.calcs.map((item) => (
              <TableCell
                whiteSpace="nowrap"
                backgroundColor={item.color}
                fontWeight={idx < 3 ? 900 : 300}
                key={uniqueId()}
              >
                {calculateCellValue(item)}
              </TableCell>
            ))}
          </TableRow>
        ))}
        <TableRow>
          <TableCell backgroundColor="rgba(255, 0, 0, .8)" fontWeight={300}>
            High 0%
          </TableCell>
          <TableCell backgroundColor="rgba(0, 128, 0, .2)" fontWeight={300}>
            3
          </TableCell>
          <TableCell backgroundColor="rgba(0, 128, 0, .2)" fontWeight={300}>
            1
          </TableCell>
          <TableCell backgroundColor="rgba(0, 128, 0, .2)" fontWeight={300}>
            1
          </TableCell>
          <TableCell fontWeight={300}>1</TableCell>
          <TableCell fontWeight={300}>1</TableCell>
        </TableRow>
      </tbody>
    </Table>
  );
});

HighVentingOptions.propTypes = {
  atticSqFt: PropTypes.number,
  highVentingTotal: PropTypes.number,
};

HighVentingOptions.defaultProps = {
  atticSqFt: 0,
  highVentingTotal: 0,
};
