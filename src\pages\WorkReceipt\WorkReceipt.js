import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { getDataIntakeFormValues } from '@components/DataIntakeForm/dataIntakeFormHelpers';
import {
  capWorkReceiptValuesState,
  offlineWorkReceiptValuesState,
  workreceiptModeState,
  workReceiptValuesState,
} from '@recoil/workreceipt';
import { useRecoilState, useResetRecoilState, useSetRecoilState } from 'recoil';
import { formValuesState, activeFormState, resetFormRecoilState } from '@recoil/dataIntakeForm';
import { activeTabIndexAtom } from '@recoil/app';
import styled from 'styled-components';
import { CancelButton as ResetButton, PrimaryButton } from '@components/global/Buttons';
import { WorkReceiptManager, EventsManager, UtilityManager } from '@utils/APIManager';
import { PageHeader } from '@pages/Components';
import { hesAgentFormOptionsState, hesHcsAgentsFormAutoCompleteState } from '@recoil/agents';
import { FormInput, FormSelect, Row, Col } from '@components/global/Form';
import Swal from 'sweetalert2/dist/sweetalert2';
import { startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import { urlParamsToJson, getAlphanumericString, openNewTabWithUrl } from '@utils/functions';
import { ArrowBarLeft } from '@styled-icons/bootstrap/ArrowBarLeft';
import { ArrowBarRight } from '@styled-icons/bootstrap/ArrowBarRight';
import SidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import SidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SidebarBody from '@components/EventSidebar/EventSidebarBody';
import { cloneDeep } from 'lodash';
import { defaultPrintSection, extraFields } from './FormSchema/formSchema';
import { failedDraftMap, failedSpillageMap, workReceiptModeOptions, unitOptions } from './consts';
import { workReceiptValidations } from './FormSchema/validation';
import { MarketRateWorkReceipt } from './MarketRateWR/MarketRateWorkReceipt';
import { OfflineWorkReceipt } from './OfflineWR/OfflineWorkReceipt';
import { useRoadblocksDisclosures } from './MarketRateWR/useRoadblocksDisclosures';
import { useCapRoadblocksDisclosures } from './CAPWR/useCapRoadblocksDisclosures';
import { CapWorkReceipt } from './CAPWR/CapWorkReceipt';

const Card = styled.div`
  display: flex;
  background: #ffffff;
  border-radius: 5px;
  cursor: pointer;
  border-right: ${(props) => (props.isActive ? '5px solid green' : 'none')};
  max-width: 95%;
`;

const Box = styled.div``;

const Container = styled.div`
  position: relative;
  height: calc(100% - 44px);
`;

const FormSelectContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 24px;
`;

const ActionsContainer = styled.span`
  position: absolute;
  top: 50%;
  right: 2%;
`;

const ArrowLeftIcon = styled(ArrowBarLeft)`
  visibility: visible;
  color: ${({ theme }) => theme.colors.eventGreen};
  width: 40px;
  height: 40px;
  transition: none;
`;

const ArrowRightIcon = styled(ArrowBarRight)`
  visibility: visible;
  color: ${({ theme }) => theme.colors.eventGreen};
  width: 40px;
  height: 40px;
  transition: none;
`;

const ContainerWrapper = styled.nav`
  position: fixed;
  left: 100%;
  top: 0;
  display: ${({ show }) => (show ? 'unset' : 'none')};
  width: 100%;
  height: 100%;
  transition: transform 300ms;
  transform: ${({ show }) => (show ? 'translateX(-100%)' : 'translateX(3em)')};
  background-color: ${({ theme }) => theme.colors.transparent};
  z-index: 999;
`;

const SidebarDetailContainer = styled.div`
  position: absolute;
  display: flex;
  flex-direction: column;
  right: 0;
  min-width: 320px;
  width: 50%;
  height: 100%;
  background-color: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 2px 34px ${({ theme }) => theme.colors.greyShadow};

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptop)} {
    width: 75%;
  }

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    width: 95%;
  }

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.mobileL)} {
    width: 100%;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  opacity: 1;
  border: none;
  width: 35px;
  height: 35px;
  left: -3em;
  top: 0;
  border-radius: 50%;
  color: ${({ theme }) => theme.secondary[400]};
  background: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 7px 12px ${({ theme }) => theme.colors.greyShadow};
`;

const Button = styled(PrimaryButton)`
  margin-left: 5%;
  width: 20%;
  align-self: center;
`;

const Actions = ({ showSidebar, handleSideBarClick }) => {
  return (
    <ActionsContainer>
      {showSidebar ? (
        <ArrowRightIcon onClick={handleSideBarClick} />
      ) : (
        <ArrowLeftIcon onClick={handleSideBarClick} />
      )}
    </ActionsContainer>
  );
};

const WorkReceiptTypesMap = {
  'Online-Multi-Family': MarketRateWorkReceipt,
  'Offline-Multi-Family': MarketRateWorkReceipt,
  Offline: OfflineWorkReceipt,
  CAP: CapWorkReceipt,
};

const WorkReceipt = (props) => {
  const { location } = props;
  const queryParams = location?.search;
  const { dealsParams, isCap, isMulti = false, isOffline = false } = urlParamsToJson(queryParams);
  const defaultDeals = dealsParams?.split('-') || [''];
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const resetFormState = useResetRecoilState(resetFormRecoilState);
  const resetOnlineWorkReceipt = useResetRecoilState(workReceiptValuesState);
  const resetOfflineWorkReceipt = useResetRecoilState(offlineWorkReceiptValuesState);
  const resetCapWorkReceipt = useResetRecoilState(capWorkReceiptValuesState);
  const [activeForm, setActiveForm] = useRecoilState(activeFormState);
  const setActiveTabIndex = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const [deals, setDeals] = useState([...defaultDeals]);
  const [hesEmpIdMap, setHesEmpIdMap] = useState({});
  const [unitNumbers, setUnitNumbers] = useState([]);
  const [siteIds, setSiteIds] = useState([]);
  const [isDealLoaded, setIsDealLoaded] = useState(false);
  const [unit, setUnit] = useState(deals.length || 1);
  const [workReceiptMode, setWorkReceiptMode] = useRecoilState(workreceiptModeState);
  const [showSidebar, setShowSidebar] = useState(false);
  const isOfflineWR = workReceiptMode === 'Offline';
  const { heightPerStory, squareFootage, otherCombustionAppliance, otherOvenCombustionAppliance } =
    formValues[activeForm] || {};
  const setHesAgents = useSetRecoilState(hesAgentFormOptionsState);
  const setHcsAgents = useSetRecoilState(hesHcsAgentsFormAutoCompleteState);

  const updateRoadBlocksFieldsForExcelSheet = useRoadblocksDisclosures();
  const updateCapRoadblockValuesForExcelSheet = useCapRoadblocksDisclosures();

  const WorkReceiptBusinessTypeMap = {
    Online: updateRoadBlocksFieldsForExcelSheet,
    CAP: updateCapRoadblockValuesForExcelSheet,
  };

  const handleWorkReceiptMode = (event) => {
    if (isDealLoaded) {
      return Swal.fire({
        icon: 'error',
        title: 'Work Receipt Mode Locked',
        text:
          'You cannot change the work receipt mode after the deal information is fetched. To modify it, please reset the form.',
      });
    }

    if (event.target.value === 'Offline') {
      const dealList = deals;
      setDeals(dealList.splice(0, 1));
      setUnit(1);
    }
    return setWorkReceiptMode(event.target.value);
  };

  const handleSideBarClick = useCallback(() => {
    setShowSidebar((prevState) => !prevState);
  }, []);

  const updateFieldValues = (values, index = null) => {
    setFormValues({ ...values, index: index || activeForm });
  };

  const handleUnitChange = useCallback(
    (e) => {
      if (isDealLoaded) {
        return Swal.fire({
          icon: 'error',
          title: 'Unit Change Restricted',
          text:
            'You cannot change the unit number after deal information is loaded. Please reset the form to make changes.',
        });
      }

      if (isOfflineWR && e.target.value > 1) {
        return Swal.fire({
          icon: 'error',
          title: 'Single Unit Restriction',
          text: 'Offline mode only supports a single-family unit. Please use only unit 1',
        });
      }

      if (isDealLoaded && Number(e.target.value) < unit) {
        setActiveForm(Number(e.target.value) - 1);
      }
      let dealIdsCopy = [...deals];
      if (e.target.value > unit) {
        const changes = new Array(Number(e.target.value) - Number(unit)).fill('');
        dealIdsCopy = dealIdsCopy.concat(changes);
      } else {
        dealIdsCopy = dealIdsCopy.slice(0, e.target.value);
      }
      setDeals(dealIdsCopy);
      setUnit(e.target.value);
      return false;
    },
    [isOfflineWR, isDealLoaded, unit, deals],
  );

  const handleCardClick = (index) => {
    if (activeForm === index) return;
    if (isDealLoaded) {
      startLoading(`Loading form details for Unit ${index + 1}. Almost there!`);
      setActiveForm(index);
      setTimeout(() => {
        stopLoading();
      }, 1000);
    }
  };

  const validateDealIds = (dealList) => {
    const errorMessages = [];
    const seenDeals = new Set();

    dealList.forEach((dealId, index) => {
      const dealNumber = index + 1;

      if (!dealId) {
        errorMessages.push(`Deal ${dealNumber} is empty`);
        return;
      }

      if (dealId.length !== 15 && dealId.length !== 18) {
        errorMessages.push(
          `Invalid Deal ID length at Deal ${index + 1}, Deal ID should be 15 or 18 digits`,
        );
      }

      if (workReceiptMode === 'Online-Multi-Family' && dealList.length === 1) {
        errorMessages.push(
          'Multi Family mode is selected. Please provide more than one deal ID. Otherwise select "Offline" to proceed',
        );
      }

      if (workReceiptMode === 'Offline-Multi-Family' && dealList.length === 1) {
        errorMessages.push(
          'Multi Family mode is selected. Please provide more than one deal ID. Otherwise select "Offline" to proceed',
        );
      }

      if (seenDeals.has(dealId)) {
        errorMessages.push(`Duplicate Deal ID at Deal ${dealNumber}`);
        return;
      }

      seenDeals.add(dealId);
    });
    return errorMessages;
  };

  const loadDeals = useCallback(async () => {
    try {
      const errorMessages = validateDealIds(deals);
      if (errorMessages.length > 0) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Errors',
          text: errorMessages.join(', '),
        });
        return false;
      }
      // Select and AutoComplete not synced, Autocomplete I think I should refeactor this and discuss with salesforce
      // to use lookups or only string names for the drop down. for 3 4 fields we have to create diff selectors and variables
      let hesEmployees = [];
      let hcsEmployees = [];
      let hesEmployeesNames = [];
      let hesEmployeesIdMap = [];
      const workReceiptResponse = [];
      const siteIdsList = [];
      const unitNumbersList = [];
      let hasError = false;
      for (let iterator = 0; iterator < deals.length; iterator++) {
        const dealId = deals[iterator];
        // eslint-disable-next-line no-await-in-loop
        const dealResponse = await WorkReceiptManager.getDealDetailsForWorkReceipt(dealId);
        if (!dealResponse) {
          hasError = true;
          break;
        }
        const data = dealResponse?.dbInsert;
        hesEmployeesNames = dealResponse?.hesEmployees?.hesEmployeesNames || [];
        hesEmployees = dealResponse?.hesEmployees?.hesEmployeesListByValueAndName || [];
        hcsEmployees = dealResponse?.hcsEmployees?.hcsEmployeesListByValueAndName || [];
        hesEmployeesIdMap = dealResponse?.hesEmployees?.hesEmployeeIdDictByName || [];
        siteIdsList.push(data?.siteId);
        unitNumbersList.push(data?.unitNumber);
        const response = {
          ...getDataIntakeFormValues(extraFields),
          ...data,
          ...defaultPrintSection,
        };
        workReceiptResponse.push(response);
      }
      if (hasError) {
        return false;
      }
      setHesEmpIdMap(hesEmployeesIdMap);
      setUnitNumbers(unitNumbersList);
      setSiteIds(siteIdsList);
      setIsDealLoaded(true);
      setHesAgents(hesEmployeesNames);
      setHcsAgents([...hesEmployees, ...hcsEmployees]);
      setFormValues(workReceiptResponse);
      setActiveForm(0);
      return workReceiptResponse;
    } catch (error) {
      console.log('error', error);
      return throwError(error);
    }
  }, [deals, workReceiptMode]);

  const mapEmployeeIds = (formValues) => {
    if (isDealLoaded) {
      const result = formValues.map((item, formIndex) => {
        const values = { ...item };
        if (formValues[formIndex]?.closedWonHesEmp) {
          values.closedWonHesEmpId = hesEmpIdMap[formValues[formIndex].closedWonHesEmp];
        }
        if (formValues[formIndex]?.icwBy) {
          values.icwByEmpId = hesEmpIdMap[formValues[formIndex].icwBy];
        }
        if (formValues[formIndex]?.icwFixedBy) {
          values.icwFixedByEmpId = hesEmpIdMap[formValues[formIndex].icwFixedBy];
        }
        return values;
      });

      return result;
    }
    return formValues;
  };

  const removeSalesforceNoneValues = (updatedFormValues) => {
    const values = [...updatedFormValues];
    for (let iterator = 0; iterator < updatedFormValues.length; iterator++) {
      Object.entries(values[iterator]).forEach(([key, value]) => {
        if (value === '--None--') {
          values[iterator][key] = '';
        }
      });
    }
    return values;
  };

  const createAreaOnWorkScopeMap = (areasOnWorkscopeList, formIndex) => {
    if (!areasOnWorkscopeList) return {};
    const getIndexBaseKey = (value) => {
      const parsedValue = value.replace(' ', '_');
      const isMultiFamilyLoaded = formValues.length > 1;
      const postFix = isMultiFamilyLoaded ? '_1' : '';
      return `${parsedValue}${postFix}`;
    };
    const map = {};
    const areasOnWorkScopeMap = {
      'Attic Floor': 'No',
      'Attic Wall': 'No',
      'Attic Slope': 'No',
      'Exterior Wall': 'No',
      Basement: 'No',
      Crawlspace: 'No',
      'Garage Ceiling': 'No',
      Kneewall: 'No',
    };
    areasOnWorkscopeList.forEach((workscope) => {
      if (areasOnWorkScopeMap[workscope]) {
        map[getIndexBaseKey(workscope, formIndex)] = 'Yes';
      }
    });
    return map;
  };

  const generateValuesForAreaOnWorkScope = (formValues) => {
    const result = formValues.map((item, formIndex) => {
      return { ...item, ...createAreaOnWorkScopeMap(item?.areasOnWorkscope, formIndex) };
    });
    return result;
  };

  /**
   * This function is responsible for generating new keys for failing systems for the Marketing Rate Excel Sheet,
   * specifically for spillage and draft conditions in the work receipt form values for single-family sheet generation.
   *
   * It first checks if there is more than one work receipt field; if so, it returns the fields as-is.
   * Otherwise, it deep clones the `workReceiptFields` array to avoid mutating the original data.
   *
   * The function then combines details from the `failedDraftDetailMulti` and `failedSpillageDetailMulti` arrays
   * in the work receipt by mapping them to predefined keys (`failedDraftMap` and `failedSpillageMap`).
   *
   * For each detail in the draft and spillage arrays, it creates a new key in the `combinedDetails` object
   * with a value of "Yes", indicating a failure in that particular system.
   *
   * Finally, it updates the first element of `fieldsForSheet` with the combined details,
   * effectively adding the newly generated keys for draft and spillage failures to the work receipt.
   *
   * The output will be similar to the following:
   *
   * 'Heating_System_Draft': 'Yes'
   * 'DHW_Draft': 'Yes'
   * 'Other_Draft': 'Yes'
   *
   * 'Heating_System_Spillage': 'Yes'
   * 'DHW_Spillage': 'Yes'
   * 'Other_Spillage': 'Yes'
   */

  const mapFailingDraftAndSpillage = (workReceiptFields = []) => {
    if (workReceiptFields.length > 1) return workReceiptFields;
    const fieldsForSheet = cloneDeep(workReceiptFields);
    const [workReceipt] = workReceiptFields;
    const combinedDetails = {};

    const mapDetails = (detailArray = [], map) => {
      if (!detailArray) return false;
      detailArray.forEach((item) => {
        const fieldName = map[item];
        if (fieldName) {
          combinedDetails[fieldName] = 'Yes';
        }
      });
      return detailArray;
    };
    mapDetails(workReceipt?.failedDraftDetailMulti, failedDraftMap);
    mapDetails(workReceipt?.failedSpillageDetailMulti, failedSpillageMap);

    fieldsForSheet[0] = {
      ...workReceipt,
      ...combinedDetails,
    };

    return fieldsForSheet;
  };

  const handleDealsInput = (value, index) => {
    const dealsCopy = [...deals];
    dealsCopy[index] = value;
    setDeals(dealsCopy);
  };

  const generateExcelSheet = async () => {
    const validation = workReceiptValidations(workReceiptMode);
    const isFormValid = validation(formValues, deals);
    if (!isFormValid) return false;
    let workReceiptValues = mapEmployeeIds(formValues);
    const updatedValues = removeSalesforceNoneValues(workReceiptValues);
    const savedWR = await WorkReceiptManager.saveWorkReceipt(updatedValues);
    if (!savedWR) return false;
    workReceiptValues = mapFailingDraftAndSpillage(workReceiptValues);
    workReceiptValues = generateValuesForAreaOnWorkScope(workReceiptValues);
    const businessType = WorkReceiptBusinessTypeMap[workReceiptMode];
    if (businessType) workReceiptValues = businessType(workReceiptValues);
    const key = await WorkReceiptManager.generateWorkReceipt(workReceiptValues, workReceiptMode);
    if (!key) return false;
    let s3URL = `https://s3.amazonaws.com/workreceipt-test/MA/${workReceiptMode}/`;
    const host = window.location.origin;
    if (host === 'https://sch.homeworksenergy.com')
      s3URL = `https://s3.amazonaws.com/workreceipt/MA/${workReceiptMode}/`;
    const downloadUrl = s3URL + key;
    await Swal.fire({
      icon: 'success',
      title: 'Work Receipt Generated',
      html: `Download link: <a href='${downloadUrl}'>Work Receipt</a>`,
    });
    return key;
  };

  const handleSaveWorkReceipt = async () => {
    try {
      const workReceiptValues = mapEmployeeIds(formValues);
      const updatedValues = removeSalesforceNoneValues(workReceiptValues);
      return WorkReceiptManager.saveWorkReceipt(updatedValues);
    } catch (error) {
      return throwError(error);
    }
  };
  const resetAllFormState = useCallback(() => {
    resetFormState();
    resetOnlineWorkReceipt();
    resetOfflineWorkReceipt();
    resetCapWorkReceipt();
  }, []);

  const handleReset = useCallback(async () => {
    const { value: confirmed } = await Swal.fire({
      title: 'Are you sure want to reset the forms ?',
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return false;
    resetAllFormState();
    setDeals(['']);
    setIsDealLoaded(false);
    setUnit(1);
    setSiteIds([]);
    return setWorkReceiptMode('Offline');
  }, []);

  const renderInputLabels = useCallback(
    (index) => {
      const columnLabel = (
        <>
          <Box>Unit: {unitNumbers[index]}</Box>
          <Box>Site ID: {siteIds[index]}</Box>
        </>
      );
      return isDealLoaded ? columnLabel : `Deal ${index + 1}`;
    },
    [unitNumbers, siteIds],
  );

  const handleReferral = (dealId) => {
    let referralUrl;
    const currentHost = window.location.origin;
    switch (currentHost) {
      case 'https://sch.homeworksenergy.com':
        referralUrl = `https://hes.homeworksenergy.com/docRepo/referrals/${dealId}`;
        break;
      default:
        referralUrl = `https://ts02.homeworksenergy.com/docRepo/referrals/${dealId}`;
        break;
    }
    return window.open(referralUrl, '_blank');
  };

  const handleSearch = async (searchTerm, departmentType) => {
    const searchResults = await EventsManager.searchEvents({
      searchTerm: getAlphanumericString(searchTerm),
      fieldsToSearch: 'type',
      departmentEventTypes: [isOfflineWR ? departmentType : '0000'],
    });
    if (searchResults.length === 0 || searchResults.length > 1)
      return Swal.fire({
        type: 'error',
        title: 'Failed to get Doc Repo Link',
        text: 'No Event or Multiple events showing.',
      });

    const [
      {
        customerName,
        leadVendor,
        numUnit,
        sfIds: { opportunityId },
        accountId,
      },
    ] = searchResults;
    if (departmentType === '0001') {
      const url = isOfflineWR
        ? `doc-repo/MA/HVAC_Sales/${opportunityId}`
        : `view-doc-repo/MA/HEA-CAP/${accountId}`;
      return openNewTabWithUrl(url);
    }

    return UtilityManager.openDocRepo(searchTerm, customerName, leadVendor, numUnit);
  };

  useEffect(() => {
    if (Number(heightPerStory >= 0) && Number(squareFootage >= 0)) {
      updateFieldValues({
        estimatedHouseVolume: parseFloat(squareFootage * heightPerStory).toFixed(2),
      });
    }
  }, [heightPerStory, squareFootage]);

  useEffect(() => {
    if (otherCombustionAppliance) {
      updateFieldValues({
        otherOvenCombustionAppliance: otherCombustionAppliance,
      });
    }
  }, [otherCombustionAppliance]);

  useEffect(() => {
    if (otherOvenCombustionAppliance) {
      updateFieldValues({
        otherCombustionAppliance: otherOvenCombustionAppliance,
      });
    }
  }, [otherOvenCombustionAppliance]);

  useEffect(() => {
    setActiveForm(0);
    return () => {
      setActiveForm(null);
      resetAllFormState();
    };
  }, []);

  useEffect(() => {
    setActiveTabIndex(0);
  }, [activeForm, workReceiptMode]);

  useEffect(() => {
    if (isCap) {
      setWorkReceiptMode('CAP');
    }
    if (isOffline && isMulti) {
      setWorkReceiptMode('Offline-Multi-Family');
    }
    if (isOffline && !isMulti) {
      setWorkReceiptMode('Offline');
    }
  }, []);

  useEffect(() => {
    if (defaultDeals.length && defaultDeals[0] !== '') {
      setShowSidebar(true);
    }
  }, []);

  const WorkReceiptType = WorkReceiptTypesMap[workReceiptMode];
  return (
    <>
      <Container>
        <Box>
          <PageHeader>Result HEA Visit</PageHeader>
        </Box>
        <WorkReceiptType isDealLoaded={isDealLoaded} />
        <Actions showSidebar={showSidebar} handleSideBarClick={handleSideBarClick} />
      </Container>

      <ContainerWrapper show={showSidebar}>
        <SidebarDetailContainer
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <SidebarHeader>
            <HeaderLabel>
              Select the number of units from the dropdown list before loading the deals. Once the
              deals are loaded, you will not be able to change the number of units or switch from
              online to offline receipt mode without first clicking Reset.
            </HeaderLabel>

            <HeaderLabel>
              When multiple deals are loaded, the data from the first deal will automatically
              populate the forms. To view information for a different deal, click on the
              corresponding deal card.
            </HeaderLabel>

            <FormSelectContainer>
              <FormSelect
                required
                title="Number of Units"
                name="numberOfUnits"
                value={Number(unit)}
                onChange={handleUnitChange}
                options={unitOptions}
                customWidth={320}
              />
              <FormSelect
                required
                title="Workreceipt Mode"
                name="workreceiptMode"
                value={workReceiptMode}
                onChange={handleWorkReceiptMode}
                options={workReceiptModeOptions}
                customWidth={320}
              />
            </FormSelectContainer>
          </SidebarHeader>

          <SidebarBody>
            <CloseButton onClick={handleSideBarClick}>x</CloseButton>
            {deals?.map((_, index) => (
              <Row>
                <Col>
                  <Card
                    key={`deal-${unitOptions[index]}`}
                    isActive={activeForm === index && isDealLoaded}
                    onClick={() => handleCardClick(index)}
                  >
                    <FormInput
                      placeholder="Enter Deal Id..."
                      title={renderInputLabels(index)}
                      value={deals?.[index]}
                      onChange={(e) => handleDealsInput(e.target.value, index)}
                      maxLength={18}
                      allowCopy
                    />
                    <Button disabled={!isDealLoaded} onClick={() => handleReferral(deals?.[index])}>
                      Referral
                    </Button>
                    <Button
                      disabled={!isDealLoaded}
                      onClick={() => handleSearch(deals?.[index], '0001')}
                    >
                      {isOfflineWR ? 'HVAC Doc Repo' : 'Doc Repo'}
                    </Button>
                    {isOfflineWR && (
                      <Button
                        disabled={!isDealLoaded}
                        onClick={() => handleSearch(deals?.[index], '0000')}
                      >
                        Doc Repo
                      </Button>
                    )}
                  </Card>
                </Col>
              </Row>
            ))}
          </SidebarBody>

          <SidebarFooter
            leftButtons={
              <ResetButton disabled={!isDealLoaded} onClick={handleReset}>
                Reset
              </ResetButton>
            }
            rightButtons={
              <>
                {deals?.length > 0 && (
                  <PrimaryButton onClick={loadDeals}>Load Deal(s)</PrimaryButton>
                )}
                <PrimaryButton disabled={!isDealLoaded} onClick={generateExcelSheet}>
                  Save & Generate WorkReceipt
                </PrimaryButton>
                <PrimaryButton disabled={!isDealLoaded} onClick={handleSaveWorkReceipt}>
                  Save WorkReceipt
                </PrimaryButton>
              </>
            }
          />
        </SidebarDetailContainer>
      </ContainerWrapper>
    </>
  );
};

WorkReceipt.defaultProps = {
  location: '',
};

WorkReceipt.propTypes = {
  location: PropTypes.shape({
    search: PropTypes.string,
  }),
};

Actions.defaultProps = {
  showSidebar: false,
  handleSideBarClick: () => {},
};

Actions.propTypes = {
  showSidebar: PropTypes.bool,
  handleSideBarClick: PropTypes.func,
};

export default WorkReceipt;
