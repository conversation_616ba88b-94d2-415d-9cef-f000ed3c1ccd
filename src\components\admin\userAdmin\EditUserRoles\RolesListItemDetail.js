// External Dependencies
import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';

// Internal Utils
import { getAuthorizedDepartments } from '@utils/AuthUtils';
import { UsersManager, UtilityManager } from '@utils/APIManager';

// Recoil State
import { useInvalidateSelector } from '@recoil/hooks';
import { statesSelector } from '@recoil/app';
import { selectedUserState, selectedUserRoleState } from '@recoil/admin/users';
import { refreshAdminListState } from '@recoil/admin';

// Components
import ListItemDetail from '@components/global/ScreenPartitionView/ListItemDetail';
import { Container, FormSelect, FormRadioButtons } from '@components/global/Form';
import { PrimaryButton } from '@components/global/Buttons';

// Main Component
const RolesListItemDetail = ({ userRoles, allRoles }) => {
  const refreshUsers = useInvalidateSelector(refreshAdminListState);

  const selectedUser = useRecoilValue(selectedUserState);
  const statesOptions = useRecoilValue(statesSelector);
  const [selectedRole, setSelectedRole] = useRecoilState(selectedUserRoleState);

  // Internal State
  const [unassignedDepartments, setUnassignedDepartments] = useState([]);
  const [unassignedDepartmentOptions, setUnassignedDepartmentOptions] = useState([]);

  const roleOptions = allRoles.map(({ id: roleId, role: roleName }) => {
    return { key: roleName, value: roleId };
  });

  const {
    department: departmentName,
    roleId: selectedRoleId,
    departmentId: selectedDepartmentId,
    stateAbbr: selectedState,
  } = selectedRole;

  useEffect(() => {
    if (userRoles[0]) setSelectedRole(userRoles[0]);
    getDepartments();
  }, [getDepartments, setSelectedRole, userRoles]);

  useEffect(() => {
    const unassignedDepartmentOptions = unassignedDepartments
      .filter(({ stateAbbr }) => {
        return selectedState === stateAbbr;
      })
      .map(({ id: value, department: key }) => {
        return { key, value };
      });
    setUnassignedDepartmentOptions(unassignedDepartmentOptions);
  }, [selectedState]);

  const isAddRole = !userRoles.find((existingRole) => {
    return (
      selectedRole.departmentId === existingRole.departmentId &&
      selectedState === existingRole.stateAbbr
    );
  });

  const authorizedDepartments = getAuthorizedDepartments();

  const getDepartments = useCallback(async () => {
    const departments = await UtilityManager.getAllDepartments();
    const deptAssignedToUser = userRoles.map(({ department, state }) => {
      return { department, state };
    });

    const dropDownDept = [];
    departments.forEach(({ id, department }) => {
      authorizedDepartments
        .map(({ department, state, stateAbbr }) => {
          return { department, state, stateAbbr };
        })
        .filter(({ department: authDept, state: authState, stateAbbr }) => {
          const authDeptAssigned = deptAssignedToUser.find(({ state, department }) => {
            return state === authState && department === authDept;
          });
          if (authDept === department && !authDeptAssigned)
            dropDownDept.push({ id, department: authDept, state: authState, stateAbbr });
          return authDept === department && !authDeptAssigned;
        });
    });
    setUnassignedDepartments(dropDownDept);
  }, [userRoles, authorizedDepartments]);

  const updateRoles = async () => {
    const response = await UsersManager.updateUserRoles(selectedUser.oid, selectedRole);
    if (!response) return false;

    refreshUsers();

    return true;
  };

  const handleRoleChange = (newRoleId) => {
    const role = allRoles.find((role) => role.id === newRoleId)?.role;
    setSelectedRole({ ...selectedRole, roleId: newRoleId, role, state: selectedRole.stateAbbr });
  };

  const handleStateChange = (newStateId) => {
    setSelectedRole({ ...selectedRole, stateAbbr: newStateId, state: newStateId });
  };

  const handleDepartmentChange = (newDepartmentId) => {
    const department = unassignedDepartments.find(
      (department) => department.departmentId === newDepartmentId,
    )?.department;
    setSelectedRole({ ...selectedRole, departmentId: newDepartmentId, department });
  };

  return (
    <ListItemDetail
      listItems={userRoles}
      selectedItem={selectedUser}
      setSelectedItem={setSelectedRole}
      onSaveButtonClick={updateRoles}
      headerText={departmentName || 'Add new permissions'}
      detailText={!isAddRole ? selectedRole.role : ''}
      showFooter={false}
    >
      <Container>
        <FormSelect
          required
          title="States"
          placeholder="Select States"
          name="stateAbbr"
          value={selectedState}
          onChange={(e) => handleStateChange(e.target.value)}
          options={statesOptions}
        />
        {isAddRole && (
          <FormSelect
            required
            title="Department"
            placeholder="Select Department"
            name="department"
            type="number"
            value={selectedDepartmentId}
            onChange={(e) => handleDepartmentChange(e.target.value)}
            options={unassignedDepartmentOptions}
          />
        )}
        <FormRadioButtons
          name="role"
          title="Role"
          type="number"
          required
          options={roleOptions}
          value={selectedRoleId}
          onChange={(e) => handleRoleChange(e.target.value)}
        />
        <PrimaryButton onClick={updateRoles}>
          {isAddRole ? 'Add Role' : 'Update Role'}
        </PrimaryButton>
      </Container>
    </ListItemDetail>
  );
};

RolesListItemDetail.propTypes = {
  selectedRole: PropTypes.shape({
    role: PropTypes.string,
    roleId: PropTypes.number,
    department: PropTypes.string,
    departmentId: PropTypes.number,
    stateAbbr: PropTypes.string,
  }),
  userRoles: PropTypes.arrayOf(PropTypes.shape({ role: PropTypes.string })).isRequired,
  allRoles: PropTypes.arrayOf(PropTypes.shape({ id: PropTypes.number, role: PropTypes.string }))
    .isRequired,
};

RolesListItemDetail.defaultProps = {
  selectedRole: null,
};

export default RolesListItemDetail;
