/* eslint-disable indent */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Spinner from 'react-bootstrap/Spinner';
import {
  addStartLoadingListener,
  addStopLoadingListener,
  addErrorListener,
} from '@utils/EventEmitter';
import { Wrapper } from './LoadingIndicator.styles';

const LoadingIndicator = ({ loading, message, fullscreen: globalIndicator }) => {
  const [isLoading, setIsLoading] = useState(loading);
  const [loadingMessage, setLoadingMessage] = useState(message);

  useEffect(() => {
    // If this is a globalIndicator, set the event listeners. Otherwise, return.
    if (globalIndicator === false) return () => {};
    const removeStartLoadingListener = addStartLoadingListener(startLoading);
    const removeStopLoadingListener = addStopLoadingListener(stopLoading);
    const removeErrorListener = addErrorListener(stopLoading);
    return () => {
      removeStartLoadingListener();
      removeStopLoadingListener();
      removeErrorListener();
    };
  }, [globalIndicator]);

  // For global loading indicator functionality
  const startLoading = (message) => {
    setLoadingMessage(message);
    setIsLoading(true);
  };
  const stopLoading = () => {
    setIsLoading(false);
  };

  const displayTheIndicator = isLoading;

  return displayTheIndicator ? (
    // There's no actual interaction here, just disabling outside clicks.
    <Wrapper fullscreen={globalIndicator}>
      {/* eslint-disable-next-line */}
      <div
        className="loading-indicator"
        onClick={(event) => {
          event.preventDefault();
          event.stopPropagation();
        }}
      >
        <div className="overlay" />
        <div className="loading-container">
          <h2 className="loading-message">{loadingMessage}</h2>
          <Spinner className="" animation="border" />
        </div>
      </div>
    </Wrapper>
  ) : null;
};

LoadingIndicator.propTypes = {
  loading: PropTypes.bool,
  message: PropTypes.string,
  fullscreen: PropTypes.bool,
};

LoadingIndicator.defaultProps = {
  loading: false,
  message: '',
  fullscreen: true,
};

export default LoadingIndicator;
