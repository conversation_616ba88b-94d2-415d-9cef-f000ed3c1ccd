import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';

// Need to forward ref from drag and drop
// otherwise ref gets lost (not passed down to child component) and DND doesn't work
// I think this is due to using styled(Clickable) in the Event component
const Clickable = forwardRef((props, ref) => {
  const { onClick, children, className, onMouseEnter, onMouseLeave } = props;
  const handleKeyDown = (e) => {
    if (e.keyCode === 13 || e.keyCode === 32) onClick();
  };
  return (
    <div
      className={className}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      tabIndex={0}
      style={{ cursor: 'pointer' }}
      role="button"
      ref={ref}
    >
      {children}
    </div>
  );
});

Clickable.propTypes = {
  className: PropTypes.string,
  onClick: PropTypes.func,
  children: PropTypes.node.isRequired,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
};

Clickable.defaultProps = {
  className: null,
  onClick: () => {},
  onMouseEnter: null,
  onMouseLeave: null,
};

export default Clickable;
