import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';
import _ from 'lodash';
import { UtilityManager } from '@utils/APIManager';
import { formValuesState, activeFormState } from '@recoil/dataIntakeForm';
import Swal from 'sweetalert2/dist/sweetalert2';
import { Checkbox } from '@components/global';
import {
  FormRadioButtons,
  FormSelect,
  FormInput,
  FormDatePicker,
  FormDateTimePicker,
  FormTextBox,
  FormMultiselect,
  FormNumberInput,
  FormReadOnly,
  GoogleAddressInput,
  FormInputPhoneNumber,
  FormCampaignID,
} from '@components/global/Form';
import { parseGoogleAutocomplete } from '@utils/functions';
import moment from 'moment';
import FormAutoComplete from '@components/global/Form/FormAutoComplete';
import DataIntakeFormSection from './DataIntakeFormSection';

const componentMap = {
  select: FormSelect,
  checkbox: Checkbox,
  input: FormInput,
  date: FormDatePicker,
  time: FormDateTimePicker,
  textarea: FormTextBox,
  multiselect: FormMultiselect,
  radio: FormRadioButtons,
  number: FormNumberInput,
  readonly: FormReadOnly,
  section: DataIntakeFormSection,
  address: GoogleAddressInput,
  phone: FormInputPhoneNumber,
  campaignId: FormCampaignID,
  autocomplete: FormAutoComplete,
};

const DataIntakeFormElement = ({
  name: fieldName,
  type: fieldType,
  text: originalText,
  required,
  perUnit,
  unitNum,
  conditional,
  validation,
  conditionalText,
  conditionalRequire,
  conditionalSetter,
  api,
  style,
  secondaryText,
  useSyntheticEvent,
  conditionalOptions,
  ...otherProps
}) => {
  const activeForm = useRecoilValue(activeFormState);
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const [isAddressInvalid, setIsAddressInvalid] = useState(false);
  const perUnitIndex = unitNum - 1;
  const remainingProps = otherProps;

  const text = conditionalText ? conditionalText(formValues, perUnitIndex) : originalText;

  const handleFormValueChange = async (event, perUnitIndex, perUnit) => {
    const { name: fieldName, value: newValue } = event.target;
    let newValues = {};
    // The Conditional Setter is passed from the formSchema. It allows us to make changes on other fields if certain conditional dependencies are met.
    // For example, if the "Information on All Unit" field is a checkbox,
    // we can set the fields related to the "Information Unit" field within the Conditional Setter.

    // This allows us to set specific actions or modifications when the condition evaluates to true.
    const doesFieldHaveConditionalSetter =
      conditionalSetter && conditionalSetter(formValues, newValue, perUnitIndex);
    if (doesFieldHaveConditionalSetter) {
      const result = conditionalSetter(formValues, newValue, perUnitIndex);
      // if a field is having conditionalSetter on FormSchema which means dependant similar fields
      // also need to be updated if one field is updated
      // if response type if object then use this otherwise continue to singular setter
      const isResultTypeObject = typeof result === 'object' && !(result instanceof Array);
      if (isResultTypeObject) {
        return setFormValues((prevState) => ({
          ...prevState,
          ...result,
        }));
      }
      return setFormValues((prevState) => ({
        ...prevState,
        [fieldName]: result,
      }));
    }

    // Form Setter for PerUnit Fields which are in [].
    // perUnit is true if a form field falls in Unit Related Questions
    // For example if 3 units we have then means 3 unit numbers, 3 electric or gas provider
    // so thats why use index based setter
    if (perUnit) {
      const formState = _.cloneDeep(formValues);
      formState[fieldName][perUnitIndex] = newValue;
      return setFormValues(formState);
    }

    if (api) {
      const response = await api(newValues[fieldName], formValues);
      newValues = { ...newValues, ...response };
    }
    newValues[fieldName] = newValue;
    if (activeForm >= 0) {
      return setFormValues(() => ({
        ...newValues,
        index: activeForm,
      }));
    }
    // This setter is usually used for plain form values, field which are not conditionalSetter or perUnit
    // like Preferred Language Field.
    return setFormValues((prevFormValues) => ({
      ...prevFormValues,
      ...newValues,
    }));
  };

  const resetAddressField = useCallback(() => {
    const newValues = _.cloneDeep(formValues);
    newValues[fieldName] = '';
    setFormValues((prevState) => ({
      ...prevState,
      [fieldName]: '',
    }));
  }, [formValues, fieldName, setFormValues]);

  useEffect(() => {
    if (isAddressInvalid) {
      resetAddressField();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddressInvalid]);

  const raiseError = async () => {
    setIsAddressInvalid(true);
    Swal.fire({
      icon: 'error',
      title: 'Invalid Zip Code!',
    });
  };

  const handleGoogleLocationInput = async (autocomplete) => {
    try {
      const parsedAddress = parseGoogleAutocomplete(autocomplete);
      const isAddressInvalid =
        parsedAddress.street.includes('undefined') || !parsedAddress.postalCode;
      if (parsedAddress.state === 'CT') {
        if (!isAddressInvalid) {
          setIsAddressInvalid(false);
        }
        handleFormValueChange({
          target: { name: fieldName, value: parsedAddress },
        });
        return;
      }
      const eventType = formValues?.heaOrHvac === 'HEA' ? '000000' : '000100';
      const isZipCodeValid = await UtilityManager.validateZipCode(
        parsedAddress?.postalCode,
        eventType,
      );
      if (isZipCodeValid) {
        if (!isAddressInvalid) {
          setIsAddressInvalid(false);
        }
        handleFormValueChange({
          target: { name: fieldName, value: parsedAddress },
        });
      }
      if (isAddressInvalid || !isZipCodeValid) {
        raiseError();
        return;
      }
    } catch (error) {
      console.error(error);
      raiseError();
    }
  };

  if (!formValues) return null;
  if (conditional && !Array.isArray(formValues) && !conditional(formValues, perUnitIndex))
    return null;
  // Conditional callback means do we need to conditional render any element
  // It returns boolean so if it true then the element is shown on UI else it will not show.
  // This conditional check is for object schema, schema like Lead Intake 2.0. In which we have
  // one giant object schema. i.e {someKeys: 'someValues', ...}
  // if (conditional && !activeForm && !conditional(formValues, perUnitIndex)) return null;
  // And this conditional check is for Array of objects, Like if our schema is in array based structure
  // Like in Workreceipt 2.0, each index object represents a unit. i.e [{someKeys: 'someValues'}, {someKeys: 'someValues'}, {someKeys: 'someValues'}]
  if (conditional && Array.isArray(formValues) && !conditional(formValues[activeForm])) return null;

  // Handle Dependant Picklists
  if (conditionalOptions) remainingProps.options = conditionalOptions(formValues);

  const FormComponent = componentMap[fieldType];

  if (!FormComponent)
    return (
      <div>
        Skipping field {fieldName}: {fieldType} has not yet been implemented
      </div>
    );

  let value = null;

  // perUnit, refers to values in Array String Structures, Like if we have Electric Provider, and we have 2 units
  // Value for Electric Provider will be ['NG', 'NG']. so that's why we look check for perUnit and then we use
  // perUnitIndex so we can pull value of ElectricProvider[perUnitIndex]
  // Example { electricProvider: ['NG', 'NG'], gasProvider:['Columbia', 'Columbia']}
  // so on UI if 2nd Unit is rendered we will pull value like this formValues[fieldName][perUnitIndex]
  // This check is basically form Object base schema. e.g: Lead Intake 2.0
  if (!['section', 'readonly'].includes(fieldType))
    // These 'field' types don't have 'values' in the form values object since they can't be updated, These are just scripts or labels
    value = perUnit ? formValues[fieldName][perUnitIndex] : formValues[fieldName];

  // if formValues is Array base schema like formValues: [form1,form2]
  if (Array.isArray(formValues) && perUnit) {
    // This check is basically form Array base schema. e.g: WorkReceipt 2.0
    // In Array base schema, a form object is inside Array so that's why
    // we need to go index our formValues and check on fieldName to pull perUnit values.
    // For perUnit value check above block comment.
    value = formValues?.[activeForm]?.[fieldName]?.[perUnitIndex];
  }

  if (Array.isArray(formValues)) {
    // if form is Array Object Base Schema and it's field value is not perUnit, then we only
    // pull from formvalue's object
    value = formValues?.[activeForm]?.[fieldName];
  }

  // If fieldType is time then we need to parse value of time field to make it accurate format like moment(value, 'hh:mm:ss')
  // Otherwise value will be passed as string and Time Picker will get crashed, or we can pass undefined, because moment handle undefined on default
  const checkDateTimeValidity = (format) => {
    if (value) {
      return moment(new Date(value), format);
    }
    return moment(new Date());
  };
  value = fieldType === 'time' ? checkDateTimeValidity('HH:mm:ss') : value;

  value = fieldType === 'date' ? checkDateTimeValidity('YYYY-MM-DD') : value;

  if (['multiselect'].includes(fieldType) && perUnit) {
    value = formValues?.[fieldName]?.[perUnitIndex]
      ? formValues?.[fieldName]?.[perUnitIndex].map((item) => item.value)
      : [];
  }
  return (
    <FormComponent
      value={value}
      onChange={(e) => handleFormValueChange(e, perUnitIndex, perUnit)}
      onPlaceChange={(autocomplete) => handleGoogleLocationInput(autocomplete)}
      name={fieldName}
      text={text}
      isInvalid={isAddressInvalid}
      title={text}
      secondaryText={secondaryText}
      label={text}
      required={conditionalRequire ? conditionalRequire(formValues) : required}
      perUnit={perUnit}
      compact
      lineHeight
      lgFont={fieldType === 'readonly'}
      useSyntheticEvent={useSyntheticEvent}
      uniqueId="address-input"
      {...remainingProps}
    />
  );
};

DataIntakeFormElement.propTypes = {
  name: PropTypes.string,
  type: PropTypes.string,
  text: PropTypes.string,
  required: PropTypes.bool,
  perUnit: PropTypes.bool,
  unitNum: PropTypes.number,
  conditional: PropTypes.func,
  validation: PropTypes.func,
  conditionalRequire: PropTypes.func,
  readOnly: PropTypes.bool,
  conditionalText: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  conditionalSetter: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  conditionalOptions: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  style: PropTypes.oneOfType([PropTypes.shape({}), PropTypes.string]),
  secondaryText: PropTypes.string,
  api: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  useSyntheticEvent: PropTypes.bool,
};

DataIntakeFormElement.defaultProps = {
  name: '',
  type: '',
  text: '',
  required: false,
  perUnit: false,
  unitNum: 1,
  conditional: null,
  validation: null,
  conditionalRequire: null,
  conditionalOptions: false,
  readOnly: false,
  conditionalText: false,
  conditionalSetter: false,
  style: {},
  secondaryText: '',
  api: false,
  useSyntheticEvent: false,
};

export default DataIntakeFormElement;
