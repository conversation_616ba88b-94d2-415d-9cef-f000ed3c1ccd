import React from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme, ThemeProvider } from 'styled-components';
import {
  useRecoilState,
  useSetRecoilState,
  useRecoilCallback,
  // eslint-disable-next-line camelcase
  useRecoilBridgeAcrossReactRoots_UNSTABLE,
} from 'recoil';

import capacityInfoState from '@recoil/agents/capacityInfoState';
import { handleFormFieldChange, FormInput } from '@components/global/Form';
import { SlotsManager } from '@utils/APIManager';
import CellCornerIndicator from './CellCornerIndicator';

const CapacityIndicator = (props) => {
  // RecoilBridge allows the cancelEventConfirmation to share the same recoil state as the rest of our application
  // This is necessary since sweetalert2 creates its own ReactRoot for each modal
  // https://recoiljs.org/docs/api-reference/core/useRecoilBridgeAcrossReactRoots
  const RecoilBridge = useRecoilBridgeAcrossReactRoots_UNSTABLE();
  const setCapacityInfo = useSetRecoilState(capacityInfoState);
  const theme = useTheme();

  const updateCapacity = useRecoilCallback(
    ({ snapshot }) => async () => {
      const capacityInfo = await snapshot.getPromise(capacityInfoState);
      await SlotsManager.updateCapacity(capacityInfo);
    },
    [],
  );

  const { maxAppt, date, oid } = props;

  if (!maxAppt && maxAppt !== 0) return false;

  const handleCapacityModal = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    setCapacityInfo({ maxAppt, date, oid });
    const { value: confirmed } = await fireCapacityModal(RecoilBridge, updateCapacity, theme);
    if (!confirmed) return false;
    return updateCapacity();
  };

  return <CellCornerIndicator onDoubleClick={(e) => handleCapacityModal(e)} value={maxAppt} />;
};

const fireCapacityModal = async (RecoilBridge, updateCapacity, theme) => {
  // Need to lazy load swal config so that the .app-content node exists in dom
  const { createSwalWithTheme } = await import('@config/swalConfig');
  const swal = createSwalWithTheme(theme);

  return swal.fire({
    titleText: 'Update capacity',
    html: (
      <RecoilBridge>
        <CapacityModal theme={theme} />
      </RecoilBridge>
    ),
    confirmButtonText: 'Submit',
    showCancelButton: true,
    cancelButtonText: 'Cancel',
  });
};

const StyledCapacityModal = styled.div``;

const CapacityModal = ({ theme }) => {
  const [capacityInfo, setCapacityInfo] = useRecoilState(capacityInfoState);

  const { maxAppt } = capacityInfo;

  const handleFieldChange = (e, updatedEvent = capacityInfo) => {
    return handleFormFieldChange(e, updatedEvent, setCapacityInfo);
  };

  return (
    <ThemeProvider theme={theme}>
      <StyledCapacityModal>
        <FormInput
          required
          name="maxAppt"
          value={maxAppt}
          type="number"
          max={5}
          min={0}
          title="Max Number of Appointments"
          onChange={handleFieldChange}
        />
      </StyledCapacityModal>
    </ThemeProvider>
  );
};

CapacityIndicator.propTypes = {
  maxAppt: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
  oid: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
};
CapacityIndicator.defaultProps = {
  maxAppt: null,
};
CapacityModal.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  theme: PropTypes.object.isRequired,
};

export default CapacityIndicator;
