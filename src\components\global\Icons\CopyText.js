import React, { useState } from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';

import { Clickable, DropDownMenu } from '@components/global';
import { Copy } from '@styled-icons/boxicons-regular/Copy';
import { copyTextToClipboard } from '@utils/functions';
import Tooltip from '../Tooltip/Tooltip';

const IconButtonContainer = styled(Clickable)`
  position: relative;
  height: 100%;
  background-color: ${({ backgroundColor }) => backgroundColor};
  ${({ isOnInput }) => (isOnInput ? 'border-radius: 0px 4px 4px 0px;' : '')};
  ${({ isOnInput, theme }) => (isOnInput ? `border: 1px solid ${theme.secondary[300]};` : '')}
  ${({ isOnInput }) => (isOnInput ? 'border-left: 0px;' : '')}
`;

const CopyIconStyled = styled(Copy)`
  color: ${({ theme }) => theme.secondary[800]};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: center;
  height: ${({ alignHeightAndWidth }) => (alignHeightAndWidth ? '15%' : '100%')};
  width: ${({ alignHeightAndWidth }) => (alignHeightAndWidth ? '15%' : '100%')};
  ${({ floatRight }) => {
    return floatRight ? 'float: right; margin-right: 20px;' : 'margin: 0 auto;';
  }}
`;

const ConfirmationStyled = styled.div`
  line-height: 225%;
  color: ${({ theme }) => theme.secondary[600]};
  position: absolute;
  left: ${({ alignConfirm, left }) => (alignConfirm ? left : '-60px')};
  top: 0;
  bottom: 0;
`;

const CopyText = ({
  isOnInput,
  copyTextValue,
  hoverText,
  backgroundColor,
  useDropDown = false,
  dealId,
  operationsId,
  payzerId,
  siteId,
  alignConfirm,
  left,
}) => {
  const theme = useTheme();
  const resolvedBackgroundColor = backgroundColor || theme.secondary[100];
  const [isHovering, setIsHovering] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const dropDownList = [];
  if (dealId) dropDownList.push({ text: 'Deal Id', onClick: () => handleCopyText(dealId) });
  if (operationsId)
    dropDownList.push({ text: 'Operations Id', onClick: () => handleCopyText(operationsId) });
  if (payzerId) dropDownList.push({ text: 'Payzer Id', onClick: () => handleCopyText(payzerId) });
  if (siteId) dropDownList.push({ text: 'Site Id', onClick: () => handleCopyText(siteId) });

  const handleCopyText = (hasText = false) => {
    setShowConfirmation(true);
    // Actual Function to copy the text
    // The function will have its independent logic to copy the text
    copyTextToClipboard(hasText || copyTextValue);
    setTimeout(() => {
      setShowConfirmation(false);
    }, 2000);
  };

  return (
    <IconButtonContainer
      isOnInput={isOnInput}
      backgroundColor={resolvedBackgroundColor}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {!useDropDown ? (
        <CopyIconStyled onClick={() => handleCopyText()} />
      ) : (
        <DropDownMenu
          DropDownIcon={<CopyIconStyled floatRight alignHeightAndWidth />}
          listItems={dropDownList}
          marginTopVal="50px"
        />
      )}
      {showConfirmation && (
        <ConfirmationStyled left={left} alignConfirm={alignConfirm}>
          Copied!
        </ConfirmationStyled>
      )}
      {hoverText && isHovering && <Tooltip text={hoverText} />}
    </IconButtonContainer>
  );
};

CopyText.propTypes = {
  copyTextValue: PropTypes.string.isRequired,
  backgroundColor: PropTypes.string,
  hoverText: PropTypes.string,
  isOnInput: PropTypes.bool,
  useDropDown: PropTypes.bool,
  dealId: PropTypes.string,
  operationsId: PropTypes.string,
  payzerId: PropTypes.string,
  siteId: PropTypes.string,
  alignConfirm: PropTypes.bool,
  left: PropTypes.string,
};

CopyText.defaultProps = {
  hoverText: null,
  isOnInput: false,
  useDropDown: false,
  dealId: '',
  operationsId: '',
  payzerId: '',
  siteId: '',
  alignConfirm: false,
  left: '180px',
  backgroundColor: '',
};

export default CopyText;
